{
	"name": "Python 3",
	"build": {
		"dockerfile": "Dockerfile",
		"context": "..",
		"args": {
			"VARIANT": "3.12-bookworm" // Set Python version here
		}
	},
	"customizations": {
		"codespaces": {
			"openFiles": [
				"README.md"
			]
		},
		"vscode": {
			// Set *default* container specific settings.json values on container create.
			"settings": {
				"terminal.integrated.shell.linux": "/bin/bash",
				"python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python",
				"python.linting.enabled": true,
				"python.linting.pylintEnabled": true,
				"python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
				"python.formatting.blackPath": "/usr/local/py-utils/bin/black",
				"python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
				"python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
				"python.linting.flake8Path": "/usr/local/py-utils/bin/flake8",
				"python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
				"python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
				"python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
				"python.linting.pylintPath": "/usr/local/py-utils/bin/pylint",
				"python.linting.pylintArgs": [
					"--disable=C0111"
				]
			},
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"GitHub.github-vscode-theme",
				"ms-python.python",
				"ms-python.vscode-pylance",
				"ms-windows-ai-studio.windows-ai-studio",
				"mathematic.vscode-pdf",
				"alexcvzz.vscode-sqlite",
				"ms-toolsai.jupyter",
				"mechatroner.rainbow-csv"
			]
		}
	},
	"features": {
		"ghcr.io/warrenbuckley/codespace-features/sqlite:latest": {}
	},
	"remoteUser": "vscode",
	// Update welcome text and set terminal prompt to '$ '
	"onCreateCommand": "echo PS1='\"$ \"' >> ~/.bashrc",
	// Pull all branches
	// "postAttachCommand": "git pull --all",
	"postCreateCommand": "sh .devcontainer/startup.sh"
}