# Fundamentals of AI Engineering: Principles and Practical Applications
This is the repository for the LinkedIn Learning course `Fundamentals of AI Engineering: Principles and Practical Applications`. The full course is available from [LinkedIn Learning][lil-course-url].

![course-name-alt-text][lil-thumbnail-url] 

## Course Description

Transform your software engineering skills into AI engineering capabilities with this in-depth, hands-on course. Learn how to build production-ready AI systems, from embedding generation to model deployment. Master practical implementations of vector stores, RAG systems, and hybrid search while gaining expertise in operational aspects like monitoring and CI/CD. Plus, learn to use GitHub Codespaces to build real-world applications that showcase modern AI engineering practices.

## Instructor

<PERSON><PERSON>

CEO and Cofounder of Keru.ai


[0]: # (Replace these placeholder URLs with actual course URLs)

[lil-course-url]: https://www.linkedin.com/learning/fundamentals-of-ai-engineering-principles-and-practical-applications
[lil-thumbnail-url]: https://media.licdn.com/dms/image/v2/D4E0DAQG9ydMOnbtIAA/learning-public-crop_675_1200/B4EZc8E5WaH0Ag-/0/1749059606216?e=2147483647&v=beta&t=ng-obQhpFh8iJ3dx5e6tEKD1P-XS_IFaDmruWPuIIr0

