{"cells": [{"cell_type": "markdown", "id": "a7ae6c59", "metadata": {}, "source": ["# Running Inference Locally\n", "\n", "Large Language Models (LLMs) have revolutionized AI applications, but they don't always need to be accessed through cloud APIs. In this lesson, we'll explore how to download, save, and run LLMs locally in your development environment."]}, {"cell_type": "code", "execution_count": 6, "id": "0ac9f0e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.3 environment at: /media/dsbtek/Data/project/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 5ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install necessary libraries (if not already installed)\n", "!uv pip install transformers "]}, {"cell_type": "markdown", "id": "4f3a6fe5", "metadata": {}, "source": ["Understanding Local LLMs\n", "\n", "Running LLMs locally offers several advantages:\n", "- **Privacy**: Your data doesn't leave your environment\n", "- **Cost**: No per-token API charges\n", "- **Latency**: No network delays\n", "- **Customization**: Full control over model parameters\n", "\n", "However, local LLMs also have limitations:\n", "- **Hardware requirements**: Models need sufficient RAM and GPU\n", "- **Model size**: Smaller models fit locally but may have reduced capabilities\n", "- **Updates**: You manage model versions yourself\n", "\n", "Let's start by downloading a small LLM called DistilGPT2, a distilled version of GPT-2."]}, {"cell_type": "code", "execution_count": 1, "id": "4ae5057c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/dsbtek/Data/project/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading model from Hugging Face Hub...\n", "\n", "Model: distilgpt2\n", "Number of parameters: 81,912,576\n", "Model size on disk: ~312.47 MB (estimated)\n", "\n", "Saving model to ./downloaded_model...\n", "Model and tokenizer saved successfully!\n"]}], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "import os\n", "\n", "# Set the directory where you want to save the model\n", "save_directory = \"./downloaded_model\"  # Change this to your preferred path\n", "\n", "# Create the directory if it doesn't exist\n", "os.makedirs(save_directory, exist_ok=True)\n", "\n", "# Load model and tokenizer\n", "print(\"Downloading model from Hugging Face Hub...\")\n", "model_name = \"distilgpt2\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name)\n", "\n", "# Print model information\n", "print(f\"\\nModel: {model_name}\")\n", "print(f\"Number of parameters: {model.num_parameters():,}\")\n", "print(\n", "    f\"Model size on disk: ~{model.num_parameters() * 4 / (1024 * 1024):.2f} MB (estimated)\")\n", "\n", "# Save the model and tokenizer to the specified directory\n", "print(f\"\\nSaving model to {save_directory}...\")\n", "model.save_pretrained(save_directory)\n", "tokenizer.save_pretrained(save_directory)\n", "print(\"Model and tokenizer saved successfully!\")"]}, {"cell_type": "markdown", "id": "2eb5d342", "metadata": {}, "source": ["## Loading and Using a Local Model\n", "\n", "Once saved, we can load the model from local storage instead of downloading it again. This is especially useful for larger models or when working in environments with limited internet access."]}, {"cell_type": "code", "execution_count": 2, "id": "c411dc73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading model from local directory...\n", "Model loaded from local directory!\n", "\n", "Using device: cpu\n"]}], "source": ["# Now we can load from local directory instead of downloading again\n", "print(\"Loading model from local directory...\")\n", "local_model = AutoModelForCausalLM.from_pretrained(save_directory)\n", "local_tokenizer = AutoTokenizer.from_pretrained(save_directory)\n", "local_tokenizer.pad_token = local_tokenizer.eos_token\n", "print(\"Model loaded from local directory!\")\n", "\n", "# Codespace is mostly run on cpus, so we're going to use a CPU\n", "device = \"cpu\"\n", "print(f\"\\nUsing device: {device}\")"]}, {"cell_type": "markdown", "id": "d95ac5b2", "metadata": {}, "source": ["## Generating Text with Your Local LLM\n", "\n", "Now let's create a more versatile text generation function that allows us to control various parameters:\n", "\n", "- **Temperature**: Controls randomness (higher = more creative, lower = more deterministic)\n", "- **Max length**: The maximum number of tokens to generate\n", "- **Top-p (nucleus sampling)**: Limits token selection to a subset of most likely tokens\n", "- **Top-k**: Limits selection to the k most likely tokens"]}, {"cell_type": "code", "execution_count": 3, "id": "bbe6b318", "metadata": {}, "outputs": [], "source": ["def generate_text(prompt,\n", "                  max_length=50,\n", "                  temperature=0.8,\n", "                  top_p=0.9,\n", "                  top_k=50,\n", "                  do_sample=True):\n", "    \"\"\"Generate text from a prompt with customizable parameters\n", "    \n", "    Args:\n", "        prompt (str): The input text to continue\n", "        max_length (int): Maximum length of generated text (including prompt)\n", "        temperature (float): Higher values (>1.0) increase randomness, lower values (<1.0) make it more deterministic\n", "        top_p (float): Nucleus sampling parameter (0-1.0)\n", "        top_k (int): Limits selection to k most likely tokens\n", "        do_sample (bool): If False, uses greedy decoding instead of sampling\n", "        \n", "    Returns:\n", "        str: The generated text including the prompt\n", "    \"\"\"\n", "    # Prepare the inputs\n", "    inputs = local_tokenizer(prompt, return_tensors=\"pt\",\n", "                             return_attention_mask=True).to(device)\n", "\n", "    # Generate text\n", "    output = local_model.generate(\n", "        input_ids=inputs.input_ids,\n", "        attention_mask=inputs.attention_mask,\n", "        max_length=max_length,\n", "        do_sample=do_sample,\n", "        temperature=temperature,\n", "        top_p=top_p,\n", "        top_k=top_k,\n", "        pad_token_id=local_tokenizer.pad_token_id\n", "    )\n", "\n", "    # Decode the output\n", "    generated_text = local_tokenizer.decode(\n", "        output[0], skip_special_tokens=True)\n", "    \n", "    # Clean up excess whitespace with regex\n", "    import re\n", "    cleaned_text = re.sub(r'\\s+', ' ', generated_text)\n", "\n", "    return cleaned_text"]}, {"cell_type": "markdown", "id": "4c5c9b3f", "metadata": {}, "source": ["## Experimenting with Different Generation Parameters\n", "\n", "Let's try generating text with different parameters to see how they affect the output:"]}, {"cell_type": "code", "execution_count": 4, "id": "9d410e13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example 1: Default parameters (temperature=0.8)\n", "Prompt: \"Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class\"\n", "Generated: Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class is free for anyone with a degree in robotics, or you can take advantage of this free course to learn how to use AI in a variety of other fields. \n", "----------------------------------------------------------------------------------------------------\n", "\n", "Example 2: Low temperature (more deterministic)\n", "Prompt: \"Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class\"\n", "Generated: Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class is free and open to all.\n", "----------------------------------------------------------------------------------------------------\n", "\n", "Example 3: High temperature (more creative/random)\n", "Prompt: \"Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class\"\n", "Generated: Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class covers topics ranging from theoretical science to mathematics. We hope you'll be a great teacher on this new project for anyone learning at home. More info! The best way to learn new skills is to come back in person after each new session (it seems most people are already in their 30\n", "----------------------------------------------------------------------------------------------------\n", "\n", "Example 4: Greedy decoding (no sampling, always selects most likely token)\n", "Prompt: \"Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class\"\n", "Generated: Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class is free and open to all. \n"]}], "source": ["prompt = \"Welcome to Fundamentals of AI Engineering on LinkedIn Learning. This class\"\n", "\n", "print(\"Example 1: Default parameters (temperature=0.8)\")\n", "print(f\"Prompt: \\\"{prompt}\\\"\")\n", "print(f\"Generated: {generate_text(prompt, max_length=75)}\")\n", "print(\"-\"*100 + \"\\n\")\n", "print(\"Example 2: Low temperature (more deterministic)\")\n", "print(f\"Prompt: \\\"{prompt}\\\"\")\n", "print(f\"Generated: {generate_text(prompt, temperature=0.2, max_length=75)}\")\n", "print(\"-\"*100 + \"\\n\")\n", "print(\"Example 3: High temperature (more creative/random)\")\n", "print(f\"Prompt: \\\"{prompt}\\\"\")\n", "print(f\"Generated: {generate_text(prompt, temperature=1.5, max_length=75)}\")\n", "print(\"-\"*100 + \"\\n\")\n", "print(\"Example 4: Greedy decoding (no sampling, always selects most likely token)\")\n", "print(f\"Prompt: \\\"{prompt}\\\"\")\n", "print(f\"Generated: {generate_text(prompt, top_p=None, temperature=None, do_sample=False, max_length=75)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "8cd59bc7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}