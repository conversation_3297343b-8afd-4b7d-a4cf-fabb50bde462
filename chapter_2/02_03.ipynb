{"cells": [{"cell_type": "markdown", "id": "2e10637d", "metadata": {}, "source": ["# Deconstructing Local LLMs\n", "\n", "When we download an LLM for local use, it comes with several essential components that work together to make text generation possible. In this lesson, we'll explore these components, understand what they do, and learn how they fit together."]}, {"cell_type": "code", "execution_count": 1, "id": "2d7ac0ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading distilgpt2...\n", "Saving model to ./downloaded_model...\n", "Model and tokenizer saved successfully!\n"]}], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "import os\n", "import json\n", "import torch\n", "from safetensors import safe_open\n", "\n", "# Set the directory where we'll save the model\n", "save_directory = \"./downloaded_model\"  \n", "os.makedirs(save_directory, exist_ok=True)\n", "\n", "# Download a small model\n", "model_name = \"distilgpt2\"\n", "print(f\"Downloading {model_name}...\")\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name)\n", "\n", "# Save the model to our local directory\n", "print(f\"Saving model to {save_directory}...\")\n", "model.save_pretrained(save_directory)\n", "tokenizer.save_pretrained(save_directory)\n", "print(\"Model and tokenizer saved successfully!\")"]}, {"cell_type": "markdown", "id": "e1d244ca", "metadata": {}, "source": ["## 1. Exploring the Model Files\n", "\n", "Let's see what files were created when we downloaded the model:"]}, {"cell_type": "code", "execution_count": 2, "id": "2e21c314", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files in the model directory:\n", "- config.json (0.00093 MB)\n", "- generation_config.json (0.00011 MB)\n", "- merges.txt (0.43518 MB)\n", "- model.safetensors (312.47895 MB)\n", "- special_tokens_map.json (0.00009 MB)\n", "- tokenizer.json (3.39287 MB)\n", "- tokenizer_config.json (0.00045 MB)\n", "- vocab.json (0.76118 MB)\n"]}], "source": ["# List all files in the model directory\n", "files = os.listdir(save_directory)\n", "print(\"Files in the model directory:\")\n", "for file in sorted(files):\n", "    # Get file size in MB\n", "    file_path = os.path.join(save_directory, file)\n", "    file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB\n", "    print(f\"- {file} ({file_size:.5f} MB)\")"]}, {"cell_type": "markdown", "id": "b46e1914", "metadata": {}, "source": ["## Understanding the Key Components\n", "\n", "The files we see in the model directory can be grouped into these categories:\n", "\n", "1. **Model Configuration**\n", "   - `config.json` - Contains model architecture and hyperparameters\n", "\n", "2. **Model Weights**\n", "   - `pytorch_model.bin` or `model.safetensors` - The actual trained parameters\n", "\n", "3. **Tokenizer Components**\n", "   - `tokenizer_config.json` - Tokenizer settings\n", "   - `vocab.json` - The vocabulary mapping tokens to IDs\n", "   - `merges.txt` - Byte-Pair Encoding (BPE) merges for subword tokenization\n", "   - `special_tokens_map.json` - Defines special tokens like [PAD], [CLS], etc.\n", "\n", "Let's examine each of these components in detail."]}, {"cell_type": "markdown", "id": "2b52d08f", "metadata": {}, "source": ["## Model Configuration (config.json)\n", "\n", "The `config.json` file contains essential information about the model architecture and hyperparameters. This tells the framework how to construct the model's neural network layers.\n", "\n", "Understanding these parameters:\n", "- model_type: The architecture family (e.g., GPT-2)\n", "- vocab_size: Number of tokens in the vocabulary\n", "- n_positions: Maximum sequence length the model can handle\n", "- n_embd: Dimension of embeddings and hidden layers\n", "- n_layer: Number of transformer layers/blocks\n", "- n_head: Number of attention heads in each layer\n", "- activation_function: Non-linearity used (e.g., gelu, relu)\n", "- *_pdrop: Dropout probabilities for different components"]}, {"cell_type": "code", "execution_count": 9, "id": "09b890c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Key model configuration parameters:\n", "- model_type: gpt2\n", "- vocab_size: 50257\n", "- n_positions: 1024\n", "- n_embd: 768\n", "- n_layer: 6\n", "- n_head: 12\n", "- activation_function: gelu_new\n", "- resid_pdrop: 0.1\n", "- embd_pdrop: 0.1\n", "- attn_pdrop: 0.1\n"]}], "source": ["# Load and examine the config.json file\n", "config_path = os.path.join(save_directory, \"config.json\")\n", "with open(config_path, \"r\") as f:\n", "    config = json.load(f)\n", "\n", "# Let's see what's in the config\n", "print(\"Key model configuration parameters:\")\n", "important_params = [\n", "    \"model_type\", \"vocab_size\", \"n_positions\", \"n_embd\", \"n_layer\", \"n_head\", \n", "    \"activation_function\", \"resid_pdrop\", \"embd_pdrop\", \"attn_pdrop\"\n", "]\n", "for param in important_params:\n", "    if param in config:\n", "        print(f\"- {param}: {config[param]}\")"]}, {"cell_type": "markdown", "id": "5258d386", "metadata": {}, "source": ["## Model Weights File\n", "\n", "The model weights might be stored in one of these formats:\n", "- `pytorch_model.bin` - PyTorch's native format\n", "- `model.safetensors` - A newer, safer format for storing tensors\n", "\n", "These files contain the actual trained parameters of the model. Let's examine what's inside:"]}, {"cell_type": "code", "execution_count": 4, "id": "b6eb1d49", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found weights file: model.safetensors\n", "\n", "Model contains these weight matrices:\n", "Layer Name                                         Shape           Preview\n", "--------------------------------------------------------------------------------\n", "transformer.h.0.attn.c_attn.bias                   torch.Size([2304]) [0.4693034589290619, -0.4959352910518646, -0.4157843589782715]...\n", "transformer.h.0.attn.c_attn.weight                 torch.Size([768, 2304]) [-0.4988037049770355, -0.19897758960723877, -0.1046222522854805]...\n", "transformer.h.0.attn.c_proj.bias                   torch.<PERSON><PERSON>([768]) [0.16174378991127014, -0.16444097459316254, -0.15611258149147034]...\n", "transformer.h.0.attn.c_proj.weight                 torch.Size([768, 768]) [0.25814932584762573, -0.16598303616046906, 0.062477629631757736]...\n", "transformer.h.0.ln_1.bias                          torch.Size([768]) [0.00478767603635788, 0.01292799785733223, -0.018999796360731125]...\n", "transformer.h.0.ln_1.weight                        torch.Size([768]) [0.21948328614234924, 0.18534228205680847, 0.15715038776397705]...\n", "transformer.h.0.ln_2.bias                          torch.Size([768]) [0.03851698711514473, 0.05805707350373268, 0.013325878418982029]...\n", "transformer.h.0.ln_2.weight                        torch.Size([768]) [0.13420014083385468, 0.21758565306663513, 0.20979763567447662]...\n", "transformer.h.0.mlp.c_fc.bias                      torch.Size([3072]) [0.04582929611206055, -0.08486124128103256, -0.13325951993465424]...\n", "transformer.h.0.mlp.c_fc.weight                    torch.Size([768, 3072]) [0.12394002079963684, 0.10340151190757751, -0.00964804645627737]...\n"]}], "source": ["# Find the weights file\n", "weights_file = None\n", "for file in files:\n", "    if file.endswith(\".bin\") or file.endswith(\".safetensors\"):\n", "        weights_file = file\n", "        break\n", "\n", "if weights_file:\n", "    print(f\"Found weights file: {weights_file}\")\n", "\n", "    if weights_file.endswith(\".bin\"):\n", "        weights_path = os.path.join(save_directory, weights_file)\n", "        state_dict = torch.load(weights_path)\n", "\n", "        print(\"\\nModel contains these weight matrices:\")\n", "        print(f\"{'Layer Name':<50} {'Shape':<15} {'Preview'}\")\n", "        print(\"-\" * 80)\n", "\n", "        for i, (name, tensor) in enumerate(list(state_dict.items())[:10]):\n", "            # Get first 3 values as preview\n", "            preview = tensor.flatten()[:3].tolist()\n", "            print(f\"{name:<50} {str(tensor.shape):<15} {preview}...\")\n", "\n", "    elif weights_file.endswith(\".safetensors\"):\n", "        try:\n", "            weights_path = os.path.join(save_directory, weights_file)\n", "            with safe_open(weights_path, framework=\"pt\") as f:\n", "                tensor_names = list(f.keys())[:10]\n", "\n", "                print(\"\\nModel contains these weight matrices:\")\n", "                print(f\"{'Layer Name':<50} {'Shape':<15} {'Preview'}\")\n", "                print(\"-\" * 80)\n", "\n", "                for name in tensor_names:\n", "                    tensor = f.get_tensor(name)\n", "                    preview = tensor.flatten()[:3].tolist()\n", "                    print(f\"{name:<50} {str(tensor.shape):<15} {preview}...\")\n", "        except ImportError:\n", "            print(\"safetensors library not installed. Run: pip install safetensors\")\n", "\n", "else:\n", "    print(\"No weights file found\")"]}, {"cell_type": "markdown", "id": "4fb9c87d", "metadata": {}, "source": ["## Tokenizer Components\n", "\n", "The tokenizer is responsible for converting text into token IDs that the model can process. Let's examine its components.\n", "\n", "Config Info:\n", "- model_max_length: Maximum sequence length the tokenizer will handle\n", "- bos_token, eos_token, etc.: Special tokens for different purposes"]}, {"cell_type": "code", "execution_count": 5, "id": "ff8f11a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizer Configuration:\n", "- add_prefix_space: False\n", "- added_tokens_decoder: {'50256': {'content': '<|endoftext|>', 'lstrip': False, 'normalized': True, 'rstrip': False, 'single_word': False, 'special': True}}\n", "- bos_token: <|endoftext|>\n", "- clean_up_tokenization_spaces: False\n", "- eos_token: <|endoftext|>\n", "- extra_special_tokens: {}\n", "- model_max_length: 1024\n", "- tokenizer_class: GPT2Tokenizer\n", "- unk_token: <|endoftext|>\n"]}], "source": ["# Examine tokenizer_config.json\n", "tokenizer_config_path = os.path.join(save_directory, \"tokenizer_config.json\")\n", "if os.path.exists(tokenizer_config_path):\n", "    with open(tokenizer_config_path, \"r\") as f:\n", "        tokenizer_config = json.load(f)\n", "\n", "    print(\"Tokenizer Configuration:\")\n", "    for key, value in tokenizer_config.items():\n", "        print(f\"- {key}: {value}\")\n", "else:\n", "    print(\"No tokenizer_config.json found\")"]}, {"cell_type": "code", "execution_count": 6, "id": "b7b0d900", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vocabulary size: 50257 tokens\n", "\n", "Sample tokens (first 20):\n", "    0: '!'\n", "    1: '\"'\n", "    2: '#'\n", "    3: '$'\n", "    4: '%'\n", "    5: '&'\n", "    6: \"'\"\n", "    7: '('\n", "    8: ')'\n", "    9: '*'\n", "   10: '+'\n", "   11: ','\n", "   12: '-'\n", "   13: '.'\n", "   14: '/'\n", "   15: '0'\n", "   16: '1'\n", "   17: '2'\n", "   18: '3'\n", "   19: '4'\n", "\n", "Some interesting tokens:\n", "31373: 'hello'\n", " 6894: 'world'\n", "20185: 'AI'\n", "19849: 'model'\n", "\n", "Special tokens:\n", "50256: '<|endoftext|>'\n"]}], "source": ["# Examine vocab.json\n", "vocab_path = os.path.join(save_directory, \"vocab.json\")\n", "if os.path.exists(vocab_path):\n", "    with open(vocab_path, \"r\") as f:\n", "        vocab = json.load(f)\n", "\n", "    print(f\"Vocabulary size: {len(vocab)} tokens\")\n", "\n", "    # Show the first 20 tokens\n", "    print(\"\\nSample tokens (first 20):\")\n", "    for i, (token, token_id) in enumerate(list(vocab.items())[:20]):\n", "        print(f\"{token_id:5d}: {repr(token)}\")\n", "\n", "    # Show some interesting tokens\n", "    print(\"\\nSome interesting tokens:\")\n", "    interesting_tokens = [\"hello\", \"world\", \"programming\", \"AI\", \"model\"]\n", "    for token in interesting_tokens:\n", "        if token in vocab:\n", "            print(f\"{vocab[token]:5d}: {repr(token)}\")\n", "\n", "    # Show some special tokens\n", "    print(\"\\nSpecial tokens:\")\n", "    special_tokens = [\"<|endoftext|>\", \"<|pad|>\", \"<|mask|>\"]\n", "    for token in special_tokens:\n", "        if token in vocab:\n", "            print(f\"{vocab[token]:5d}: {repr(token)}\")\n", "else:\n", "    print(\"No vocab.json found\")"]}, {"cell_type": "code", "execution_count": 7, "id": "eb48526c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of BPE merges: 50001\n", "\n", "First 10 BPE merges:\n", "1: #version: 0.2\n", "2: Ġ t\n", "3: Ġ a\n", "4: h e\n", "5: i n\n", "6: r e\n", "7: o n\n", "8: Ġt he\n", "9: e r\n", "10: Ġ s\n", "\n", "Understanding BPE merges:\n", "- Each line shows two tokens that get merged into one\n", "- The merges are applied in order during tokenization\n", "- This enables the model to handle unknown words by breaking them into subwords\n"]}], "source": ["# Examine merges.txt (BPE merges)\n", "merges_path = os.path.join(save_directory, \"merges.txt\")\n", "if os.path.exists(merges_path):\n", "    with open(merges_path, \"r\", encoding=\"utf-8\") as f:\n", "        merges = f.readlines()\n", "\n", "    print(f\"Number of BPE merges: {len(merges)}\")\n", "\n", "    # Show the first few merges\n", "    print(\"\\nFirst 10 BPE merges:\")\n", "    for i, merge in enumerate(merges[:10]):\n", "        print(f\"{i+1}: {merge.strip()}\")\n", "\n", "    print(\"\\nUnderstanding BPE merges:\")\n", "    print(\"- Each line shows two tokens that get merged into one\")\n", "    print(\"- The merges are applied in order during tokenization\")\n", "    print(\"- This enables the model to handle unknown words by breaking them into subwords\")\n", "else:\n", "    print(\"No merges.txt found\")"]}, {"cell_type": "markdown", "id": "e2e4d148", "metadata": {}, "source": ["## Tokenization in Action\n", "\n", "Let's see how the tokenizer works with a real example:"]}, {"cell_type": "code", "execution_count": 8, "id": "f2f03f83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original text: The quick brown fox jumps over the lazy dog. This is an example of tokenization in NLP.\n", "\n", "Tokenized into 21 tokens:\n", "['The', 'Ġquick', 'Ġbrown', 'Ġfox', 'Ġjumps', 'Ġover', 'Ġthe', 'Ġlazy', 'Ġdog', '.', 'ĠThis', 'Ġis', 'Ġan', 'Ġexample', 'Ġof', 'Ġtoken', 'ization', 'Ġin', 'ĠN', 'LP', '.']\n", "\n", "Converted to 21 token IDs:\n", "[464, 2068, 7586, 21831, 18045, 625, 262, 16931, 3290, 13, 770, 318, 281, 1672, 286, 11241, 1634, 287, 399, 19930, 13]\n", "\n", "Token to ID mapping:\n", "The             → 464\n", "Ġquick          → 2068\n", "Ġbrown          → 7586\n", "Ġfox            → 21831\n", "Ġjumps          → 18045\n", "Ġover           → 625\n", "Ġthe            → 262\n", "Ġlazy           → 16931\n", "Ġdog            → 3290\n", ".               → 13\n", "ĠThis           → 770\n", "Ġis             → 318\n", "Ġan             → 281\n", "Ġexample        → 1672\n", "Ġof             → 286\n", "Ġtoken          → 11241\n", "ization         → 1634\n", "Ġin             → 287\n", "ĠN              → 399\n", "LP              → 19930\n", ".               → 13\n", "\n", "Decoded text: The quick brown fox jumps over the lazy dog. This is an example of tokenization in NLP.\n"]}], "source": ["# Reload the tokenizer to ensure we're using the local files\n", "local_tokenizer = AutoTokenizer.from_pretrained(save_directory)\n", "\n", "# Define a sample text\n", "sample_text = \"The quick brown fox jumps over the lazy dog. This is an example of tokenization in NLP.\"\n", "\n", "# Tokenize the text\n", "tokens = local_tokenizer.tokenize(sample_text)\n", "token_ids = local_tokenizer.encode(sample_text)\n", "\n", "# Display the results\n", "print(f\"Original text: {sample_text}\")\n", "print(f\"\\nTokenized into {len(tokens)} tokens:\")\n", "print(tokens)\n", "\n", "print(f\"\\nConverted to {len(token_ids)} token IDs:\")\n", "print(token_ids)\n", "\n", "# Show token to ID mapping\n", "print(\"\\nToken to ID mapping:\")\n", "for token, id in zip(tokens, token_ids[:-1] if token_ids[-1] == local_tokenizer.eos_token_id else token_ids):\n", "    print(f\"{token:15} → {id}\")\n", "\n", "# Decode back to text\n", "decoded_text = local_tokenizer.decode(token_ids)\n", "print(f\"\\nDecoded text: {decoded_text}\")"]}, {"cell_type": "markdown", "id": "8752ac25", "metadata": {}, "source": ["# Conclusion\n", "\n", "\n", "### Model Architecture and Configuration\n", "\n", "- **config.json**: Defines the neural network architecture and hyperparameters (layers, attention heads, dimensions).\n", "generation_config.json: Contains default parameters for text generation (temperature, top_p, max length).\n", "\n", "- **generation_config.json**: Contains default parameters for text generation (temperature, top_p, max length).\n", "\n", "### Model Weights\n", "\n", "- **model.safetensors**: Contains all trained neural network weights - the actual learned parameters.\n", "\n", "### Tokenizer Components\n", "\n", "- **vocab.json**: Maps text tokens to their corresponding IDs in the model's vocabulary.\n", "- **merges.txt**: Contains the BPE merge rules that determine how characters combine into subword tokens.\n", "- **tokenizer.json**: Optimized version combining vocabulary and merge rules for faster processing.\n", "- **tokenizer_config.json**: Settings for the tokenizer like special token handling and padding.\n", "- **special_tokens_map.json**: Defines special tokens like [PAD], [BOS], [EOS] that have specific functions."]}, {"cell_type": "markdown", "id": "bd61b43b", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}