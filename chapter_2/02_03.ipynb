from transformers import AutoModelForCausalLM, AutoTokenizer
import os
import json
import torch
from safetensors import safe_open

# Set the directory where we'll save the model
save_directory = "./downloaded_model"  
os.makedirs(save_directory, exist_ok=True)

# Download a small model
model_name = "distilgpt2"
print(f"Downloading {model_name}...")
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# Save the model to our local directory
print(f"Saving model to {save_directory}...")
model.save_pretrained(save_directory)
tokenizer.save_pretrained(save_directory)
print("Model and tokenizer saved successfully!")

# List all files in the model directory
files = os.listdir(save_directory)
print("Files in the model directory:")
for file in sorted(files):
    # Get file size in MB
    file_path = os.path.join(save_directory, file)
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
    print(f"- {file} ({file_size:.5f} MB)")

# Load and examine the config.json file
config_path = os.path.join(save_directory, "config.json")
with open(config_path, "r") as f:
    config = json.load(f)

# Let's see what's in the config
print("Key model configuration parameters:")
important_params = [
    "model_type", "vocab_size", "n_positions", "n_embd", "n_layer", "n_head", 
    "activation_function", "resid_pdrop", "embd_pdrop", "attn_pdrop"
]
for param in important_params:
    if param in config:
        print(f"- {param}: {config[param]}")

# Find the weights file
weights_file = None
for file in files:
    if file.endswith(".bin") or file.endswith(".safetensors"):
        weights_file = file
        break

if weights_file:
    print(f"Found weights file: {weights_file}")

    if weights_file.endswith(".bin"):
        weights_path = os.path.join(save_directory, weights_file)
        state_dict = torch.load(weights_path)

        print("\nModel contains these weight matrices:")
        print(f"{'Layer Name':<50} {'Shape':<15} {'Preview'}")
        print("-" * 80)

        for i, (name, tensor) in enumerate(list(state_dict.items())[:10]):
            # Get first 3 values as preview
            preview = tensor.flatten()[:3].tolist()
            print(f"{name:<50} {str(tensor.shape):<15} {preview}...")

    elif weights_file.endswith(".safetensors"):
        try:
            weights_path = os.path.join(save_directory, weights_file)
            with safe_open(weights_path, framework="pt") as f:
                tensor_names = list(f.keys())[:10]

                print("\nModel contains these weight matrices:")
                print(f"{'Layer Name':<50} {'Shape':<15} {'Preview'}")
                print("-" * 80)

                for name in tensor_names:
                    tensor = f.get_tensor(name)
                    preview = tensor.flatten()[:3].tolist()
                    print(f"{name:<50} {str(tensor.shape):<15} {preview}...")
        except ImportError:
            print("safetensors library not installed. Run: pip install safetensors")

else:
    print("No weights file found")

# Examine tokenizer_config.json
tokenizer_config_path = os.path.join(save_directory, "tokenizer_config.json")
if os.path.exists(tokenizer_config_path):
    with open(tokenizer_config_path, "r") as f:
        tokenizer_config = json.load(f)

    print("Tokenizer Configuration:")
    for key, value in tokenizer_config.items():
        print(f"- {key}: {value}")
else:
    print("No tokenizer_config.json found")

# Examine vocab.json
vocab_path = os.path.join(save_directory, "vocab.json")
if os.path.exists(vocab_path):
    with open(vocab_path, "r") as f:
        vocab = json.load(f)

    print(f"Vocabulary size: {len(vocab)} tokens")

    # Show the first 20 tokens
    print("\nSample tokens (first 20):")
    for i, (token, token_id) in enumerate(list(vocab.items())[:20]):
        print(f"{token_id:5d}: {repr(token)}")

    # Show some interesting tokens
    print("\nSome interesting tokens:")
    interesting_tokens = ["hello", "world", "programming", "AI", "model"]
    for token in interesting_tokens:
        if token in vocab:
            print(f"{vocab[token]:5d}: {repr(token)}")

    # Show some special tokens
    print("\nSpecial tokens:")
    special_tokens = ["<|endoftext|>", "<|pad|>", "<|mask|>"]
    for token in special_tokens:
        if token in vocab:
            print(f"{vocab[token]:5d}: {repr(token)}")
else:
    print("No vocab.json found")

# Examine merges.txt (BPE merges)
merges_path = os.path.join(save_directory, "merges.txt")
if os.path.exists(merges_path):
    with open(merges_path, "r", encoding="utf-8") as f:
        merges = f.readlines()

    print(f"Number of BPE merges: {len(merges)}")

    # Show the first few merges
    print("\nFirst 10 BPE merges:")
    for i, merge in enumerate(merges[:10]):
        print(f"{i+1}: {merge.strip()}")

    print("\nUnderstanding BPE merges:")
    print("- Each line shows two tokens that get merged into one")
    print("- The merges are applied in order during tokenization")
    print("- This enables the model to handle unknown words by breaking them into subwords")
else:
    print("No merges.txt found")

# Reload the tokenizer to ensure we're using the local files
local_tokenizer = AutoTokenizer.from_pretrained(save_directory)

# Define a sample text
sample_text = "The quick brown fox jumps over the lazy dog. This is an example of tokenization in NLP."

# Tokenize the text
tokens = local_tokenizer.tokenize(sample_text)
token_ids = local_tokenizer.encode(sample_text)

# Display the results
print(f"Original text: {sample_text}")
print(f"\nTokenized into {len(tokens)} tokens:")
print(tokens)

print(f"\nConverted to {len(token_ids)} token IDs:")
print(token_ids)

# Show token to ID mapping
print("\nToken to ID mapping:")
for token, id in zip(tokens, token_ids[:-1] if token_ids[-1] == local_tokenizer.eos_token_id else token_ids):
    print(f"{token:15} → {id}")

# Decode back to text
decoded_text = local_tokenizer.decode(token_ids)
print(f"\nDecoded text: {decoded_text}")