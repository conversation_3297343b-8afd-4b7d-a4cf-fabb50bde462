{"cells": [{"cell_type": "markdown", "id": "intro-md", "metadata": {}, "source": ["# Putting the LLM Pipeline Together: Step by Step\n", "\n", "In this notebook, we'll walk through the complete process of text generation with a local LLM, keeping things simple and clear. We'll follow these steps:\n", "\n", "Input text → Tokenization → Converting to IDs → Model processing → Next token prediction → Token selection → Building the response"]}, {"cell_type": "markdown", "id": "9a4ca502", "metadata": {}, "source": ["Let's begin by loading our local model:"]}, {"cell_type": "code", "execution_count": 1, "id": "load-model", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading distilgpt2...\n", "Saving model to ./downloaded_model...\n", "Model and tokenizer saved successfully!\n", "Model and tokenizer loaded successfully!\n"]}], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer\n", "import torch\n", "import numpy as np\n", "import os\n", "\n", "# Set the directory where we'll save the model\n", "save_directory = \"./downloaded_model\"\n", "os.makedirs(save_directory, exist_ok=True)\n", "\n", "# Download a small model\n", "model_name = \"distilgpt2\"\n", "print(f\"Downloading {model_name}...\")\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "model = AutoModelForCausalLM.from_pretrained(model_name)\n", "\n", "# Save the model to our local directory\n", "print(f\"Saving model to {save_directory}...\")\n", "model.save_pretrained(save_directory)\n", "tokenizer.save_pretrained(save_directory)\n", "print(\"Model and tokenizer saved successfully!\")\n", "\n", "# Load model and tokenizer from our local directory\n", "model = AutoModelForCausalLM.from_pretrained(save_directory)\n", "tokenizer = AutoTokenizer.from_pretrained(save_directory)\n", "tokenizer.pad_token = tokenizer.eos_token\n", "\n", "print(\"Model and tokenizer loaded successfully!\")"]}, {"cell_type": "markdown", "id": "step1-md", "metadata": {}, "source": ["## Step 1: Input Text\n", "\n", "Let's begin with a simple prompt:"]}, {"cell_type": "code", "execution_count": 2, "id": "input-text", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Prompt: Artificial intelligence is transforming\n"]}], "source": ["# Our starting prompt\n", "prompt = \"Artificial intelligence is transforming\"\n", "print(f\"Input Prompt: {prompt}\")"]}, {"cell_type": "markdown", "id": "step2-md", "metadata": {}, "source": ["## Step 2: Tokenization - Breaking Text into Pieces\n", "\n", "The tokenizer breaks our text into smaller units (tokens) that the model can understand:"]}, {"cell_type": "code", "execution_count": 3, "id": "tokenization", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenization Result:\n", "Token 1: 'Art'\n", "Token 2: 'ificial'\n", "Token 3: 'Ġintelligence'\n", "Token 4: 'Ġis'\n", "Token 5: 'Ġtransforming'\n"]}], "source": ["# Tokenize the input\n", "tokens = tokenizer.tokenize(prompt)\n", "\n", "print(\"Tokenization Result:\")\n", "for i, token in enumerate(tokens):\n", "    print(f\"Token {i+1}: '{token}'\")"]}, {"cell_type": "markdown", "id": "tokenization-explanation", "metadata": {}, "source": ["### What's happening here?\n", "\n", "The tokenizer has split our input text into tokens. Notice a few important things:\n", "\n", "- Some tokens have a 'Ġ' prefix - this represents a space before the word\n", "- The word \"transforming\" is kept as a single token because it's common enough\n", "- If we used a less common word, it might be split into multiple subword tokens"]}, {"cell_type": "markdown", "id": "step3-md", "metadata": {}, "source": ["## Step 3: Converting Tokens to IDs\n", "\n", "Next, each token is converted to its corresponding numeric ID from the vocabulary:"]}, {"cell_type": "code", "execution_count": 4, "id": "token-to-id", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokens to IDs Conversion:\n", "Token 'Art' → ID: 8001\n", "Token 'ificial' → ID: 9542\n", "Token 'Ġintelligence' → ID: 4430\n", "Token 'Ġis' → ID: 318\n", "Token 'Ġtransforming' → ID: 25449\n", "\n", "Model input tensor shape: torch.Size([1, 5])\n", "Model input tensor: tensor([[ 8001,  9542,  4430,   318, 25449]])\n"]}], "source": ["# Convert tokens to IDs\n", "input_ids = tokenizer.encode(prompt, return_tensors=\"pt\")[0].tolist()\n", "\n", "print(\"Tokens to IDs Conversion:\")\n", "for token, id_value in zip(tokens, input_ids):\n", "    print(f\"Token '{token}' → ID: {id_value}\")\n", "\n", "# Show the tensor format that will be input to the model\n", "model_input_ids = tokenizer.encode(prompt, return_tensors=\"pt\")\n", "print(\"\\nModel input tensor shape:\", model_input_ids.shape)\n", "print(\"Model input tensor:\", model_input_ids)"]}, {"cell_type": "markdown", "id": "ids-explanation", "metadata": {}, "source": ["### What's happening here?\n", "\n", "Each token has been converted to a numeric ID according to the model's vocabulary. These IDs are what the model actually processes - it doesn't understand the text directly, only these numbers.\n", "\n", "The IDs are then formatted as a PyTorch tensor with shape [1, n_tokens] - this is the actual input format the model expects."]}, {"cell_type": "markdown", "id": "step4-md", "metadata": {}, "source": ["## Step 4: Model Processing\n", "\n", "Now the model processes these IDs through its neural network layers:"]}, {"cell_type": "code", "execution_count": 5, "id": "model-processing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output logits shape: torch.Size([1, 5, 50257])\n", "This means we have predictions for 5 positions\n", "For each position, we have scores for all 50257 tokens in the vocabulary\n"]}], "source": ["# Run the model on our input\n", "with torch.no_grad():  # Disable gradient calculation for inference\n", "    outputs = model(model_input_ids)\n", "\n", "# The model outputs logits (unnormalized probabilities) for each possible next token\n", "logits = outputs.logits\n", "print(f\"Output logits shape: {logits.shape}\")\n", "print(f\"This means we have predictions for {logits.shape[1]} positions\")\n", "print(f\"For each position, we have scores for all {logits.shape[2]} tokens in the vocabulary\")"]}, {"cell_type": "markdown", "id": "b964fe49", "metadata": {}, "source": ["### What's happening here?\n", "\n", "Inside the model, here's what's happening:\n", "\n", "1. The number IDs for each word get turned into lists of numbers that represent their meaning (each ID is convered into an embedding vector).\n", "2. The model adds information about where each word appears in the sentence - first, second, third, etc. (with position embeddings).\n", "3. The model then processes this information through several layers that:\n", "    - Figure out which words should pay attention to each other - like how \"is\" relates to \"intelligence\" (self-attention mechanisms).\n", "    - Process this information to understand the meaning better\n", "4. Finally, the model makes a giant list of scores for every possible next word it knows.\n", "\n", "The output is basically a big scorecard showing how likely each possible next word is. Since our model knows about 50,257 different words or word pieces, it gives a score to each one of them, ranking from most likely to least likely."]}, {"cell_type": "markdown", "id": "step5-md", "metadata": {}, "source": ["## Step 5: Next Token Prediction\n", "\n", "Now let's look at the model's prediction for the next token after our prompt:"]}, {"cell_type": "code", "execution_count": 6, "id": "next-token-prediction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 Predictions for Next Token:\n", "----------------------------------------\n", "Token           ID       Probability\n", "----------------------------------------\n", "' the'          262      26.98%\n", "' our'          674      6.10%\n", "' human'        1692     2.45%\n", "' people'       661      2.02%\n", "' itself'       2346     1.83%\n", "' technology'   3037     1.82%\n", "' a'            257      1.54%\n", "' us'           514      1.41%\n", "' society'      3592     1.29%\n", "' how'          703      1.24%\n"]}], "source": ["# We want the predictions for the last position (after \"transforming\")\n", "next_token_logits = logits[0, -1, :]\n", "\n", "# Convert logits to probabilities\n", "next_token_probs = torch.softmax(next_token_logits, dim=0)\n", "\n", "# Get the top 10 most likely tokens\n", "top_k = 10\n", "topk_probs, topk_indices = torch.topk(next_token_probs, top_k)\n", "\n", "# Convert to lists for easier handling\n", "topk_probs = topk_probs.detach().numpy()\n", "topk_indices = topk_indices.detach().numpy()\n", "\n", "# Get the corresponding tokens\n", "topk_tokens = [tokenizer.decode([idx]) for idx in topk_indices]\n", "\n", "print(\"Top 10 Predictions for Next Token:\")\n", "print(\"-\" * 40)\n", "print(f\"{'Token':<15} {'ID':<8} {'Probability':<10}\")\n", "print(\"-\" * 40)\n", "for i in range(top_k):\n", "    print(f\"{repr(topk_tokens[i]):<15} {topk_indices[i]:<8} {topk_probs[i]*100:.2f}%\")"]}, {"cell_type": "markdown", "id": "d4e7957c", "metadata": {}, "source": ["### What's happening here?\n", "\n", "The model has read our phrase \"Artificial intelligence is transforming\" and made a guess about what might come next:\n", "\n", "1. First, the model creates raw scores for every possible next tokens\n", "2. We turn these scores into percentages (like 60%, 25%, 10%) so they're easier to understand\n", "3. We look at just the top 10 tokens with the highest percentages\n", "\n", "These percentages show what the model thinks should come next based on all the text it's seen before. A higher percentage means the model is more confident that word is a good fit to continue the sentence."]}, {"cell_type": "markdown", "id": "step6-md", "metadata": {}, "source": ["## Step 6: <PERSON><PERSON>\n", "\n", "Now we need to select which token to use next. Let's look at different ways to do this:"]}, {"cell_type": "code", "execution_count": 7, "id": "token-selection", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token Selection Results:\n", "Greedy selection: ' the' (always picks the most likely token)\n", "Temperature sampling: ' our' (randomly selects based on adjusted probabilities)\n", "\n", "Top-k tokens with temperature adjustment:\n", "----------------------------------------\n", "Token           Original %   Adjusted %  \n", "----------------------------------------\n", "' the'          26.98        83.48       \n", "' our'          6.10         9.97        \n", "' human'        2.45         2.70        \n", "' people'       2.02         2.06        \n", "' itself'       1.83         1.79        \n"]}], "source": ["# Method 1: Greedy selection (always pick the most likely token)\n", "greedy_index = torch.argmax(next_token_probs).item()\n", "greedy_token = tokenizer.decode([greedy_index])\n", "\n", "# Method 2: Temperature sampling (adjust probability distribution)\n", "temperature = 0.7  # Lower = more deterministic, Higher = more random\n", "temp_logits = next_token_logits / temperature\n", "temp_probs = torch.softmax(temp_logits, dim=0)\n", "\n", "# Method 3: Top-k sampling (sample from k most likely tokens)\n", "k = 5\n", "topk_temp_probs, topk_indices = torch.topk(temp_probs, k)\n", "topk_temp_probs = topk_temp_probs / topk_temp_probs.sum()  # Renormalize\n", "\n", "# Let's select using temperature + top-k\n", "sample_index = np.random.choice(topk_indices.detach().numpy(), p=topk_temp_probs.detach().numpy())\n", "sample_token = tokenizer.decode([sample_index])\n", "\n", "print(\"Token Selection Results:\")\n", "print(f\"Greedy selection: '{greedy_token}' (always picks the most likely token)\")\n", "print(f\"Temperature sampling: '{sample_token}' (randomly selects based on adjusted probabilities)\")\n", "\n", "# Show the top-k tokens with adjusted probabilities\n", "print(\"\\nTop-k tokens with temperature adjustment:\")\n", "print(\"-\" * 40)\n", "print(f\"{'Token':<15} {'Original %':<12} {'Adjusted %':<12}\")\n", "print(\"-\" * 40)\n", "for i in range(k):\n", "    token_id = topk_indices[i].item()\n", "    token_text = tokenizer.decode([token_id])\n", "    orig_prob = next_token_probs[token_id].item() * 100\n", "    adj_prob = topk_temp_probs[i].item() * 100\n", "    print(f\"{repr(token_text):<15} {orig_prob:<12.2f} {adj_prob:<12.2f}\")"]}, {"cell_type": "markdown", "id": "a24570a8", "metadata": {}, "source": ["### What's happening here?\n", "\n", "We're looking at two different ways to pick the next word:\n", "\n", "1. **Greedy selection**: Always pick the most likely word: This is like always picking the safe choice. It's predictable, but can get boring and repetitive.\n", "\n", "2. **Temperature sampling with Top-k**: Mix in some controlled randomness.\n", "    - We can adjust how random we want to be (temperature)\n", "    - We only consider the few most likely words (top-k)\n", "   - Then we randomly choose from those words, giving better odds to more likely words\n", "\n", "\n", "\n", "Using some randomness helps make the text more interesting and varied, instead of always saying the same thing when given the same starting point."]}, {"cell_type": "markdown", "id": "step7-md", "metadata": {}, "source": ["## Step 7: Building the Response\n", "\n", "Now we'll see the complete text generation process in action, adding one token at a time:"]}, {"cell_type": "code", "execution_count": 8, "id": "build-response", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting prompt: 'Artificial intelligence is transforming'\n", "\n", "--- Step 1: Generating token #5 ---\n", "\n", "Top candidates:\n", "  1. ' the' (ID: 262, Probability: 66.72%)\n", "  2. ' our' (ID: 674, Probability: 7.97%)\n", "  3. ' human' (ID: 1692, Probability: 2.16%)\n", "  4. ' people' (ID: 661, Probability: 1.65%)\n", "  5. ' itself' (ID: 2346, Probability: 1.43%)\n", "\n", "Selected token: ' the'\n", "Text so far: 'Artificial intelligence is transforming the'\n", "\n", "--- Step 2: Generating token #6 ---\n", "\n", "Top candidates:\n", "  1. ' way' (ID: 835, Probability: 45.63%)\n", "  2. ' world' (ID: 995, Probability: 33.68%)\n", "  3. ' lives' (ID: 3160, Probability: 4.88%)\n", "  4. ' human' (ID: 1692, Probability: 2.45%)\n", "  5. ' workplace' (ID: 15383, Probability: 0.91%)\n", "\n", "Selected token: ' world'\n", "Text so far: 'Artificial intelligence is transforming the world'\n", "\n", "--- Step 3: Generating token #7 ---\n", "\n", "Top candidates:\n", "  1. ' of' (ID: 286, Probability: 37.49%)\n", "  2. '.' (ID: 13, Probability: 18.31%)\n", "  3. ' into' (ID: 656, Probability: 12.98%)\n", "  4. ',' (ID: 11, Probability: 7.24%)\n", "  5. ' around' (ID: 1088, Probability: 6.34%)\n", "\n", "Selected token: ' of'\n", "Text so far: 'Artificial intelligence is transforming the world of'\n", "\n", "--- Step 4: Generating token #8 ---\n", "\n", "Top candidates:\n", "  1. ' the' (ID: 262, Probability: 15.25%)\n", "  2. ' science' (ID: 3783, Probability: 11.44%)\n", "  3. ' technology' (ID: 3037, Probability: 8.57%)\n", "  4. ' artificial' (ID: 11666, Probability: 7.56%)\n", "  5. ' computing' (ID: 14492, Probability: 5.33%)\n", "\n", "Selected token: ' science'\n", "Text so far: 'Artificial intelligence is transforming the world of science'\n", "\n", "--- Step 5: Generating token #9 ---\n", "\n", "Top candidates:\n", "  1. ' and' (ID: 290, Probability: 45.56%)\n", "  2. '.' (ID: 13, Probability: 19.95%)\n", "  3. ' into' (ID: 656, Probability: 11.00%)\n", "  4. ',' (ID: 11, Probability: 9.08%)\n", "  5. ' fiction' (ID: 10165, Probability: 6.26%)\n", "\n", "Selected token: ' into'\n", "Text so far: 'Artificial intelligence is transforming the world of science into'\n", "\n", "Final generated text: 'Artificial intelligence is transforming the world of science into'\n"]}], "source": ["def generate_step_by_step(prompt, max_new_tokens=5, temperature=0.7, top_k=5):\n", "    \"\"\"Generate text token by token with detailed output at each step\"\"\"\n", "    # Start with the prompt\n", "    current_text = prompt\n", "    input_ids = tokenizer.encode(prompt, return_tensors=\"pt\")\n", "    \n", "    print(f\"Starting prompt: '{prompt}'\\n\")\n", "    \n", "    # Generate new tokens one by one\n", "    for i in range(max_new_tokens):\n", "        print(f\"--- Step {i+1}: Generating token #{len(prompt.split())+i+1} ---\")\n", "        \n", "        # Get model predictions\n", "        with torch.no_grad():\n", "            outputs = model(input_ids)\n", "        \n", "        # Get next token logits (predictions for the next token)\n", "        next_token_logits = outputs.logits[0, -1, :]\n", "        \n", "        # Apply temperature\n", "        next_token_logits = next_token_logits / temperature\n", "        \n", "        # Get top-k token indices and their probabilities\n", "        topk_probs, topk_indices = torch.topk(torch.softmax(next_token_logits, dim=0), top_k)\n", "        \n", "        # Print the top candidates\n", "        print(\"\\nTop candidates:\")\n", "        for j in range(top_k):\n", "            token_id = topk_indices[j].item()\n", "            token_text = tokenizer.decode([token_id])\n", "            token_prob = topk_probs[j].item() * 100\n", "            print(f\"  {j+1}. '{token_text}' (ID: {token_id}, Probability: {token_prob:.2f}%)\")\n", "        \n", "        # Renormalize probabilities for top-k\n", "        topk_probs = topk_probs / topk_probs.sum()\n", "        \n", "        # Sample from top-k\n", "        chosen_idx = np.random.choice(topk_indices.detach().numpy(), p=topk_probs.detach().numpy())\n", "        chosen_token = tokenizer.decode([chosen_idx])\n", "        \n", "        print(f\"\\nSelected token: '{chosen_token}'\")\n", "        \n", "        # Update for next iteration\n", "        next_token = torch.tensor([[chosen_idx]])\n", "        input_ids = torch.cat([input_ids, next_token], dim=1)\n", "        current_text += chosen_token\n", "        \n", "        print(f\"Text so far: '{current_text}'\\n\")\n", "    \n", "    print(f\"Final generated text: '{current_text}'\")\n", "    return current_text\n", "\n", "# Generate text step by step\n", "final_text = generate_step_by_step(prompt, max_new_tokens=5, temperature=0.7, top_k=5)"]}, {"cell_type": "markdown", "id": "complete-process-explanation", "metadata": {}, "source": ["### What's happening here?\n", "\n", "We've just witnessed the complete text generation process, with each step broken down:\n", "\n", "1. We start with our prompt\n", "2. For each new token:\n", "   - The model processes all the text so far\n", "   - It generates probabilities for the next token\n", "   - We apply temperature and top-k filtering\n", "   - We sample a token from the resulting distribution\n", "   - The selected token is added to our text\n", "   - We repeat until we reach our desired length\n", "\n", "This shows how the model works in an auto-regressive manner - each new token depends on all the tokens that came before it."]}, {"cell_type": "markdown", "id": "parameters-effect", "metadata": {}, "source": ["## The Effect of Generation Parameters\n", "\n", "Different parameters can dramatically change the output. Let's experiment with a few:"]}, {"cell_type": "code", "execution_count": 9, "id": "parameter-experiments", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Effect of Generation Parameters:\n", "\n", "Greedy (no sampling)\n", "Parameters: {'do_sample': False}\n", "Input: Artificial intelligence is transforming\n", "Generated:  the way we think about our lives.\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Low Temperature (0.3)\n", "Parameters: {'temperature': 0.3, 'do_sample': True}\n", "Input: Artificial intelligence is transforming\n", "Generated:  the way we interact with the world and our lives.\n", "\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "High Temperature (1.5)\n", "Parameters: {'temperature': 1.5, 'do_sample': True}\n", "Input: Artificial intelligence is transforming\n", "Generated:  human behavior. We may be at least partially equipped to answer these questions,\n", "--------------------------------------------------------------------------------\n", "Top-k (5)\n", "Parameters: {'top_k': 5, 'do_sample': True}\n", "Input: Artificial intelligence is transforming\n", "Generated:  the way people think about life in a way that's more like the way\n", "--------------------------------------------------------------------------------\n", "Top-p (0.9)\n", "Parameters: {'top_p': 0.9, 'do_sample': True}\n", "Input: Artificial intelligence is transforming\n", "Generated:  artificial intelligence into something of an artificial intelligence, thanks to the technology of the\n", "--------------------------------------------------------------------------------\n", "Balanced\n", "Parameters: {'temperature': 0.7, 'top_k': 50, 'top_p': 0.9, 'do_sample': True}\n", "Input: Artificial intelligence is transforming\n", "Generated:  the way we work.\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# Function to generate text with different parameters\n", "def generate_with_params(prompt, max_new_tokens=15, **params):\n", "    input_ids = tokenizer.encode(prompt, return_tensors=\"pt\")\n", "    \n", "    # Set up generation parameters\n", "    gen_params = {}\n", "    if 'temperature' in params:\n", "        gen_params['temperature'] = params['temperature']\n", "    if 'top_k' in params:\n", "        gen_params['top_k'] = params['top_k']\n", "    if 'top_p' in params:\n", "        gen_params['top_p'] = params['top_p']\n", "    if 'do_sample' in params:\n", "        gen_params['do_sample'] = params['do_sample']\n", "    \n", "    # Generate the output\n", "    output_ids = model.generate(\n", "        input_ids, \n", "        max_length=len(input_ids[0]) + max_new_tokens,\n", "        pad_token_id=tokenizer.eos_token_id,\n", "        **gen_params\n", "    )\n", "    \n", "    return tokenizer.decode(output_ids[0], skip_special_tokens=True)\n", "\n", "# Try different parameter combinations\n", "params_to_try = [\n", "    {'name': 'Greedy (no sampling)', 'params': {'do_sample': False}},\n", "    {'name': 'Low Temperature (0.3)', 'params': {'temperature': 0.3, 'do_sample': True}},\n", "    {'name': 'High Temperature (1.5)', 'params': {'temperature': 1.5, 'do_sample': True}},\n", "    {'name': 'Top-k (5)', 'params': {'top_k': 5, 'do_sample': True}},\n", "    {'name': 'Top-p (0.9)', 'params': {'top_p': 0.9, 'do_sample': True}},\n", "    {'name': 'Balanced', 'params': {'temperature': 0.7, 'top_k': 50, 'top_p': 0.9, 'do_sample': True}}\n", "]\n", "\n", "# Generate and display results\n", "print(\"Effect of Generation Parameters:\\n\")\n", "\n", "for setting in params_to_try:\n", "    output = generate_with_params(prompt, **setting['params'])\n", "    generated_part = output[len(prompt):]\n", "    \n", "    print(f\"{setting['name']}\")\n", "    print(f\"Parameters: {setting['params']}\")\n", "    print(f\"Input: {prompt}\")\n", "    print(f\"Generated: {generated_part}\")\n", "    print(\"-\" * 80)"]}, {"cell_type": "markdown", "id": "parameters-explanation", "metadata": {}, "source": ["### Generation Parameters Explained\n", "\n", "- **do_sample**: When False, the model always picks the most likely token (greedy decoding). When True, it samples according to the probability distribution.\n", "\n", "- **temperature**: Controls the randomness of predictions.\n", "  - Lower values (e.g., 0.3) make the model more confident and deterministic\n", "  - Higher values (e.g., 1.5) make the model more random and creative\n", "  - Value of 1.0 keeps the original probabilities unchanged\n", "\n", "- **top_k**: Limits the selection to only the k most likely next tokens.\n", "  - Lower values (e.g., 5) focus on the most probable tokens\n", "  - Higher values allow more diversity but might include less relevant tokens\n", "\n", "- **top_p (nucleus sampling)**: Selects from the smallest set of tokens whose cumulative probability exceeds p.\n", "  - Adapts the number of tokens considered based on the confidence of the model\n", "  - Values around 0.9 are common and work well in practice"]}, {"cell_type": "markdown", "id": "complete-pipeline", "metadata": {}, "source": ["## The Complete LLM Pipeline\n", "\n", "Let's summarize the entire text generation pipeline we've explored:\n", "\n", "1. **Input Text**: We start with a text prompt that the model will continue\n", "\n", "2. **Tokenization**: The tokenizer breaks the text into tokens (words, subwords, or characters)\n", "\n", "3. **Token → ID Conversion**: Each token is converted to a numeric ID according to the model's vocabulary\n", "\n", "4. **Model Processing**: The IDs are processed through the neural network architecture:\n", "   - Embedding lookup for each token\n", "   - Position information added\n", "   - Multiple transformer layers process the sequence\n", "   - Attention mechanisms focus on relevant parts of the input\n", "   \n", "5. **Next Token Prediction**: The model outputs probabilities for each possible next token\n", "\n", "6. **Token Selection**: A token is selected based on these probabilities:\n", "   - Greedy selection (most likely token)\n", "   - Sampling with temperature/top-k/top-p for controlled randomness\n", "   \n", "7. **Add to Output**: The selected token is added to the generated text\n", "\n", "8. **Repeat**: Steps 3-7 are repeated with the updated text until we reach the desired length"]}, {"cell_type": "markdown", "id": "conclusion", "metadata": {}, "source": ["## Summary: The LLM Pipeline End-to-End\n", "\n", "We've now explored the complete text generation pipeline of a local LLM. This process demonstrates how an LLM generates text one token at a time, with each decision influenced by all previous tokens. \n", "\n", "The probabilistic nature of token selection (except in greedy decoding) explains why you can get different outputs from the same prompt - a key characteristic of working with AI systems.\n", "\n", "Understanding this pipeline helps you:\n", "- Debug issues in text generation\n", "- Optimize performance by adjusting parameters\n", "- Design more effective prompts\n", "- Better integrate LLMs into your applications"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}