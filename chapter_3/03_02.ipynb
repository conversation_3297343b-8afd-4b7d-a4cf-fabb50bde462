{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Text Extraction Fundamentals"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Ignoring wrong pointing object 6 0 (offset 0)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["PDF extract: Renewable Energy Market Trends: A 2025 Overview Executive Summary This report examines the current state of renewable energy markets globally, highlighting key trends, challenges, and opportunities. I...\n", "DOCX extract: Renewable Energy Market Trends: A 2025 Overview\n", "\n", "Executive Summary\n", "\n", "This report examines the current state of renewable energy markets globally, highlighting key trends, challenges, and opportunities....\n", "Web extract: <!doctype html>\n", "<html>\n", "<head>\n", "    <title>Example Domain</title>\n", "\n", "    <meta charset=\"utf-8\" />\n", "    <meta http-equiv=\"Content-type\" content=\"text/html; charset=utf-8\" />\n", "    <meta name=\"viewport\" conten...\n"]}], "source": ["from llama_index.readers.file import PDFReader, DocxReader\n", "from llama_index.readers.web import SimpleWebPageReader\n", "import pathlib\n", "\n", "# Extract from PDF\n", "pdf_reader = PDFReader()\n", "pdf_docs = pdf_reader.load_data(file=pathlib.Path(\"samples/pdf-report.pdf\"))\n", "\n", "# Extract from DOCX\n", "docx_reader = DocxReader()\n", "docx_docs = docx_reader.load_data(file=pathlib.Path(\"samples/docx-report.docx\"))\n", "\n", "# Extract from Web\n", "web_reader = SimpleWebPageReader()\n", "web_docs = web_reader.load_data(urls=[\"https://example.com\"])\n", "\n", "# Let's see what the extracted text looks like\n", "print(f\"PDF extract: {pdf_docs[0].text[:200]}...\")\n", "print(f\"DOCX extract: {docx_docs[0].text[:200]}...\")\n", "print(f\"Web extract: {web_docs[0].text[:200]}...\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV extract: OrderID, OrderDate, Customer, Product, Quantity, OrderStatus, Misc\n", "4321, 1/30/2010, BX30550, ABQ008, 163, Complete, 54\n", "4352, 1/15/2010, DY55760, ABQ008, 107, Complete, 36\n", "4353, 1/29/2010, BC13961, ABQ...\n", "JSON extract: \"OrderID\": 4321,\n", "\"OrderDate\": \"1/30/2010\",\n", "\"Customer\": \"BX30550\",\n", "\"Product\": \"ABQ008\",\n", "\"Quantity\": 163,\n", "\"OrderStatus\": \"Complete\",\n", "\"Misc\": 54\n", "\"OrderID\": 4352,\n", "\"OrderDate\": \"1/15/2010\",\n", "\"Customer\": \"DY...\n", "Markdown extract: \n", "\n", "Sample Data Files Files Overview\n", "This directory contains sample data files in various formats for testing and demonstration purposes.\n", "- **csv-data.csv**: Tabular data in CSV format\n", "- **json-data.jso...\n", "DB extract: OrderID: 4321, OrderDate: 1/30/2010, Customer: BX30550, Product: ABQ008, Quantity: 163, OrderStatus: Complete, Misc: 54...\n"]}], "source": ["from llama_index.readers.file import CSVReader, MarkdownReader\n", "from llama_index.readers.json import JSONReader\n", "from llama_index.readers.database import DatabaseReader\n", "import pathlib\n", "\n", "# CSV files\n", "csv_reader = CSVReader()\n", "csv_docs = csv_reader.load_data(file=pathlib.Path(\"samples/csv-data.csv\"))\n", "\n", "# JSON files\n", "json_reader = JSONReader()\n", "json_docs = json_reader.load_data(input_file=\"samples/json-data.json\")\n", "\n", "# Markdown files\n", "md_reader = MarkdownReader()\n", "md_docs = md_reader.load_data(file=\"samples/README.md\")\n", "\n", "# Databases \n", "db_reader = DatabaseReader(uri=\"sqlite:///samples/database.db\")\n", "db_docs = db_reader.load_data(query=\"SELECT * FROM orders\")\n", "\n", "# Let's see what the extracted text looks like\n", "print(f\"CSV extract: {csv_docs[0].text[:200]}...\")\n", "print(f\"JSON extract: {json_docs[0].text[:200]}...\")\n", "print(f\"Markdown extract: {md_docs[0].text[:200]}...\")\n", "print(f\"DB extract: {db_docs[0].text[:200]}...\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original first 100 chars: Renewable Energy Market Trends: A 2025 Overview Executive Summary This report examines the current s\n", "Cleaned first 100 chars: Renewable Energy Market Trends: A 2025 Overview Executive Summary This report examines the current s\n", "Original length: 2180 characters\n", "Cleaned length: 2173 characters\n"]}], "source": ["import re\n", "from llama_index.core.schema import Document\n", "\n", "# Get our raw text from a document\n", "raw_text = pdf_docs[0].text\n", "\n", "def clean_text(text):\n", "    # Remove excessive whitespace\n", "    text = re.sub(r'\\s+', ' ', text)\n", "\n", "    # Remove special characters but keep structural elements\n", "    text = re.sub(r'[^\\w\\s\\.\\,\\;\\:\\-\\(\\)\\[\\]\\{\\}\\\"\\'\\n\\t]', '', text)\n", "\n", "    # Fix common OCR errors (example)\n", "    text = text.replace('l<eywor', 'keyword')\n", "\n", "    return text.strip()\n", "\n", "\n", "# Let's clean our text\n", "cleaned_text = clean_text(raw_text)\n", "\n", "print(f\"Original first 100 chars: {raw_text[:100]}\")\n", "print(f\"Cleaned first 100 chars: {cleaned_text[:100]}\")\n", "print(f\"Original length: {len(raw_text)} characters\")\n", "print(f\"Cleaned length: {len(cleaned_text)} characters\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted metadata: {'source': '../samples/pdf-report.pdf', 'file_type': 'pdf'}\n"]}], "source": ["# Function to extract basic metadata\n", "def extract_metadata(text, filename):\n", "    metadata = {\n", "        \"source\": filename,\n", "        \"file_type\": filename.split('.')[-1],\n", "    }\n", "\n", "    # Extract title (assume first line might be title)\n", "    lines = text.split('\\n')\n", "    if lines and len(lines[0]) < 100:  # Simple heuristic for title\n", "        metadata[\"title\"] = lines[0].strip()\n", "\n", "    # Try to extract date with regex (simple example)\n", "    date_match = re.search(r'\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}', text)\n", "    if date_match:\n", "        metadata[\"date\"] = date_match.group(0)\n", "\n", "    return metadata\n", "\n", "\n", "# Extract metadata from our document\n", "metadata = extract_metadata(raw_text, \"../samples/pdf-report.pdf\")\n", "\n", "# Create a new document with cleaned text and metadata\n", "processed_doc = Document(\n", "    text=cleaned_text,\n", "    metadata=metadata\n", ")\n", "\n", "print(f\"Extracted metadata: {metadata}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Ignoring wrong pointing object 6 0 (offset 0)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed document: 2173 characters\n", "Metadata: {'source': 'samples/pdf-report.pdf', 'file_type': 'pdf'}\n"]}], "source": ["def process_document(file_path):\n", "    \"\"\"Process a document with appropriate reader and cleaning\"\"\"\n", "\n", "    # Determine file type\n", "    file_type = file_path.split('.')[-1].lower()\n", "\n", "    # Select appropriate reader\n", "    if file_type == 'pdf':\n", "        reader = PDFReader()\n", "    elif file_type in ['docx', 'doc']:\n", "        reader = DocxReader()\n", "    elif file_type in ['html', 'htm']:\n", "        # Assuming file is a local HTML file\n", "        reader = SimpleWebPageReader()\n", "    else:\n", "        # Default to simple text reading\n", "        with open(file_path, 'r') as f:\n", "            return Document(text=f.read(), metadata={\"source\": file_path})\n", "\n", "    # Load and extract text\n", "    docs = reader.load_data(file=file_path)\n", "\n", "    if not docs:\n", "        return None\n", "\n", "    # Clean the text\n", "    cleaned_text = clean_text(docs[0].text)\n", "\n", "    # Extract metadata\n", "    metadata = extract_metadata(docs[0].text, file_path)\n", "\n", "    # Create processed document\n", "    return Document(text=cleaned_text, metadata=metadata)\n", "\n", "# Example usage\n", "processed_doc = process_document(\"samples/pdf-report.pdf\")\n", "print(f\"Processed document: {len(processed_doc.text)} characters\")\n", "print(f\"Metadata: {processed_doc.metadata}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}