{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Benefits of preserving text structure:\n", "\n", "- Better chunking that respects semantic boundaries\n", "- More accurate search and retrieval\n", "- Improved question answering by maintaining context\n", "- The ability to handle structured data like tables\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total nodes created: 6\n", "\n", "Node 1:\n", "Text: # AI Engineering Fundamentals...\n", "Metadata: {'header_path': '/'}\n", "\n", "Node 2:\n", "Text: ## Introduction to Vector Databases Vector databases [...]...\n", "Metadata: {'header_path': '/AI Engineering Fundamentals/'}\n", "\n", "Node 3:\n", "Text: ### Key Advantages - Efficient similarity search - [...]...\n", "Metadata: {'header_path': '/AI Engineering Fundamentals/Introduction to Vector Databases/'}\n", "\n", "Node 4:\n", "Text: ### Common Operations 1. **Vector Indexing**: Creating [...]...\n", "Metadata: {'header_path': '/AI Engineering Fundamentals/Introduction to Vector Databases/'}\n", "\n", "Node 5:\n", "Text: ## Working with Embeddings Embeddings are dense [...]...\n", "Metadata: {'header_path': '/AI Engineering Fundamentals/'}\n", "\n", "Node 6:\n", "Text: ### Popular Embedding Models - OpenAI text-embedding- [...]...\n", "Metadata: {'header_path': '/AI Engineering Fundamentals/Working with Embeddings/'}\n"]}], "source": ["from llama_index.core.schema import Document\n", "from llama_index.core.node_parser import MarkdownNodeParser\n", "import textwrap\n", "\n", "# Sample markdown document with clear structure\n", "markdown_text = \"\"\"\n", "# AI Engineering Fundamentals\n", "\n", "## Introduction to Vector Databases\n", "\n", "Vector databases are specialized database systems designed to store and query vector embeddings efficiently.\n", "\n", "### Key Advantages\n", "- Efficient similarity search\n", "- Scalable to billions of vectors\n", "- Support for metadata filtering\n", "\n", "### Common Operations\n", "1. **Vector Indexing**: Creating data structures for efficient search\n", "2. **Approximate Nearest Neighbor Search**: Finding similar vectors quickly\n", "3. **Hybrid Search**: Combining vector similarity with metadata filters\n", "\n", "## Working with Embeddings\n", "\n", "Embeddings are dense numerical representations of data that capture semantic meaning.\n", "\n", "### Popular Embedding Models\n", "- OpenAI text-embedding-ada-002\n", "- Sentence Transformers\n", "- CLIP for image embeddings\n", "\"\"\"\n", "\n", "# Create a document\n", "document = Document(text=markdown_text)\n", "\n", "# Create a parser that recognizes markdown structure\n", "markdown_parser = MarkdownNodeParser()\n", "\n", "# Parse the document\n", "nodes = markdown_parser.get_nodes_from_documents([document])\n", "\n", "# Display the resulting nodes\n", "print(f\"Total nodes created: {len(nodes)}\")\n", "for i, node in enumerate(nodes):\n", "    print(f\"\\nNode {i+1}:\")\n", "    print(f\"Text: {textwrap.shorten(node.text, width=60)}...\")\n", "    print(f\"Metadata: {node.metadata}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total HTML nodes created: 14\n", "\n", "Node 1:\n", "Text: AI Engineering Fundamentals...\n", "Metadata: {'tag': 'h1'}\n", "\n", "Node 2:\n", "Text: Introduction to Vector Databases...\n", "Metadata: {'tag': 'h2'}\n", "\n", "Node 3:\n", "Text: Vector databases are specialized database systems [...]...\n", "Metadata: {'tag': 'p'}\n", "\n", "Node 4:\n", "Text: Key Advantages...\n", "Metadata: {'tag': 'h3'}\n", "\n", "Node 5:\n", "Text: Efficient similarity search Scalable to billions of [...]...\n", "Metadata: {'tag': 'li'}\n", "\n", "Node 6:\n", "Text: Common Operations...\n", "Metadata: {'tag': 'h3'}\n", "\n", "Node 7:\n", "Text: : Creating data structures for efficient search...\n", "Metadata: {'tag': 'li'}\n", "\n", "Node 8:\n", "Text: Vector Indexing...\n", "Metadata: {'tag': 'b'}\n", "\n", "Node 9:\n", "Text: : Finding similar vectors quickly...\n", "Metadata: {'tag': 'li'}\n", "\n", "Node 10:\n", "Text: Approximate Nearest Neighbor Search...\n", "Metadata: {'tag': 'b'}\n", "\n", "Node 11:\n", "Text: : Combining vector similarity with metadata filters...\n", "Metadata: {'tag': 'li'}\n", "\n", "Node 12:\n", "Text: Hybrid Search...\n", "Metadata: {'tag': 'b'}\n", "\n", "Node 13:\n", "Text: Working with Embeddings...\n", "Metadata: {'tag': 'h2'}\n", "\n", "Node 14:\n", "Text: Embeddings are dense numerical representations of data [...]...\n", "Metadata: {'tag': 'p'}\n", "\n", "Extracted Table:\n", "['Model Name', 'Dimensions', 'Use Case']\n", "['text-embedding-ada-002', '1536', 'General text embeddings']\n", "['all-MiniLM-L6-v2', '384', 'Efficient semantic search']\n"]}], "source": ["from llama_index.core.node_parser import HTMLNodeParser\n", "from llama_index.core import Document\n", "import textwrap\n", "from bs4 import BeautifulSoup\n", "\n", "# Sample HTML document\n", "html_text = \"\"\"\n", "<html>\n", "<body>\n", "  <h1>AI Engineering Fundamentals</h1>\n", "  \n", "  <h2>Introduction to Vector Databases</h2>\n", "  <p>Vector databases are specialized database systems designed to store and query vector embeddings efficiently.</p>\n", "  \n", "  <h3>Key Advantages</h3>\n", "  <ul>\n", "    <li>Efficient similarity search</li>\n", "    <li>Scalable to billions of vectors</li>\n", "    <li>Support for metadata filtering</li>\n", "  </ul>\n", "  \n", "  <h3>Common Operations</h3>\n", "  <ol>\n", "    <li><b>Vector Indexing</b>: Creating data structures for efficient search</li>\n", "    <li><b>Approximate Nearest Neighbor Search</b>: Finding similar vectors quickly</li>\n", "    <li><b>Hybrid Search</b>: Combining vector similarity with metadata filters</li>\n", "  </ol>\n", "  \n", "  <h2>Working with Embeddings</h2>\n", "  <p>Embeddings are dense numerical representations of data that capture semantic meaning.</p>\n", "  \n", "  <table border=\"1\">\n", "    <tr>\n", "      <th>Model Name</th>\n", "      <th>Dimensions</th>\n", "      <th>Use Case</th>\n", "    </tr>\n", "    <tr>\n", "      <td>text-embedding-ada-002</td>\n", "      <td>1536</td>\n", "      <td>General text embeddings</td>\n", "    </tr>\n", "    <tr>\n", "      <td>all-MiniLM-L6-v2</td>\n", "      <td>384</td>\n", "      <td>Efficient semantic search</td>\n", "    </tr>\n", "  </table>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "# Create a document\n", "html_document = Document(text=html_text)\n", "\n", "# Create HTML parser\n", "html_parser = HTMLNodeParser()\n", "\n", "# Parse the document\n", "html_nodes = html_parser.get_nodes_from_documents([html_document])\n", "\n", "# Display the resulting nodes\n", "print(f\"Total HTML nodes created: {len(html_nodes)}\")\n", "for i, node in enumerate(html_nodes):\n", "    print(f\"\\nNode {i+1}:\")\n", "    print(f\"Text: {textwrap.shorten(node.text, width=60)}...\")\n", "    print(f\"Metadata: {node.metadata}\")\n", "\n", "# Extract table data specifically\n", "\n", "\n", "def extract_tables(html_content):\n", "    soup = BeautifulSoup(html_content, 'html.parser')\n", "    tables = soup.find_all('table')\n", "\n", "    extracted_tables = []\n", "    for table in tables:\n", "        rows = table.find_all('tr')\n", "        table_data = []\n", "\n", "        for row in rows:\n", "            cols = row.find_all(['td', 'th'])\n", "            row_data = [col.text.strip() for col in cols]\n", "            table_data.append(row_data)\n", "\n", "        extracted_tables.append(table_data)\n", "\n", "    return extracted_tables\n", "\n", "\n", "# Extract tables\n", "tables = extract_tables(html_text)\n", "print(\"\\nExtracted Table:\")\n", "for row in tables[0]:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document Table of Contents:\n", "Section 1\n", "Section 2\n", "Section 3\n", "Section 4\n", "Section 5\n", "Section 6\n", "\n", "Searching for 'advantages':\n", "Found in: Section 3\n", "Snippet: ### Key Advantages\n", "- Efficient similarity search\n", "- Scalable to billions of vectors\n", "- Support for met...\n", "\n", "Searching for 'embedding models':\n", "Found in: Section 6\n", "Snippet: ### Popular Embedding Models\n", "- OpenAI text-embedding-ada-002\n", "- Sentence Transformers\n", "- CLIP for imag...\n", "\n", "Searching for 'indexing':\n", "Found in: Section 4\n", "Snippet: ### Common Operations\n", "1. **Vector Indexing**: Creating data structures for efficient search\n", "2. **App...\n"]}], "source": ["# Function to create a searchable document map\n", "def create_document_map(nodes):\n", "    \"\"\"Create a searchable map of document sections\"\"\"\n", "    document_map = {}\n", "\n", "    for i, node in enumerate(nodes):\n", "        # Get the heading or create a default one\n", "        heading = node.metadata.get(\"heading\", f\"Section {i+1}\")\n", "        level = node.metadata.get(\"heading_level\", 0)\n", "\n", "        # Add to document map with indent based on level\n", "        indent = \"  \" * (level - 1) if level > 0 else \"\"\n", "        document_map[heading] = {\n", "            \"index\": i,\n", "            \"level\": level,\n", "            \"text\": node.text,\n", "            \"display\": f\"{indent}{heading}\"\n", "        }\n", "\n", "    return document_map\n", "\n", "\n", "# Create document map from our markdown nodes\n", "doc_map = create_document_map(nodes)\n", "\n", "# Display the document structure as a table of contents\n", "print(\"Document Table of Contents:\")\n", "for heading, info in doc_map.items():\n", "    print(f\"{info['display']}\")\n", "\n", "# Simple section lookup function\n", "\n", "\n", "def find_section(query, doc_map):\n", "    \"\"\"Find sections that match a query string\"\"\"\n", "    matches = []\n", "\n", "    for heading, info in doc_map.items():\n", "        # Check if query is in heading or content\n", "        if query.lower() in heading.lower() or query.lower() in info['text'].lower():\n", "            matches.append((heading, info))\n", "\n", "    return matches\n", "\n", "\n", "# Try looking up sections\n", "search_terms = [\"advantages\", \"embedding models\", \"indexing\"]\n", "\n", "for term in search_terms:\n", "    print(f\"\\nSearching for '{term}':\")\n", "    results = find_section(term, doc_map)\n", "\n", "    if results:\n", "        for heading, info in results:\n", "            print(f\"Found in: {info['display']}\")\n", "            # Extract a relevant snippet\n", "            text = info['text']\n", "            start = max(0, text.lower().find(term.lower()) - 40)\n", "            snippet = text[start:start+100] + \"...\"\n", "            print(f\"Snippet: {snippet}\")\n", "    else:\n", "        print(\"No results found\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}