{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Why Metadata Matters:\n", "- It provides context about our documents (author, date, source, topic)\n", "- It enables filtering and sorting (find all documents from a specific date range)\n", "- It improves search relevance (identifying which documents are most important)\n", "- It helps with organization (grouping related documents together)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document Text (first 50 chars): \n", "# Annual Report 2023\n", "## Financial Performance\n", "Our\n", "\n", "Document Metadata:\n", "  file_name: annual_report_2023.md\n", "  file_path: /documents/annual_report_2023.md\n", "  file_type: md\n", "  file_size: 348\n", "  extracted_date: 2025-03-30\n", "  year: 2023\n", "  document_type: annual_report\n", "\n", "Nodes created: 1\n", "First node metadata:\n", "  file_name: annual_report_2023.md\n", "  file_path: /documents/annual_report_2023.md\n", "  file_type: md\n", "  file_size: 348\n", "  extracted_date: 2025-03-30\n", "  year: 2023\n", "  document_type: annual_report\n"]}], "source": ["from llama_index.core import Document\n", "from llama_index.core.node_parser import SimpleNodeParser\n", "from llama_index.core.schema import MetadataMode\n", "import os\n", "from datetime import datetime\n", "\n", "# Sample document with some metadata potential\n", "sample_doc = \"\"\"\n", "# Annual Report 2023\n", "## Financial Performance\n", "Our company achieved record profits in 2023, with revenue increasing 15% compared to 2022.\n", "\n", "## Product Launches\n", "The new X1000 product line was launched in March 2023 and has exceeded sales expectations.\n", "\n", "## Future Outlook\n", "We expect continued growth in 2024, driven by expansion into European markets.\n", "\"\"\"\n", "\n", "# Create a document with basic metadata\n", "filename = \"annual_report_2023.md\"\n", "file_path = f\"/documents/{filename}\"\n", "\n", "# Extract basic metadata from filename and content\n", "def extract_basic_metadata(content, filename, file_path):\n", "    \"\"\"Extract basic metadata from document content and file information\"\"\"\n", "\n", "    # Get file information\n", "    file_metadata = {\n", "        \"file_name\": filename,\n", "        \"file_path\": file_path,\n", "        # Extension without the dot\n", "        \"file_type\": os.path.splitext(filename)[1][1:],\n", "        \"file_size\": len(content),  # Simple size in characters\n", "        \"extracted_date\": datetime.now().strftime(\"%Y-%m-%d\")\n", "    }\n", "\n", "    # Try to extract year from content or filename\n", "    year_match = None\n", "    if \"2023\" in content:\n", "        year_match = \"2023\"\n", "    elif \"2022\" in content:\n", "        year_match = \"2022\"\n", "    elif \"2024\" in content:\n", "        year_match = \"2024\"\n", "\n", "    if year_match:\n", "        file_metadata[\"year\"] = year_match\n", "\n", "    # Try to extract document type\n", "    if \"annual report\" in content.lower() or \"annual report\" in filename.lower():\n", "        file_metadata[\"document_type\"] = \"annual_report\"\n", "\n", "    return file_metadata\n", "\n", "\n", "# Extract metadata\n", "basic_metadata = extract_basic_metadata(sample_doc, filename, file_path)\n", "\n", "# Create document with metadata\n", "doc = Document(text=sample_doc, metadata=basic_metadata)\n", "\n", "# Show the document with its metadata\n", "print(\"Document Text (first 50 chars):\", doc.text[:50])\n", "print(\"\\nDocument Metadata:\")\n", "for key, value in doc.metadata.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# Let's create nodes with the metadata\n", "parser = SimpleNodeParser.from_defaults()\n", "nodes = parser.get_nodes_from_documents([doc])\n", "\n", "# Show that metadata is propagated to nodes\n", "print(\"\\nNodes created:\", len(nodes))\n", "print(\"First node metadata:\")\n", "for key, value in nodes[0].metadata.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}