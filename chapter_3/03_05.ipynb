{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## What is Chunking and Why Does it Matter?\n", "\n", "Chunking is the process of breaking down documents into smaller pieces that can be efficiently processed by language models and retrieval systems. The way you chunk your documents directly impacts:\n", "\n", "- **Retrieval Precision**: How accurately your system can find relevant information\n", "- **Context Preservation**: How much surrounding information is maintained\n", "- **Token Economy**: How efficiently you use your LLM's context window\n", "- **Storage Requirements**: How much vector storage you need\n", "\n", "Let's explore different chunking strategies and their impact on document retrieval."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original Document:\n", " # Introduction to Vector Databases  Vector databases are specialized database systems designed to\n", "store and query vector embeddings efficiently. Unlike traditional databases optimized for exact\n", "matches, vector databases excel at similarity searches.  ## Key Advantages  Vector databases offer\n", "several advantages for AI applications: - Efficient similarity search using algorithms like HNSW and\n", "IVF - Support for high-dimensional vector data - Optimized for retrieval-augmented generation (RAG)\n", "appli\n", "...\n", "\n"]}], "source": ["from llama_index.core.schema import Document\n", "import textwrap\n", "\n", "# Sample document text\n", "sample_text = \"\"\"\n", "# Introduction to Vector Databases\n", "\n", "Vector databases are specialized database systems designed to store and query vector embeddings efficiently.\n", "Unlike traditional databases optimized for exact matches, vector databases excel at similarity searches.\n", "\n", "## Key Advantages\n", "\n", "Vector databases offer several advantages for AI applications:\n", "- Efficient similarity search using algorithms like HNSW and IVF\n", "- Support for high-dimensional vector data\n", "- Optimized for retrieval-augmented generation (RAG) applications\n", "\n", "## Common Operations\n", "\n", "The most common operations in vector databases include:\n", "1. Adding vectors with associated metadata\n", "2. Searching for similar vectors using distance metrics\n", "3. Filtering results based on metadata\n", "4. Building and optimizing indexes for faster retrieval\n", "\n", "# Performance Considerations\n", "\n", "When working with vector databases at scale, consider:\n", "- Index construction time vs. query performance\n", "- Memory usage vs. search accuracy\n", "- Batch processing for efficient vector insertion\n", "\"\"\"\n", "\n", "# Create a Document\n", "document = Document(text=sample_text)\n", "\n", "# Let's print the original document to understand its structure\n", "print(\"Original Document:\")\n", "print(textwrap.fill(document.text[:500], 100))\n", "print(\"...\\n\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Sentence Splitting created 6 chunks\n", "\n", "Example chunks:\n", "\n", "Chunk 0:\n", "# Introduction to Vector Databases  Vector databases are specialized database systems designed to\n", "store and query vector embeddings efficiently. Unlike traditional databases optimized for exact\n", "matches, vector databases excel at similarity searches.  ## Key\n", "Character length: 257\n", "\n", "Chunk 1:\n", "## Key Advantages  Vector databases offer several advantages for AI applications: - Efficient\n", "similarity search using algorithms like HNSW and IVF - Support for high-dimensional vector data -\n", "Optimized for\n", "Character length: 205\n", "\n", "Chunk 2:\n", "for high-dimensional vector data - Optimized for retrieval-augmented generation (RAG) applications\n", "## Common Operations  The most common operations in vector databases include: 1.\n", "Character length: 180\n"]}], "source": ["from llama_index.core.node_parser import SentenceSplitter\n", "\n", "# Sentence-based chunking\n", "sentence_splitter = SentenceSplitter(\n", "    chunk_size=40,  # Target chunk size (in characters)\n", "    chunk_overlap=10  # Overlap between chunks (in characters)\n", ")\n", "\n", "sentence_nodes = sentence_splitter.get_nodes_from_documents([document])\n", "\n", "print(f\"Sentence Splitting created {len(sentence_nodes)} chunks\\n\")\n", "print(\"Example chunks:\")\n", "for i in range(min(3, len(sentence_nodes))):\n", "    print(f\"\\nChunk {i}:\")\n", "    print(textwrap.fill(sentence_nodes[i].text[:300], 100))\n", "    print(f\"Character length: {len(sentence_nodes[i].text)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata length (2) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Token Splitting created 7 chunks\n", "\n", "Example chunks:\n", "\n", "Chunk 0:\n", "# Introduction to Vector Databases  Vector databases are specialized database systems designed to\n", "store and query vector embeddings efficiently. Unlike traditional databases optimized for exact\n", "matches, vector databases excel at similarity\n", "Character length: 239\n", "\n", "Chunk 1:\n", "optimized for exact matches, vector databases excel at similarity searches.  ## Key Advantages\n", "Vector databases offer several advantages for AI applications: - Efficient similarity search using\n", "algorithms like HNSW and\n", "Character length: 219\n", "\n", "Chunk 2:\n", "Efficient similarity search using algorithms like HNSW and IVF - Support for high-dimensional vector\n", "data - Optimized for retrieval-augmented generation (RAG) applications  ## Common\n", "Character length: 182\n"]}], "source": ["from llama_index.core.node_parser import TokenTextSplitter\n", "\n", "# Token-based chunking\n", "token_splitter = TokenTextSplitter(\n", "    chunk_size=40,  # Target chunk size (in tokens)\n", "    chunk_overlap=10  # Overlap between chunks (in tokens)\n", ")\n", "\n", "token_nodes = token_splitter.get_nodes_from_documents([document])\n", "\n", "print(f\"Token Splitting created {len(token_nodes)} chunks\\n\")\n", "print(\"Example chunks:\")\n", "for i in range(min(3, len(token_nodes))):\n", "    print(f\"\\nChunk {i}:\")\n", "    print(textwrap.fill(token_nodes[i].text[:300], 100))\n", "    print(f\"Character length: {len(token_nodes[i].text)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Metadata length (0) is close to chunk size (40). Resulting chunks are less than 50 tokens. Consider increasing the chunk size or decreasing the size of your metadata to avoid this.\n", "Hierarchical Splitting created 19 chunks\n", "\n", "Example chunks:\n", "\n", "Chunk 0:\n", "# Introduction to Vector Databases  Vector databases are specialized database systems designed to\n", "store and query vector embeddings efficiently. Unlike traditional databases optimized for exact\n", "matches, vector databases excel at similarity searches.\n", "Character length: 249\n", "\n", "Chunk 1:\n", "Unlike traditional databases optimized for exact matches, vector databases excel at similarity\n", "searches.  ## Key Advantages  Vector databases offer several advantages for AI applications: -\n", "Efficient similarity search using algorithms like HNSW and IVF - Support for high-dimensional vector\n", "data - Op\n", "Character length: 443\n", "\n", "Chunk 2:\n", "Adding vectors with associated metadata 2. Searching for similar vectors using distance metrics 3.\n", "Filtering results based on metadata 4. Building and optimizing indexes for faster retrieval  #\n", "Performance Considerations  When working with vector databases at scale, consider: - Index\n", "construction ti\n", "Character length: 343\n"]}], "source": ["from llama_index.core.node_parser import HierarchicalNodeParser\n", "\n", "# Hierarchical chunking\n", "hierarchical_splitter = HierarchicalNodeParser.from_defaults(\n", "    chunk_sizes=[70, 60, 40]  # Multi-level chunking\n", ")\n", "\n", "hierarchical_nodes = hierarchical_splitter.get_nodes_from_documents([document])\n", "\n", "print(f\"Hierarchical Splitting created {len(hierarchical_nodes)} chunks\\n\")\n", "print(\"Example chunks:\")\n", "for i in range(min(3, len(hierarchical_nodes))):\n", "    print(f\"\\nChunk {i}:\")\n", "    print(textwrap.fill(hierarchical_nodes[i].text[:300], 100))\n", "    print(f\"Character length: {len(hierarchical_nodes[i].text)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Markdown-aware Splitting created 4 chunks\n", "\n", "Example chunks with their headings:\n", "\n", "Chunk 0 (Heading: No heading):\n", "# Introduction to Vector Databases  Vector databases are specialized database systems designed to\n", "store and query vector embeddings efficiently. Unlike traditional databases optimized for exact\n", "matche\n", "\n", "Chunk 1 (Heading: No heading):\n", "## Key Advantages  Vector databases offer several advantages for AI applications: - Efficient\n", "similarity search using algorithms like HNSW and IVF - Support for high-dimensional vector data -\n", "Optimize\n", "\n", "Chunk 2 (Heading: No heading):\n", "## Common Operations  The most common operations in vector databases include: 1. Adding vectors with\n", "associated metadata 2. Searching for similar vectors using distance metrics 3. Filtering results ba\n"]}], "source": ["from llama_index.core.node_parser import MarkdownNodeParser\n", "\n", "# Structure-aware chunking for Markdown\n", "markdown_splitter = MarkdownNodeParser()\n", "\n", "markdown_nodes = markdown_splitter.get_nodes_from_documents([document])\n", "\n", "print(f\"\\nMarkdown-aware Splitting created {len(markdown_nodes)} chunks\\n\")\n", "print(\"Example chunks with their headings:\")\n", "for i in range(min(3, len(markdown_nodes))):\n", "    heading = markdown_nodes[i].metadata.get('heading', 'No heading')\n", "    print(f\"\\nChunk {i} (Heading: {heading}):\")\n", "    print(textwrap.fill(markdown_nodes[i].text[:200], 100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Let's try the different strategies!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Collecting llama-index-embeddings-huggingface\n", "  Using cached llama_index_embeddings_huggingface-0.5.2-py3-none-any.whl.metadata (767 bytes)\n", "Collecting huggingface-hub>=0.19.0 (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface)\n", "  Using cached huggingface_hub-0.29.3-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: llama-index-core<0.13.0,>=0.12.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-embeddings-huggingface) (0.12.27)\n", "Collecting sentence-transformers>=2.6.1 (from llama-index-embeddings-huggingface)\n", "  Using cached sentence_transformers-4.0.1-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: filelock in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.18.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2025.3.0)\n", "Requirement already satisfied: packaging>=20.9 in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (6.0.2)\n", "Requirement already satisfied: requests in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (4.67.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (4.13.0)\n", "Requirement already satisfied: aiohttp in /home/<USER>/.local/lib/python3.12/site-packages (from huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.11.14)\n", "Requirement already satisfied: SQLAlchemy>=1.4.49 in /home/<USER>/.local/lib/python3.12/site-packages (from SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2.0.40)\n", "Requirement already satisfied: banks<3.0.0,>=2.0.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2.1.0)\n", "Requirement already satisfied: dataclasses-json in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.6.7)\n", "Requirement already satisfied: deprecated>=1.2.9.3 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.2.18)\n", "Requirement already satisfied: <PERSON><PERSON><PERSON><2.0.0,>=1.0.8 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.0.8)\n", "Requirement already satisfied: filetype<2.0.0,>=1.2.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.2.0)\n", "Requirement already satisfied: httpx in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.28.1)\n", "Requirement already satisfied: nest-asyncio<2.0.0,>=1.5.8 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.6.0)\n", "Requirement already satisfied: networkx>=3.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.4.2)\n", "Requirement already satisfied: nltk>3.8.1 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.9.1)\n", "Requirement already satisfied: numpy in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2.2.4)\n", "Requirement already satisfied: pillow>=9.0.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (11.1.0)\n", "Requirement already satisfied: pydantic>=2.8.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2.11.1)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.2.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (9.0.0)\n", "Requirement already satisfied: tiktoken>=0.3.3 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.9.0)\n", "Requirement already satisfied: typing-inspect>=0.8.0 in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.9.0)\n", "Requirement already satisfied: wrapt in /home/<USER>/.local/lib/python3.12/site-packages (from llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.17.2)\n", "Collecting transformers<5.0.0,>=4.41.0 (from sentence-transformers>=2.6.1->llama-index-embeddings-huggingface)\n", "  Using cached transformers-4.50.3-py3-none-any.whl.metadata (39 kB)\n", "Collecting torch>=1.11.0 (from sentence-transformers>=2.6.1->llama-index-embeddings-huggingface)\n", "  Using cached torch-2.6.0-cp312-cp312-manylinux1_x86_64.whl.metadata (28 kB)\n", "Requirement already satisfied: scikit-learn in /home/<USER>/.local/lib/python3.12/site-packages (from sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (1.6.1)\n", "Requirement already satisfied: scipy in /home/<USER>/.local/lib/python3.12/site-packages (from sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (1.15.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (6.2.0)\n", "Requirement already satisfied: propcache>=0.2.0 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /home/<USER>/.local/lib/python3.12/site-packages (from aiohttp->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (1.18.3)\n", "Requirement already satisfied: griffe in /home/<USER>/.local/lib/python3.12/site-packages (from banks<3.0.0,>=2.0.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.7.1)\n", "Requirement already satisfied: jinja2 in /home/<USER>/.local/lib/python3.12/site-packages (from banks<3.0.0,>=2.0.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.1.6)\n", "Requirement already satisfied: click in /home/<USER>/.local/lib/python3.12/site-packages (from nltk>3.8.1->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (8.1.8)\n", "Requirement already satisfied: joblib in /home/<USER>/.local/lib/python3.12/site-packages (from nltk>3.8.1->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /home/<USER>/.local/lib/python3.12/site-packages (from nltk>3.8.1->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2024.11.6)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/.local/lib/python3.12/site-packages (from pydantic>=2.8.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.0 in /home/<USER>/.local/lib/python3.12/site-packages (from pydantic>=2.8.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (2.33.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/.local/lib/python3.12/site-packages (from pydantic>=2.8.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/.local/lib/python3.12/site-packages (from requests->huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.local/lib/python3.12/site-packages (from requests->huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.local/lib/python3.12/site-packages (from requests->huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.local/lib/python3.12/site-packages (from requests->huggingface-hub>=0.19.0->huggingface-hub[inference]>=0.19.0->llama-index-embeddings-huggingface) (2025.1.31)\n", "Requirement already satisfied: greenlet>=1 in /home/<USER>/.local/lib/python3.12/site-packages (from SQLAlchemy>=1.4.49->SQLAlchemy[asyncio]>=1.4.49->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.1.1)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface)\n", "  Using cached nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (10.3.5.147)\n", "Collecting nvidia-cusolver-cu12==******** (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface)\n", "  Using cached nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (3.2.0)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (75.6.0)\n", "Requirement already satisfied: sympy==1.13.1 in /home/<USER>/.local/lib/python3.12/site-packages (from torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/.local/lib/python3.12/site-packages (from sympy==1.13.1->torch>=1.11.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (1.3.0)\n", "Collecting tokenizers<0.22,>=0.21 (from transformers<5.0.0,>=4.41.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface)\n", "  Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Requirement already satisfied: safetensors>=0.4.3 in /home/<USER>/.local/lib/python3.12/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (0.5.3)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /home/<USER>/.local/lib/python3.12/site-packages (from typing-inspect>=0.8.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.0.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /home/<USER>/.local/lib/python3.12/site-packages (from dataclasses-json->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.26.1)\n", "Requirement already satisfied: anyio in /home/<USER>/.local/lib/python3.12/site-packages (from httpx->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /home/<USER>/.local/lib/python3.12/site-packages (from httpx->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /home/<USER>/.local/lib/python3.12/site-packages (from httpcore==1.*->httpx->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.14.0)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /home/<USER>/.local/lib/python3.12/site-packages (from scikit-learn->sentence-transformers>=2.6.1->llama-index-embeddings-huggingface) (3.6.0)\n", "Requirement already satisfied: sniffio>=1.1 in /home/<USER>/.local/lib/python3.12/site-packages (from anyio->httpx->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (1.3.1)\n", "Requirement already satisfied: colorama>=0.4 in /home/<USER>/.local/lib/python3.12/site-packages (from griffe->banks<3.0.0,>=2.0.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (0.4.6)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/.local/lib/python3.12/site-packages (from jinja2->banks<3.0.0,>=2.0.0->llama-index-core<0.13.0,>=0.12.0->llama-index-embeddings-huggingface) (3.0.2)\n", "Using cached llama_index_embeddings_huggingface-0.5.2-py3-none-any.whl (8.9 kB)\n", "Using cached huggingface_hub-0.29.3-py3-none-any.whl (468 kB)\n", "Using cached sentence_transformers-4.0.1-py3-none-any.whl (340 kB)\n", "Using cached torch-2.6.0-cp312-cp312-manylinux1_x86_64.whl (766.6 MB)\n", "Using cached nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "Using cached nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "Using cached transformers-4.50.3-py3-none-any.whl (10.2 MB)\n", "Using cached tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)\n", "Installing collected packages: nvidia-cudnn-cu12, nvidia-cusolver-cu12, huggingface-hub, torch, tokenizers, transformers, sentence-transformers, llama-index-embeddings-huggingface\n", "Successfully installed huggingface-hub-0.29.3 llama-index-embeddings-huggingface-0.5.2 nvidia-cudnn-cu12-******** nvidia-cusolver-cu12-******** sentence-transformers-4.0.1 tokenizers-0.21.1 torch-2.6.0 transformers-4.50.3\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["# Special lib needed for local embeddings\n", "%uv pip install --user llama-index-embeddings-huggingface"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "RETRIEVAL COMPARISON\n", "\n", "Sentence chunking result:\n", "common operations in vector databases include: 1. Adding vectors with associated metadata 2.\n", "Searching for similar vectors using distance metrics 3. Filtering results based on metadata 4.\n", "\n", "Token chunking result:\n", "generation (RAG) applications  ## Common Operations  The most common operations in vector databases\n", "include: 1. Adding vectors with associated metadata 2. Searching for similar vectors using distance\n", "\n", "Markdown-aware chunking result:\n", "## Common Operations  The most common operations in vector databases include: 1. Adding vectors with\n", "associated metadata 2. Searching for similar vectors using distance metrics 3. Filtering results\n", "based on metadata 4. Building and optimizing indexes for faster retrieval\n"]}], "source": ["from llama_index.core import VectorStoreIndex\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "\n", "# Create a local embedding model\n", "local_embed_model = HuggingFaceEmbedding(model_name=\"all-MiniLM-L6-v2\")\n", "\n", "# Create vector stores with different chunking strategies (using local embeddings)\n", "sentence_index = VectorStoreIndex(\n", "    sentence_nodes, embed_model=local_embed_model)\n", "token_index = VectorStoreIndex(token_nodes, embed_model=local_embed_model)\n", "markdown_index = VectorStoreIndex(\n", "    markdown_nodes, embed_model=local_embed_model)\n", "\n", "# Query to test retrieval\n", "query = \"What are the top operations in vector databases?\"\n", "\n", "# Get retrieval results\n", "sentence_results = sentence_index.as_retriever().retrieve(query)\n", "token_results = token_index.as_retriever().retrieve(query)\n", "markdown_results = markdown_index.as_retriever().retrieve(query)\n", "\n", "# Compare top results\n", "print(\"\\nRETRIEVAL COMPARISON\\n\")\n", "print(\"Sentence chunking result:\")\n", "print(textwrap.fill(sentence_results[0].node.text[:300], 100))\n", "\n", "print(\"\\nToken chunking result:\")\n", "print(textwrap.fill(token_results[0].node.text[:300], 100))\n", "\n", "print(\"\\nMarkdown-aware chunking result:\")\n", "print(textwrap.fill(markdown_results[0].node.text[:300], 100))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusions\n", "\n", "1. **Chunk Size Tradeoffs**: Smaller chunks allow for more precise retrieval but may lose context. Larger chunks preserve more context but might introduce noise and use more tokens from your LLM's context window.\n", "\n", "2. **Overlap Between Chunks**: dding overlap ensures that sentences or ideas that cross chunk boundaries aren't lost, but increases storage requirements and can create duplicate information in retrieval.\n", "\n", "3. **Structure Awareness**: Domain-specific chunking that understands document structure (like our markdown example) typically produces better results but requires more specialized processing."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}