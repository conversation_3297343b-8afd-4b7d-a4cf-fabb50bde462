# Sample Data Files

This directory contains sample data files in various formats for testing and demonstration purposes.

## Files Overview

- **csv-data.csv**: Tabular data in CSV format
- **json-data.json**: Structured data in JSON format
- **docx-report.docx**: Sample report in Microsoft Word format
- **pdf-report.pdf**: Sample report in PDF format
- **txt-report.txt**: Sample report in plain text format

## File Descriptions

### data.csv
A comma-separated values file containing tabular data. This can be used for testing data loading and processing capabilities.

### data.json
A JSON file containing structured data that can be used for testing JSON parsing and processing.

### docx-report.docx
A Microsoft Word document containing a sample report with text, possibly including formatting, tables, and other document elements.

### pdf-report.pdf
A PDF version of the sample report, useful for testing PDF parsing and text extraction.

### txt-report.txt
A plain text version of the sample report, providing the simplest format for text processing.