{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Understanding Text Embeddings: From Words to Vectors\n", "\n", "This notebook explores text embeddings, a fundamental concept in natural language processing. We'll investigate how computers understand semantic meaning by converting words and sentences into numerical vectors.\n", "\n", "**What Are Embeddings?**\n", "- Dense numerical representations of data in a continuous vector space\n", "- Similar meanings are positioned close together \n", "- Relative positions capture semantic relationships"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 8ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# Set up matplotlib\n", "try:\n", "    plt.style.use('seaborn-v0_8-whitegrid')\n", "except:\n", "    try:\n", "        plt.style.use('seaborn-whitegrid')  # Fallback for older versions\n", "    except:\n", "        pass  # Default style if neither is available\n", "        \n", "plt.rcParams['figure.figsize'] = (10, 7)\n", "np.random.seed(42)  # For reproducibility"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Loading an Embedding Model\n", "\n", "We'll use the `all-MiniLM-L6-v2` model, which creates 384-dimensional embeddings and is optimized for semantic similarity tasks."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: all-MiniLM-L6-v2\n", "Embedding dimension: 384\n"]}], "source": ["from sentence_transformers import SentenceTransformer\n", "# Load the pre-trained model\n", "model = SentenceTransformer('all-MiniLM-L6-v2')\n", "print(f\"Model: all-MiniLM-L6-v2\")\n", "print(f\"Embedding dimension: {model.get_sentence_embedding_dimension()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Creating and Examining Embeddings\n", "\n", "Let's create embeddings for some example sentences grouped by topic."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sentence 1 (AI/ML): I love machine learning and artificial intelligence.\n", "Sentence 2 (AI/ML): AI and ML are fascinating fields of study.\n", "Sentence 3 (Weather): The weather is beautiful today.\n", "Sentence 4 (Weather): It's a sunny day with clear skies.\n", "Sentence 5 (Python): Python is my favorite programming language.\n", "Sentence 6 (Python): I enjoy coding in Python for data analysis.\n"]}], "source": ["# Example sentences grouped by topic\n", "sentences = [\n", "    # AI/ML related sentences\n", "    \"I love machine learning and artificial intelligence.\",\n", "    \"AI and ML are fascinating fields of study.\",\n", "    \n", "    # Weather related sentences\n", "    \"The weather is beautiful today.\",\n", "    \"It's a sunny day with clear skies.\",\n", "    \n", "    # Python related sentences\n", "    \"Python is my favorite programming language.\",\n", "    \"I enjoy coding in Python for data analysis.\"\n", "]\n", "\n", "# Topic labels for visualization\n", "topics = ['AI/ML', 'AI/ML', 'Weather', 'Weather', 'Python', 'Python']\n", "\n", "# Display our sentences with their topics\n", "for i, (sentence, topic) in enumerate(zip(sentences, topics)):\n", "    print(f\"Sentence {i+1} ({topic}): {sentence}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of each embedding: (384,)\n", "Number of embeddings: 6\n", "\n", "First 10 dimensions of first embedding: [-0.00239476 -0.07797179  0.0768146   0.00246213  0.06795019 -0.06487745\n", " -0.03318882  0.00960123  0.03197829  0.03624603]\n", "Min: -0.1715, Max: 0.1487, Mean: 0.0017\n"]}], "source": ["# Create embeddings for our sentences\n", "embeddings = model.encode(sentences)\n", "\n", "# Display embedding information\n", "print(f\"Shape of each embedding: {embeddings[0].shape}\")\n", "print(f\"Number of embeddings: {len(embeddings)}\")\n", "\n", "# Show a snippet of the first embedding\n", "print(f\"\\nFirst 10 dimensions of first embedding: {embeddings[0][:10]}\")\n", "print(f\"Min: {embeddings[0].min():.4f}, Max: {embeddings[0].max():.4f}, Mean: {embeddings[0].mean():.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Measuring Similarity with Cosine Similarity\n", "\n", "**Cosine Similarity Explained:**\n", "- Measures the cosine of the angle between two vectors\n", "- Ranges from -1 (opposite) to 1 (identical)\n", "- Higher values indicate greater semantic similarity"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cosine Similarity Matrix:\n", "[[1.     0.6934 0.0295 0.0339 0.5063 0.4714]\n", " [0.6934 1.     0.0107 0.0368 0.2927 0.3494]\n", " [0.0295 0.0107 1.     0.6957 0.0592 0.0082]\n", " [0.0339 0.0368 0.6957 1.     0.0369 0.0168]\n", " [0.5063 0.2927 0.0592 0.0369 1.     0.7249]\n", " [0.4714 0.3494 0.0082 0.0168 0.7249 1.    ]]\n"]}], "source": ["# Calculate cosine similarity between all pairs of embeddings\n", "similarity_matrix = cosine_similarity(embeddings)\n", "\n", "# Display the similarity matrix\n", "print(\"Cosine Similarity Matrix:\")\n", "np.set_printoptions(precision=4, suppress=True)\n", "print(similarity_matrix)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Heatmap Interpretation:\n", "- The diagonal (1.0 values) shows each sentence's similarity with itself\n", "- Brighter blocks show high similarity between sentences on the same topic\n", "- Darker areas show lower similarity between sentences on different topics\n"]}], "source": ["# Create labels for our heatmap\n", "labels = [f\"S{i+1}: {topic}\" for i, topic in enumerate(topics)]\n", "\n", "# Create a heatmap of the similarity matrix\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(similarity_matrix, annot=True, cmap='viridis', xticklabels=labels, yticklabels=labels)\n", "plt.title('Cosine Similarity Heatmap')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Heatmap Interpretation:\")\n", "print(\"- The diagonal (1.0 values) shows each sentence's similarity with itself\")\n", "print(\"- Brighter blocks show high similarity between sentences on the same topic\")\n", "print(\"- Darker areas show lower similarity between sentences on different topics\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualizing Embeddings in 2D Space\n", "\n", "We'll use PCA to reduce our 384-dimensional embeddings to 2D for visualization."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAMWCAYAAAAgRDUeAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAykBJREFUeJzs3Xd4VNXe9vF7EhJSIQRCNYBBE0InEDqioUrvIE1pihQVRQXFgg8IWA6o4LEjRaV3EBGCKL2EIi2KEAgdAoRU0vb7B2/mEJPAZJiEZPh+rovrZPZea89v76zJ88zt2mubDMMwBAAAAAAAAOQhh/tdAAAAAAAAAB48hFIAAAAAAADIc4RSAAAAAAAAyHOEUgAAAAAAAMhzhFIAAAAAAADIc4RSAAAAAAAAyHOEUgAAAAAAAMhzhFIAAAAAAADIc4RSAAAAAAAAyHOF7ncBAICCKSEhQVu3blVoaKj27t2rc+fOydHRUeXLl1erVq00cOBAubu7Z+rXv39/7dq1y/zawcFBHh4e8vLyUuXKldWgQQN17NhRnp6eOarnzJkzat68eYZtDg4OKlKkiKpWrapevXqpdevW2fb//ffftWLFCu3bt09RUVGSpNKlS6tWrVrq1KmTGjVqlG3ftWvXavTo0ZKkqVOnqnPnzjmqXZJ27typAQMGZNjm6OioYsWKqUaNGnr66afVoEGDHB/Xmhq6dOmiKVOm5Op75VRISIjOnj2r8PDw+12KTWzdulUzZszQsWPHFB8fL0kWnVtERIS+++477dixQ+fPn1ehQoXk7e2tChUqqG7dumrXrp0qVKiQ2+UjC0uXLtW4cePu2m7OnDmqX79+rtSQ/newXr16mjt3bq68hyXSr8XIkSM1atQoi/pkV7s1xwIAFByEUgAAq6xevVrjx4+XJFWqVEkhISGKjY3Vvn379Nlnn2nNmjWaN2+eihcvnmX/Jk2ayMfHR5IUFxen8+fPa9OmTVq/fr0+/vhjjR8/Xl27ds1xXW5ububwKTk5WSdOnNDWrVu1detWDR06VGPGjMnQPjY2Vq+88op+++03mUwmBQQEqFq1apKkkydPavny5Vq+fLm6deum999/P8v3XLlyZYafrQml0pUoUUJNmzaVJN28eVNHjx5VaGioNm3apLffflt9+vSx+tj5VX75Ip1Xzp07p5EjRyo5OVkNGzbM9jPyb1u2bNGIESOUmJgoHx8fNWjQQEWKFNGFCxe0d+9ebd26VYUKFdKzzz6by2eQ2dixY7Vs2bJcDVwKivLly6tOnTrZ7i9RokQeVgMAQP5GKAUAsEqhQoXUq1cvPf3006pUqZJ5+6VLl/Tcc8/pyJEjev/99/Xxxx9n2f/ZZ5/N9OU1JiZG33//vf773/9q3LhxSklJUc+ePXNUV7FixTLN8kn/L+3ffPONOnTooICAAElSSkqKnn32We3du1c1a9bUpEmT9Oijj2boe/LkSU2bNk2RkZFZvt/Vq1f1xx9/yNXVVY6Ojtq+fbsuXryoUqVK5ajudH5+fhnqNwxDM2fO1GeffaapU6eqdevWFocYOVWjRg2tXbs2x7PU8sL333+v5OTk+12GTWzbtk3x8fEaPny4XnzxRYv6JCYm6rXXXlNiYqKGDRumkSNHysnJybw/Pj5eGzdulKura26VDQvVqVMn3800LMhatmypmjVrqlixYve7FABALmBNKQCAVbp06aL33nsvQyAlSSVLltTbb78tSVq/fr2SkpIsPqanp6dGjRqlyZMnS5ImTpyoK1eu3HOtXbt2Vf369WUYhjZu3Gje/v3332vv3r169NFHNXv27EyBlCQ9/PDD+vTTT7MND9asWaOUlBS1aNFCrVu3VlpamlavXn3PNaczmUwaPny4ypcvr8TERG3ZssVmx/43V1dXVapUSSVLlsy197BW+fLlM421gurChQuSJF9fX4v77N27V1FRUSpVqpRGjx6dIZCSbs0Q7NChg1q0aGHTWoH7zdPTU5UqVZK3t/f9LgUAkAsIpQAANle5cmVJUlJSkq5fv57j/p06dVKdOnV08+ZNLVy40CY1ValSRdL/AoHU1FR9//33kqTXXnvtrjNM6tatm+X29Fv3OnbsqI4dO0qSVqxYYYuSzRwcHMzXNL3+nTt3KiAgQGPHjtXly5f15ptv6rHHHlOVKlXM5yVJ+/bt0/PPP68GDRqoWrVqCgkJ0bvvvquLFy9mep/bj5mV33//Xc8++6z5WM2bN9fkyZN17dq1LNsbhqHVq1dr4MCBql+/vqpXr66QkBC9+OKL2r59uyTps88+M68FtmvXLgUEBJj/3V5HSEiIeYbbv+XkHJcuXaqAgAB99tlnOnfunF555RU1aNBANWrUUNeuXRUaGprle9xJQkKCZs6cqfbt26tGjRqqU6eO+vbtqzVr1mRol359P/vsM0nSuHHjzOeavi07V69elSSrvpin/x4GDBig4OBgVa9eXU8++aQ+++wzJSQkZGrfv39/BQQE6MyZM9qwYYN69uypWrVqqV69enr55ZfNYzBdQECAli1bJkkaMGBAht/hmTNnMrTNyRgaO3asAgICtHPnTu3evVsDBgxQ7dq1FRQUpGeffVbHjx/P9px///13DRs2TI0aNVK1atXUrFkzPffcc/rll18ytb1+/bo+/vhjtW3b1vz7GzBggDZt2mTR9b0Xt4/H06dP68UXX1T9+vUVFBSkIUOGmM8xJSVFX3zxhVq3bq3q1aurZcuW+uGHH+547NjYWE2cOFHNmjUz/86///57paWlZdk+ISFBX375pTp37qzatWurdu3a6tmzp/l3m5W9e/fqmWeeUe3atVW3bl0NHjxYBw4cuGNd58+f16uvvqoGDRqoZs2a6tq16x3/Zt5+jW5n7fiIj4/XRx99pJCQEPO1nDlzppKTk7P9OxMWFqbhw4friSeeULVq1dS4cWN1795dH330keLi4u54vgCAO+P2PQCAzaXf6ubk5CQvLy+rjtGuXTvt3btXO3fu1PDhw++5pvQvDs7OzpKkI0eO6PLly/Ly8lKTJk2sOubJkyd18OBBlShRQo0bN5aDg4NKly6t8PBwhYeHZxui2KL+dFevXlX37t2VmpqqoKAgJSUlmQO2FStWaNy4ceZ9ZcqU0eHDh/XTTz/p119/1Zw5cyyeffTRRx/p66+/lpOTk6pXry4fHx+Fh4fr+++/V2hoqH766acMa+Wkpqbq5Zdf1rp16+Tk5KSgoCCVKFFC58+f1+bNm83rKQUGBqp169b65ZdfMqynJemO6/Kks/Ycz549q+7du8vd3V0NGzbU+fPntW/fPo0YMUJff/21xWMiNjZWAwYM0OHDh+Xt7a3HH39cCQkJ2rFjh/bs2aN9+/aZ114rUaKEunTpoqNHj+rYsWMKCgoyL0oeGBh4x/cpU6aMJOmvv/7Snj17sg1J/y0tLU2vvvqqVq9eLTc3N1WrVk1FixbVoUOHNGPGDP3++++aO3euXFxcMvX98ccf9f3336tOnTp67LHHdPDgQa1Zs0aHDx/WihUrzH26dOmivXv36vTp0xnWipNuzeBKl9MxlG7Tpk2aM2eOOVw6evSoNm/erAMHDmj16tUZ3k+SpkyZolmzZsnBwUG1atVS2bJldenSJYWFhenChQsZHnhw8uRJDRw4UOfPn1e5cuXUpEkTxcXF6cCBAxo2bJhee+01DR482KJrfS/OnDmjHj16qHjx4mrUqJGOHz+uP/74Q4cPH9bKlSv1zjvvaNeuXapfv74eeugh7dy5U++9956cnJyyvMU5KSlJAwYMUGRkpBo0aKDk5GRt375dkydP1rFjxzLdXhgVFaWBAwcqPDxcPj4+Cg4OlmEY2rdvn8aOHatDhw7prbfeytBn06ZNGjlypFJSUlSjRg35+vrq2LFj6tu3b7brAUZGRuqpp57S5cuX5evrq0aNGunixYt6/fXX1bdvX6uuXU7GR1JSkgYOHKj9+/fLy8tLTzzxhJKSkvT111/ryJEjWR4/NDRUI0aMkGEYqlGjhmrXrq0bN27o1KlT+vrrr9W7d+8sH+oBALCQAQCAjb355puGv7+/8dxzz2Xa169fP8Pf39/YsWPHHY+xZ88ew9/f32jcuLFF7xkZGWn4+/sbTzzxRKZ9iYmJxhNPPGH4+/sbCxcuNAzDMBYuXGj4+/sbTz/9tEXHz8q0adMMf39/Y+LEieZtH3zwgeHv729MnTo1R8fasWOH4e/vb/Tr1y/TvitXrhhBQUGGv7+/sXXr1gzt/f39jREjRhiJiYkZ+pw7d86oUaOGERgYaGzYsMG8PTU11Zg0aZLh7+9vdO3aNcsaXn/99Qzb165da/j7+xvt27c3IiIizNvT0tKMTz75xPD39zdeeumlDH1mzpxp+Pv7G23btjVOnz6dYd+NGzeMnTt3ml+n/+6yOvd06b+/ez3HJUuWmK/blClTjNTUVPO+WbNmGf7+/kafPn2yrePf3nvvPcPf39/o37+/ERMTY95+/Phxo2HDhoa/v78RGhqaoc+nn35q+Pv7G0uWLLH4fVJSUoy2bdsa/v7+RmBgoPHss88as2bNMnbt2mXEx8dn2+/rr782X9tLly6Zt9+8edN44403DH9/f+PDDz/M0Cf9M1qzZk0jLCzMvD0+Pt7o1auX4e/vbyxatChDn9dff/2On2trxlD6MStXrmz8+uuvGa7FqFGjDH9/f2P69OkZ+ixfvtzw9/c3mjRpYhw5ciTDvoSEBGPLli0ZjtO+fXvD39/f+PrrrzOMhYiICCMkJMQIDAw0wsPDszynf0sfW//+/FjSx9/f3/joo4+MtLQ0wzBuXZexY8eaP0Pt27c3oqKizP22bduW5d+79M+Sv7+/0aFDhwx9Tp06ZTRp0sTw9/fPcD0NwzCGDh1q/lt28+ZN8/bLly8bXbt2Nfz9/Y3Nmzebt8fExBgNGjQw/P39jcWLF5u3p6WlGR9++KG5hk8//TTD+wwePNjw9/c3xo0bZyQnJ5u3b9y40QgMDMzy70D6Nfr3sawZH+l/l7p3725ER0dnuG7NmjUz1327vn37Gv7+/sa6deuMfztw4ECGzz0AIOe4fQ8AYFObN2/W4sWL5eTkpJdeesnq46Qvanvjxg2rj5GcnKzw8HC98MILOnv2rLy8vNSmTRtJMt8uZO06JYZhaNWqVZJkvm3v9p9Xr16d7W0ylrp586YOHDig559/XrGxsXr44YczLQ7v7Oyst956S4ULF86wfdGiRUpMTNSTTz5pvj1OunUr4JgxY1SyZEkdOnRIe/fuvWsdX3zxhSTp448/Ns/skW6tdzVq1CgFBgbql19+Md9ilpSUpFmzZkmS3n///UxrJ3l6eqpevXo5uBJZu5dzfOihhzR69Gg5OPzv/xXq16+fihYtqgMHDli0Flp8fLwWL14sBwcHvfPOO/Lw8DDvq1Spkp5//nlJ0pw5c+7lNCVJjo6O+vLLL1W7dm2lpqbqt99+0+TJk9WvXz8FBwdr2LBh+vPPPzP0SUlJ0TfffCM3NzdNmzYtw4yR9HHj4+OjhQsXZjlWn376adWuXdv82tXVVQMHDpQk7dmzJ0f153QM3a5du3YZ1spydHQ0P2Hw33V8+eWXkm7dGvnv2WcuLi5q3Lix+fWmTZv0119/qXXr1hoyZEiGsVChQgWNHTtWqampOb6FeNmyZRluYbz9X3Yz3Hx9ffXCCy/IZDJJunVdnn76aUnS8ePH9cYbb2T4W9WwYUNVqVJFZ8+ezXSLZLrXX389Q5/y5cubZ53OmzfPvD19ZlH16tU1bty4DLMxS5Qoof/7v/+TJP3000/m7em/q+DgYHXr1s283WQy6cUXX1Tp0qUz1RMZGak//vhDHh4eGjdunAoV+t8NGyEhIRlmsOVETsbH/PnzJd26NkWKFDFvf+ihhzRixIgsj58+Jhs1apRpX40aNTJ87gEAOUcoBQCwmX/++UevvvqqDMPQq6++al4HyRqGYUiS+Uuapc6ePWv+AlitWjV17NhRv/32m0qUKKHPP//cZk+W27t3r86cOSM/Pz9Vr17dvD0gIECVK1fWxYsXtWPHjhwf9/Z1lWrUqKGePXvqwIEDqlChgmbOnClHR8cM7atWrZrlk/7Sv4x16NAh0z5nZ2dzOHe3UCoqKkrHjh1TxYoV5e/vn2m/yWRSUFCQUlNTdfjwYUnSoUOHdOPGDVWuXFk1a9a07MStcC/nWK9evUy3QhYqVEgPPfSQkpOTLVoL7fDhw0pMTFSVKlWyvEWwU6dOkm6tR3OvAaV064vz/Pnz9dNPP2no0KGqV6+e3NzclJycrE2bNql3795au3atuf2RI0d07do11a5dO8vb4lxcXFS1alVFR0crIiIi0/6sbmGsWLGiJOny5csW123NGLK0jkuXLpm3Xbx4Uf/884+KFCmiJ5988q51pT80oGXLllnuT7999N9h392UL19eXbp0yfJf+/bts+xTr169TIvXp4e5Tk5OWYa46fuz+l14eXllCODSpb//vn37zGMy/Tq0aNEiQzCXrkqVKnJzc8twHdI/e23bts3U3snJKcuAKf1z2LRp0yz/Drdr1y7TNktYOj7Onj2rixcvysfHJ8twMLsxU7VqVUnSq6++qoMHD9rkswwA+B/WlAIA2MTFixc1dOhQRUdHa+DAgeb/ym+t9JlMRYsWzVE/Nzc38xciBwcHFSlSRFWqVFHLli0zLGaePhMrq5kZlrh9gfN/69ixo44dO6aVK1dm+V/X7+T2dZUcHR3l5eWlWrVq6fHHH8/0pVX631pD/5b+ZaxcuXJZ7k/fntVi4Lc7e/asJCkiIuKua2Sl/87SF8IuX778Hdvfq3s5x6xmckgyrw1jyUypu71/kSJF5OnpqZiYGEVHR9vskfZBQUEKCgoy17lt2zZ9+OGHOn78uN555x01a9ZM7u7u5hk0W7dutfh3d7usrlFOrk86a8bQ7bIKXdNnpyQnJ5u33f5UQ0vC7PS6xowZozFjxuSopjupU6dOpjWb7iarc0y/1iVKlMgURkv/W68rq99F2bJls3wfT09PFSlSRDdu3DCPyfTrMG3aNE2bNi3bGm9/H0s/e7dL75NdbQ899FC2730nlo6P9PAuu8++h4eH+drc7uWXX9Zff/2lTZs2adOmTSpatKiCgoLUvHlzdezYMdMsVQBAzhBKAQDu2fXr1zVo0CCdPXtWXbt21euvv37Pxzx69Kgk6ZFHHslRv2LFiln0hTB9FtfRo0dlGEaOZmQlJSXp559/liStXbtWv//+e4b96YuS//LLL3rnnXfu+mS/2/n5+eXoC621X4gsPd/0WQE+Pj53Xfw7uy+b98udzjGrGSF5XYMtODs76/HHH1fVqlXVsmVL3bhxQ/v27VOTJk3Msw0rVKhgDrGyk9UDCWxV+72Oodz6XaXX1bRp0yxnkqWzVZh4J3c6x9weq+nXoU6dOrkeJOeG3L4+ZcqU0ZIlS7Rjxw799ttv2rVrlzmg+uabbzR//vw8GSMAYK8IpQAA9yQuLk5Dhw7V8ePH1apVK02cONEmX2bTb0P69xpKtlKlShX5+Pjo8uXL+uOPP/TYY49Z3HfTpk3m/5r+119/ZdsuPj5eGzZsyPL2stxWsmRJnTx5UufOndOjjz6aaX/67IisZhncLn1WgaVh3+19Tp8+nZOSc8xW53gv7y9J586dy3J/TEyMbty4IRcXlxzP+MspHx8f+fn56fDhw+aZPennndOg09asGUP38j6RkZEWBc3p7Xv06GH1ekb5VXZjMjY21jwm09dUSr8OLVq00KBBgyw6fvrYT/+MWfL+6WuaZVdbdseylfT3T59R92/p1yYrhQoVUpMmTcyh6tmzZ/XGG29ox44d+vrrr/Xaa6/lTtEA8ABgTSkAgNWSkpI0fPhwHTx4UE2aNNHHH3+c5W0mObV8+XKFhYXJ1dVVPXr0sEGlmTk6OuqZZ56RJH3wwQdKSEi4Y/uwsDDzz+m37r399tsKDw/P8l/6l+8VK1bkSv13k75myurVqzPtS0pK0rp16yT9b92c7JQuXVp+fn46fvy4Tp48adF7V6tWTUWKFNGxY8d08ODBu7ZPvy0xJSXFouOns9U5Wqtq1apycXHR4cOHs1yTKX2cBAUF3fNsjvRZT9lJTU3NFMLVqFFDnp6e2rVrl0VrZN2L9N9hampqpn3WjCFrlCpVSpUqVdKNGzfMMxnvJH3NpV9//TXXarpfrl+/ru3bt2favmbNGklSrVq1zH+rrbkO6Z+prK5zSkqK1q9fn22fP/74Q7GxsZn2374eWm4oV66cSpUqpcuXL2f4e54u/e+FpccaOnSoJOnvv/+2WY0A8CAilAIAWCU1NVUvv/yyduzYobp162rGjBmZFo7OqZiYGM2YMUNvvPGGJOmtt96y+ul4lnjmmWdUp04d/f3333rmmWd0/PjxTG0iIyM1evRo81or169f1+bNm+Xo6GheSDsrLVu2VOHChbVt2zZduXIl184hO927d5eLi4vWrl2r3377zbw9LS1N06ZN08WLF1W1alWLApvhw4crLS1NL7zwgvm2yttdu3YtwxPKnJ2dzWuKvfnmm5lmQMTExGjXrl3m18WKFZOTk5MiIyOzDDXy4hyt4ebmpm7duiktLU3vvfee4uPjzftOnjyp//73v5Kk/v373/N7hYaG6sUXX8zyy3R8fLzeffddXb9+XSVLllStWrUk3fo9DBkyRHFxcRo1apQiIyMz9b148aKWL19+z/Wlz5zJLnTK6RiyVvpT16ZMmaJjx45l2Hfz5k1t3brV/LpVq1Z65JFHtGrVKs2cOTPT2kyGYWjv3r0WPaEyP5o6dWqG9bAiIyM1c+ZMSVLfvn3N22vWrKnGjRsrLCxMEyZMyDIwOnbsWIbblNu0aSMvLy/t2rVLy5YtM283DEOfffZZlrOhypcvryZNmig2NlZTpkzJ8FnfvHlzjkIha/Xu3VvSrWsTExNj3n727Fl9/vnnWfb5/vvvs1xMfvPmzZKyX9cPAGAZbt8DAFhl3rx55v+yXqxYMU2YMCHLdq+99lqWwdJXX31l/jITHx+vCxcu6MiRI0pOTpaHh4feeustde7cOdfql27dkvHVV1/p5Zdf1ubNm9W+fXtVrlxZFSpUkGEYOnXqlPmLbc+ePSXd+q/5ycnJatKkiYoXL57tsT08PNSsWTOtX79eq1evNs/Kyitly5bVhAkTNG7cOA0bNkxBQUEqU6aMDh8+rJMnT6pEiRL68MMPLTpWhw4ddPz4cX3xxRfq2rWrAgMD5evrK8MwFBkZqfDwcLm5uZmvkSQNGzZMR48e1YYNG9S6dWvVrVtXxYsX1/nz53XkyBE1atTI/EQxZ2dnNWnSRJs2bVKnTp1UpUoVOTk5KSgoKMPj5nPzHK318ssva//+/dq6datatGih4OBgJSQkaMeOHbp586b69++vkJCQe34fwzC0bt06rVu3Tj4+PgoMDFSRIkV07do1HTp0SNHR0XJzc9MHH3yQIRx+9tlndeLECa1YsUJPPvmkqlSpYn7C4MmTJ3X8+HEFBATc82ftiSee0MyZMzV16lRt3brVvMbOmDFjVKxYMavGkDU6d+6sQ4cOae7cueratatq1aqlMmXK6NKlSzp27JjKli1rnr1YqFAhzZw5U4MHD9ann36qH374QQEBAfL29tb169d19OhRRUVFady4cTkKNvfu3auxY8dmu799+/Z3XVvrXtWqVUvJyclq1aqVGjRooOTkZO3YsUMJCQnq2LGjWrVqlaH9hx9+qCFDhujHH3/U6tWrVblyZZUsWVKxsbEKDw/X+fPnNWDAAPNtzh4eHpo0aZJeeOEFjR07Vj/99JN8fX117NgxnTp1Sj179swyZHz33XfVu3dvLVq0SDt37lT16tV16dIl7dmzR3369NEPP/yQq9dlyJAh2rx5s/bv368WLVqofv36SkpK0s6dO9WgQQMZhpEpgJoxY4amTp2a4f82HDt2TBEREfLy8rL4lkcAQNYIpQAAVrl97Y073fYxcuTILEOp9MeQOzg4yN3dXV5eXnriiSfUoEEDderUyfz0pNzm4eGhr776Sps3b9bKlSu1b98+bdq0SSaTSaVLl1aXLl3UuXNnNWjQQNL/bsmy5PHl7dq10/r167Vy5co8D6WkW1/Qy5cvr6+++kr79u3TwYMH5ePjo6eeekrPP/98tmstZbUWz+jRo9WkSRPNmzdPYWFh+uuvv+Tu7q5SpUrpqaeeyjRrrFChQvrss8+0YsUKLVmyRIcOHdLNmzfl4+Ojxx9/XE899VSG9pMmTdLUqVO1bds2rV69WqmpqUpNTb1jKHUv52grHh4emjdvnr777jv9/PPPCg0NlZOTk6pVq6Y+ffqoffv2Nnmfpk2b6quvvtIff/yh/fv36+jRo7p27ZoKFy6shx56SF27dlX//v0zPfXMwcFBH3zwgVq3bq2FCxfqzz//1JEjR1SkSBGVLl1agwcPVtu2be+5vmrVqunDDz/UrFmztHXrViUmJkqSnn/+eXNAldMxZK3x48erYcOG+vHHH3Xo0CEdPHhQxYsXV506dTKNp4oVK2r58uXmkH3//v1KTU1ViRIlFBgYqJCQED355JM5ev/Tp0/fcT21ypUr53oo5ezsrG+++Ub/+c9/tGHDBl27dk0PPfSQevbsmeWTUYsXL6758+dr4cKFWrNmjY4ePap9+/apRIkS8vX1Vf/+/TP9zWvRooXmzJmjTz/9VH/++af++ecfVa9eXRMnTtTJkyezDKV8fX21cOFC/ec//9GWLVu0YcMG+fn5afLkyQoODs71UMrZ2VnfffedPv/8c61du1ahoaEqXbq0Bg4cqOeee05169bNtOj/+PHj9ccff+jw4cPm2WJlypTRwIEDNXDgwFz/GwMA9s5k3G2RAgAA8EDYvHmznn32WfXp00fvvPPO/S4HAPLM/v371atXLzVt2lTffPPN/S4HAB4YrCkFAAAkSYcOHZKkAvlYeACwxJEjR5SWlpZhW2RkpN5++21JUseOHe9HWQDwwOL2PQAAHnCrVq3SypUrtWXLFjk7O2dabwYA7MXLL7+s2NhY+fv7y8vLS+fOndPhw4eVlJSkkJAQdejQ4X6XCAAPFEIpAAAecAcPHtT27dsVGBiol156KdO6RABgL/r376+ff/5Z4eHhio6OlrOzswIDA9WxY0f17t07yzX1AAC5hzWlAAAAAAAAkOdYUwoAAAAAAAB5jlAKAAAAAAAAeY41pe4gJSVF0dHRKly4sBwcyO8AAAAAAADuJi0tTTdv3lTRokVVqFD20ROh1B1ER0crIiLifpcBAAAAAABQ4FSsWFHFixfPdj+h1B0ULlxY0q2L6Orqep+ryZ8Mw1BsbKw8PDx4WgnyBcYk8hvGJPITxiPyG8Yk8hPGI/KbgjwmExISFBERYc5VskModQfpt+y5urrKzc3tPleTPxmGoeTkZLm5uRW4DwnsE2MS+Q1jEvkJ4xH5DWMS+QnjEfmNPYzJuy2FxEJJAAAAAAAAyHOEUgAAAAAAAMhzhFIAAAAAAADIc4RSAAAAAAAAyHOEUgAAAAAAAMhzhFIAAAAAAADIc4RSAAAAAAAAyHOEUgAAAAAAAMhzhFIAAAAAAADIc4XudwFAdi5cuKAZM2Zo27ZtunTpkgoXLixvb29VqlRJ1apV08iRIyVJO3fu1IABA7I9zsiRIzVq1Ki8KhsAAAAAcD8ZhhQTIyUmSi4ukqenZDLd76qQBUIp5EuXL19W9+7ddfnyZfO25ORkxcbG6vTp0/r999/NoRQAAAAAAIqLkzZulJYulcLDpdRUydFRCgiQunaVmjeX3N3vd5W4DaEU8qV58+aZA6mGDRuqb9++cnNz09mzZ3Xw4EFt2LAhy37jx49XYGBghm1ly5bN9XoBAAAAAPdRWJg0frwUEXFrVpSXl+TkdCuY2r1b2rVLqlhRmjhRCgrKlRL27dunPn36qGnTpvrqq6/M28+cOaPmzZtr+fLlGb6vnj17Vk8++aS2b9+u7777TjNmzFCTJk307bffZjjut99+qw8//FD16tXT3LlzJUmfffaZNmzYoBUrVuTKueQVQinkS4cPHzb/PG7cOAUEBJhf9+zZU2+++WaW/fz9/VW3bt1crw8AAAAAkE+EhUkvvSRduSL5+krOzhn3e3tLSUm3AqvRo6Vp03IlmFq8eLH69eunxYsX6+LFiypVqtQd22/cuFH169eX+/+fveXj46OdO3fqwoULKl26tLndkiVL7HayBQudI19yv21K5fTp07Vnzx4lJSWZt7m6umbZb8yYMapWrZqCg4M1aNAgbdu2LddrBQAAAADcJ3Fxt2ZIXbki+fllDqTSOTvf2n/58q32cXE2LiNOa9eu1VNPPaXHH39cy5Ytu2ufjRs3KiQkxPy6ePHiatKkSYa+Bw8e1LVr19SsWTOb1ptfEEohX2rUqJH559DQUPXt21dBQUF66qmn9N133yk+Pj7LfpcuXVJycrJu3LihrVu3atCgQVq6dGlelQ0AAAAAyEsbN96aAeXre/fFzE2mW+0iIqTQUJuW8fPPP8vPz09+fn7q2LGjlixZIsMwsm1/48YN7d27N0MoJUndunXLEEqtWrVKHTp0kJOTk03rzS8IpZAvde/eXR06dMiwLTk5WWFhYZo6dao6deqk6OhoSZKjo6Pq16+vt956S19//bX+85//qFq1apIkwzD0/vvvZxtiAQAAAAAKKMO4tai5lP0MqX9zdr4VTi1Zcqu/jSxevFgdO3aUJDVt2lQxMTHatWtXtu03b96sgICATLf4Pf7444qNjdXu3bsVHx+vDRs2qFu3bjarM78hlEK+5OjoqI8++kgLFy7UoEGDVKVKFTk4/G+4nj592rz4W926dTVnzhz169dPjz32mNq1a6dZs2bJ09NTkhQTE6N9+/bdl/MAAAAAAOSSmJhbT9krVixn/by8bvWLjbVJGSdOnNCff/6p9u3bS5IKFSqktm3bavHixdn2+fete+mcnJzUsWNHLV26VOvWrVP58uUzrLFsb1joHPlazZo1VbNmTUnSlStXNGHCBK1fv15SxsXQ/61IkSKqUKGCDh06JEm6evVq7hcLAAAAAMg7iYm3nq6X01vbHB2l5GQpIUH6/5MZ7sXixYuVkpKipk2bmrcZhiFnZ2e9/fbbmdonJSXpjz/+0LBhw7I8Xrdu3dSzZ0/99ddfme4gsjfMlEK+tHv3bsX9a+G5EiVKqHPnzubXaWlpkmQOnm5348YNRUREZOgLAAAAALAjLi63AqbU1Jz1S0291S+bB2jlREpKilasWKGxY8dq+fLl5n8rVqxQyZIltXr16kx9du3apaJFi6py5cpZHvPRRx/VI488or///lutW7e+5xrzM2ZKIV9asGCBNm/erDZt2ig4OFglS5ZUVFSUvvjiC3Ob6tWrS5KmTp2qGzduqHPnzgoICNC1a9f03XffKfb/T8UsVqyYateufV/OAwAAAACQSzw9pYAAafduydvb8n7Xr0vBwZKHxz2X8Ntvvyk6Olrdu3c3LyGTrlWrVlq8eHGGGVTSrYd5ZXXr3u1mz56t5OTkOy6WnpiYqKNHj2bY5u7urvLly+fwLO4fQinkWzdu3NDChQu1cOHCTPt8fHzUv39/8+tjx45pypQpmdo5OTlp4sSJcnFxydVaAQAAAAB5zGSSunaVdu2SkpIsW+w8KenWAufdut39aX0WWLx4sRo1apQpkJKk1q1b65tvvjFPmEgXGhqq999//47HdXNzk2EY5gd8ZSUiIiLD3USS1LBhQ33//fcW13+/mYw7xW4PuPj4eB09elSBgYFyc3O73+XkS+kfkqJFi8p0lw+0YRiKiYlRYmKiXFxc5OnpmW2fiIgIbdiwQdu3b9fp06d15coVJScnq0yZMmrcuLGef/5581MKDh48qNWrV2vHjh26ePGiYmNj5e3treDgYA0ZMkRVqlSx+Xkj/8rJmATyAmMS+QnjEfkNYxL5CeOxgIqLk3r0kCIiJD+/OwdNhiGdOCFVrCgtWiS5u+dVlWaHDx/W008/re3bt8vpLmthFeQxaWmewkwp5Lq4uDht3LhRS1cuVfjJcKWmpcrRwVEBDweoa8euat68udz/9cegYsWKGjJkiIYMGXLX49eoUUM1atTIrfIBAAAAAPmVu7s0caI0evStwMnXN+sZU0lJUmSk5OMjTZp0XwIpSUpNTdX48ePvGkg9KAilkKvCwsI0/r3xioiKkMnXJK8mXnJycVJqYqp2/71buz7cpYrfVdTEtycqKCjofpcLAAAAAChogoKkadOk8eNvzZgymSQvr/8tgn79+q1ZUhUr3gqk7uOaw0yqyIhQCrkmLCxML417SVeKXpHvAF85e2RMq70reyspNkkRv0Zo9LjRmjZ5GsEUAAAAACDngoJu3ZIXGiotWSKFh0vJybeCqeDgW2tIhYTctxlSyBqhFHJFXFycxr83XleKXpFfJz+ZHLK+/9XZw1l+nfx0YsUJjX9vvBb9sCjTrXwAAAAAANyVu7vUoYPUvr0UGyslJEiurreeslfA1mR6UDjc7wJgnzZu3KiIqAj5tvTNNpBKZ3IwybelryKiIhQaGppHFQIAAAAA7JLJJHl6SiVL3vpfAql8i1AKNmcYhpauXCr5KtMte9lx9nCWydekJSuWiAdCAgAAAABg/wilYHMxMTEKPxmuYo8Wy1E/r0e9FH4yXLGxsblUGQAAAAAAyC8IpWBziYmJSk1LlaOLY476Obo4KjUtVQkJCblUGQAAAAAAyC9Y6Bw25+LiIkcHR6UmpuaoX2piqhwdHOXq6ppLlQEAAAAA7J1hSDExUmKi5OLCslL5GaEUsnXhwgXNmDFD27Zt06VLl1S4cGF5e3urUqVKqlatmkaOHJmh/dmzZ/Xll19qy5YtunbumoxFhmJKxcgz0FPeTb3v+n7X/76u4IeD5eHhkVunBAAAAACwU3Fx0saN0tKlUni4lJoqOTpKAQFS165S8+a3HtBnr86cOaPmzZtr+fLlCgwMvN/lWITb95Cly5cvq3v37lq0aJHOnj2r5ORkxcbG6vTp09q0aZM+//zzDO3DwsLUsWNHLViwQGfPnr21WHmqdPPcTUWHRd/1/ZJik2REGurWqZtMRNgAAAAAgBwIC5N69JDGjpV275YcHG7NknJwuPV67Nhb+8PCbPu+P/30k2rXrq2UlBTztri4OFWtWlX9+/fP0Hbnzp0KCAjQ6dOn7/l9x44dq+HDh9/zce43ZkohS/PmzdPly5clSQ0bNlTfvn3l5uams2fP6uDBg9qwYYO5bUxMjF588UXFxsbK0dFRPXr0UHBwsD6a/pEupF2Qe7k7R9FGmqHIXyNVsXhFhYSE5Op5AQAAAADsS1iY9NJL0pUrkq+v5Pyvh8B7e0tJSVJEhDR6tDRtmhQUZJv3rl+/vuLj43Xo0CHVqlVLkrRnzx6VKFFCBw4c0M2bN1W4cGFJt0KpsmXLqnz58rZ58zySlJQk539fVBthphSydPjwYfPP48aNU8uWLdW4cWP17NlTEydO1KZNm8z7ly9fbg6wRo4cqQkTJqh9+/b6zwf/USmnUoq5GaOk2KQs3ycpNkknVpyQT7SPJr0zSe72PJcSAAAAAGBTcXHS+PG3Aik/v8yBVDpn51v7L1++1T4uzjbv7+fnJx8fH+3atcu8bdeuXWrevLkeeugh7d+/P8P2+vXrKy0tTV9++aVCQkJUo0YNdezYUevWrTO3S01N1RtvvKHmzZvrscceU5s2bTR79mzz/s8++0zLli3Txo0bFRAQoICAAO3cudO8PzIyUv3791fNmjXVsWNH7du3L0PNe/bsUZ8+fVSjRg01a9ZMEydOVHx8vHl/SEiIZs6cqddee01BQUF6++23bXOxskAohSzdHg5Nnz5de/bsUVLS/4Kl2xcj37Jli/nntLQ0dejQQTVq1NArr7yiejXqqXxieUXOidSJVSd09dhVRUdE6+qxqzqx6oQi50SqYlJFTZ8yXbVr186bkwMAAAAA2IWNG2/NgPL1vfti5ibTrXYREVJoqO1qqF+/foZQaOfOnapXr56Cg4PN2xMTE3XgwAHVr19fX375pZYvX64JEyZozZo1euaZZ/Tqq6+ag620tDSVLl1a06dP1/z58zVixAhNmzZNa9eulSQNGjRITz75pJo2baotW7Zoy5YtGb5PT5s2TYMHD9by5ctVsWJFvfLKK+bbC0+fPq2hQ4eqVatWWrlypaZNm6a9e/fq//7v/zKc03fffafKlStr+fLluXqbILfvIUuNGjUyJ7WhoaEKDQ2Vk5OTqlevrpYtW6p3795yc3OTJJ08edLc77PPPjP/fO7cOa1cuVINGjTQ8CHDtXTlUoVvCVdyWrIcHRwV/HCwur3WTSEhIcyQAgAAAADkiGHcWtRcyn6G1L85O98Kp5Yskdq3t81T+Ro0aKD3339fKSkpSkxM1NGjR1WvXj2lpKRo/vz5kqR9+/YpKSlJ9erVU7t27TRr1ixzkOTr66u9e/dqwYIFqlevnpycnPTCCy/IMAxFR0crMDBQ+/fv17p169S2bVu5u7vLxcVFSUlJ8vHxyVTPoEGD9Pjjj0uSXnjhBbVr106nTp1SpUqV9OWXX6pDhw565plnJEkVK1bUm2++qf79++vdd98132rYoEEDDRo06N4vzl0QSiFL3bt31+7du7Vq1SrztuTkZIWFhSksLEw//fSTFi9erCJFiig2NtbcpmjRonrjjTckSe+//76io6O1Y8cO9e/fX7O+nqXY2FglJCTI1dVVHh4eLGoOAAAAALBKTMytp+wVK5azfl5et/rFxkqenvdeR7169RQfH68///xTN27cUMWKFeXt7a3g4GCNGzdON2/e1K5du+Tr66v4+HglJCRkCnySk5MzPDHvhx9+0OLFi3Xu3DklJSUpOTlZlStXtqiegIAA88/podXVq1dVqVIlHTt2TOHh4Rm+6xuGobS0NJ05c0aVKlWSJFWrVs3q65EThFLIkqOjoz766CP1799f69at044dO3Ts2DGlpaVJujXl79tvv9Xo0aPl5OSk1NRUSdJTTz2lzp07S5JOnDihL7/8UpK0fft2tWjRQp6envK0xaceAAAAAPBAS0yUUlMlJ6ec9XN0lJKTpYQE24RSFSpUUOnSpbVz505FR0crODhYklSqVCmVKVNGYWFh2rlzpxo0aGBeu+nLL79UqVKlMhwnfTHxNWvWaOrUqXr99df1yCOPqFSpUvruu+904MABi+pxuu2CpE8ESf8uHx8fr969e2d6MqAklSlTxvzz7Uv25CZCKdxRzZo1VbNmTUnSlStXNGHCBK1fv17S/xZDL126tCIiIiRJZcuWNfe9/efbZ1MBAAAAAHCvXFxuBUz/f46ExVJTb/WzZe5Sv3597dq1S9HR0Ro8eLB5e926dfX777/r4MGDeuqpp1SpUiU5Ozvr3LlzqlevXpbHCgsLU+3atdWnTx9FR0eraNGiOn36dIY2Tk5O5qApJ6pUqaLjx4+rQoUKOe6bG1joHFnavXu34v71OIISJUqYZ0FJ/0taa9SoYd527tw588/nz583/1y6dOlcqhQAAAAA8CDy9JQCAqTr13PW7/r1W/08PGxXS/369bV3714dO3YsQ9hUr149LViwQMnJyapfv748PDw0aNAgTZ48WcuWLdPp06d1+PBhzZ07V8uWLZN0a+bVoUOH9Mcff+j06dP65JNP9Oeff2Z4v3Llyik8PFwnTpzQ1atXlZycbFGdQ4cO1b59+/Tee+/p6NGjioiI0IYNG/Tee+/Z7mLkADOlkKUFCxZo8+bNatOmjYKDg1WyZElFRUXpiy++MLepXr26JKljx45atWqVDMPQ/Pnz5efnJ0nmBd0kqXXr1nl7AgAAAAAAu2YySV27Srt2SUlJli12npR0a4H0bt1ss8h5uvr16ysxMVF+fn4qUaKEeXtwcLDi4uL08MMPq2TJkpKkl156Sd7e3vryyy915swZeXp6qkqVKho2bJgkqXfv3jp69KhefvllSVL79u3Vp08f/f777+bj9uzZU7t27VK3bt0UHx+vOXPmqFy5cnets3Llypo7d66mT5+uPn36SLq10Hrbtm1tdi1ywmQYhnFf3rkAiI+P19GjRxUYGGh+0tyDYsyYMRkWPvs3Hx8fLVu2TCVKlFB0dLS++uorffvtt1m2HTp0qMaMGZNbpQIZpD+homjRoiykj3yBMYn8hPGI/IYxifyE8VgwxcVJPXpIERGSn9+dgybDkE6ckCpWlBYtkvL7Q+AL8pi0NE9hptQDxjAMxcTEKDExUS4uLvL09MxycI8cOVKVK1fW9u3bdfr0aV25ckXJyckqU6aMGjdurOeff14+Pj5KzzRfffVV+fv7a968eTp+/Lgkyd/fX/369VPHjh3z9BwBAAAAAA8Gd3dp4kRp9OhbgZOvb9YzppKSpMhIycdHmjQp/wdSDwpmSt2BPc2UiouL08aNG7V05VKFnwxXalqqHB0cFfBwgLp27KrmzZvL3YpPZUFObmGfGJPIbxiTyE8Yj8hvGJPITxiPBVtYmDR+/K0ZUyaT5OX1v0XQr1+/NUuqYsVbgVTt2ve3VksV5DHJTCmYhYWFafx74xURFSGTr0leTbzk5OKk1MRU7f57t3Z9uEsVv6uoiW9PVFBQ0P0uFwAAAACAHAkKunVLXmiotGSJFB4uJSffCqaCg2+tIRUSwgyp/IZQys6FhYXppXEv6UrRK/Id4Ctnj4zzGL0reyspNkkRv0Zo9LjRmjZ5GsEUAAAAAKDAcXeXOnSQ2reXYmOlhATJ1fXWU/YK2ESjB4bD/S4AuScuLk7j3xuvK0WvyK+TX6ZAKp2zh7P8OvnpctHLGv/eeMXFxeVxpQAAAAAA2IbJJHl6SiVL3vpfAqn8i1DKjm3cuFERURHybekrk8OdP4UmB5N8W/oqIipCoaGheVQhAAAAAAB4UBFK2SnDMLR05VLJV9nOkPo3Zw9nmXxNWrJiiVj/HgAAAAAA5CZCKTsVExOj8JPhKvZosRz183rUS+EnwxUbG5tLlQEAAAAAABBK2a3ExESlpqXK0cUxR/0cXRyVmpaqhISEXKoMAAAAAIDcYxiGbty8oUtxl3Tj5g3uBMrHePqenXJxcZGjg6NSE1Nz1C81MVWODo5ydXXNpcoAAAAAALC9uKQ4bTy5UUuPLlX4lXClGqlyNDkqoESAugZ2VfOHm8vd2f1+l2mRsWPH6saNG3r//ffvdym5iplSdsrT01MBDwfo+t/Xc9Tv+t/XFfBwgDw8PHKnMAAAAAAAbCzsfJh6LOqhsRvGavfZ3XJwcJBLIRc5ODho99ndGrthrHos6qGw82E2f++xY8cqICBAAQEBqlatmlq2bKkZM2YoJSXlrn3PnDmjgIAAHT161OZ1FQSEUnbKZDKpa8euMiINJcUmWdQnKTZJRqShbp26ycQzMwEAAAAABUDY+TC9tO4lRVyPkG9RX/l5+8nb1VtFXYrK29Vbft5+8i3qq4jrERq9bnSuBFNNmzbVli1b9Msvv2jgwIGaMWOGvv32W5u/j70hlLJjzZs3V8XiFRX5a6SMtDvfQ2ukGYr8NVIVi1dUSEhIHlUIAAAAAID14pLiND50vK7EX5FfMT85O2b99HlnR2f5FfPT5fjLGh86XnFJcTatw9nZWT4+PipXrpz69OmjRo0a6eeff1ZQUJDWrVuXoe2GDRtUq1YtxcbGqnnz5pKkzp07KyAgQP3798/Qdt68eWratKnq16+vCRMmKDk52bwvOjpar732moKDg1WzZk0NGTJEERER5v1Lly5V3bp19ccff+jJJ59U7dq1NXjwYF26dMmm534vCKXsmLu7uya+PVE+0T46seJEtjOmkmKTdGLFCflE+2jSO5Pk7l4w7rEFAAAAADzYNp7caJ4hdbc7fkwmk3nGVOjJ0Fytq3DhwnJwcFC7du20dOnSDPuWLFmi1q1by8PDQ4sWLZIkff/999qyZYs+++wzc7udO3fq7Nmzmj17tqZMmaJly5Zp2bJl5v1jx47VoUOH9N///lcLFiyQYRh69tlnMwRXiYmJ+u677/TBBx9o3rx5On/+vKZOnZqr554ThFJ2LigoSNMmT1PFpIqKnBOpE6tO6Oqxq4qOiNbVY1d1YtUJRc6JVMWkipo+Zbpq1659v0sGAAAAAOCuDMPQ0qO3Ap/sZkj9m7Ojs0wyacnRJbnyVD7DMLRt2zZt2bJF9evXV48ePbRlyxbz7KSoqCj9/vvv6tatmyTJ29tbkuTl5SUfHx95eXmZj1WkSBGNGTNGfn5+euKJJ9SsWTNt375dkhQREaHQ0FBNnDhRdevWVeXKlfXRRx/p4sWL2rBhg/kYycnJmjBhgqpXr66qVauqb9++2rFjh83P21o8fe8BEBQUpEU/LFJoaKiWrFii8C3hSk5LlqODo4IfDla317opJCSEGVIAAAAAgAIjJilG4VfCVcy1WI76ebl6KfxKuGKTYuVZ2NMmtfz222+qXbu2kpOTZRiG2rdvr1GjRsnNzU2PPPKIli9frmeffVYrV65U2bJlFRwcfNdjPvroo3J0dDS/9vHx0V9//SVJ+ueff1SoUCHVrFnTvL9YsWJ6+OGH9c8//5i3ubq6qnz58ubXJUuWVFRUlC1O2SYIpR4Q7u7u6tChg9q3b6/Y2FglJCTI1dVVHh4eLGoOAAAAAChwElMSlWqkysnklKN+jiZHJRvJSkhJsFkoVb9+fb377rtycnJSyZIlVajQ/+KWHj166IcfftCzzz6rpUuXqmvXrhZ9D7/9GNKt2w9zOrvLFsfITdy+94AxmUzy9PRUyZIl5enpSSAFAAAAACiQXAq5yNHkqFQjNUf9Uo1UOZoc5VrI1Wa1uLq6qkKFCipbtmymIKhjx446d+6c5syZo+PHj6tLly7mfU5OtwK11NScnUOlSpWUkpKiAwcOmLddu3ZNJ0+e1COPPHIPZ5K3CKUAAAAAAECB4+nsqYASAbqecD1H/a4nXFdAiQB5OHvkTmH/UrRoUbVs2VIffPCBGjdurNKlS5v3FS9eXC4uLvrjjz905coVxcTEWHTMihUrqnnz5nrrrbe0Z88eHTt2TK+++qpKlSplfqJfQUAoBQAAAAAAChyTyaSugV1lyFBSatZPm/+3pNQkGTLULbBbnt451L17dyUnJ5sXOE9XqFAhjR8/XgsWLFDTpk01fPhwi485efJkVa1aVcOGDVOvXr1kGIa++uor8+yrgsBk5KebCfOZ+Ph4HT16VIGBgXJzc7vf5eRLhmEoOjpaRYsW5VZA5AuMSeQ3jEnkJ4xH5DeMSeQnjMeCKS4pTj0W9VDE9Qj5FfO74+/OMAyduHZCFb0qalGPRXJ3zruHfS1fvlyTJ0/WH3/8IWdny54UWJDHpKV5CjOlAAAAAABAgeTu7K6JIRPl4+ajE9dOZDtjKik1SSeunZCPm48mhUzKs0AqISFBp0+f1tdff63evXtbHEg9KAilAAAAAABAgRVUJkjT2kxTRa+KioyO1ImrJ3Q14aqiE6N1NeGqTlw9ocjoSFX0qqjpbaardpnaeVbbN998oyeffFIlSpTQs88+m2fvW1AUunsTAAAAAACA/CuoTJAW9Vik0JOhWnJ0icKvhCvZSJajyVHB5YLVLbCbQh4OydNb9iRp1KhRGjVqVJ6+Z0FCKAUAAAAAAAo8d2d3dQjooPb+7RWbFKuElAS5FnKVh7NHgVuT6UFBKAUAAAAAAOyGyWSSZ2FPeRb2vN+l4C5YUwoAAAAAAAB5jlAKAAAAAAAAeY5QCgAAAAAAAHmOUAoAAAAAAAB5jlAKAAAAAAAAea7Ah1I//PCDQkJCVL16dfXo0UMHDx60qN+aNWsUEBCg4cOH53KFAAAAAAAA+LcCHUqtXbtWkydP1ogRI7Rs2TJVrlxZgwcPVlRU1B37nTlzRlOnTlXdunXzqFIAAAAAAADcrkCHUrNmzVLPnj3VrVs3PfLII5owYYJcXFy0ZMmSbPukpqZqzJgxGjVqlHx9ffOwWgAAAAAAAKQrsKFUUlKSDh8+rEaNGpm3OTg4qFGjRtq3b1+2/WbOnKnixYurR48eeVEmAAAAAAAAslDofhdgrWvXrik1NVXFixfPsL148eI6ceJEln327NmjxYsXa/ny5Tl6L8MwZBiGtaXatfRrw/VBfsGYRH7DmER+wnhEfsOYRH7CeER+U5DHpKU1F9hQKqdiY2P12muv6f/+7//k7e2d477Jycm5VFnBZhiG4uPjJUkmk+k+VwMwJpH/MCaRnzAekd8wJpGfMB6R3xTkMXnz5k2L2hXYUKpYsWJydHTMtKh5VFSUSpQokal9ZGSkzp49q+eff968LS0tTZJUpUoVrVu3TuXLl8/yvTw8POTm5mbD6u1HevpZtGjRAvchgX1iTCK/YUwiP2E8Ir9hTCI/YTwivynIYzI9TLubAhtKOTs7q2rVqtq+fbtatGgh6VbItH37dvXr1y9Tez8/P61atSrDtunTpysuLk5vvvmmSpcune17mUymAjcA8lL69eEaIb9gTCK/YUwiP2E8Ir9hTCI/YTwivymoY9LSegtsKCVJAwcO1Ouvv65q1aqpRo0amj17thISEtS1a1dJ0muvvaZSpUrplVdeUeHCheXv75+hf5EiRSQp03YAAAAAAADkrgIdSrVt21ZXr17Vp59+qsuXLyswMFDffPON+fa98+fPy8GhwD5gEAAAAAAAwG4V6FBKkvr165fl7XqSNHfu3Dv2nTJlSm6UBAAAAAAAgLtgGhEAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyHKEUAAAAAAAA8hyhFAAAAAAAAPIcoRQAAAAAAADyXCFrO8bFxenEiRO6du2aTCaTihUrpooVK8rDw8OW9QEAAAAAAMAO5SiUioyM1PLly7Vx40b9/fffSktLy7DfwcFBjzzyiFq0aKHOnTvL19fXpsUCAAAAAADAPlgUSh0/flyffvqpfv31VxUpUkT16tVTmzZt5OvrqyJFisgwDN24cUNnzpzR4cOHNW/ePH3++edq2bKlXnzxRVWqVCm3zwMAAAAAAAAFiEWhVKdOndSsWTN9+eWXatSokQoVunO3lJQUbdu2TfPnz1enTp106NAhmxQLAAAAAAAA+2BRKLVy5coczXYqVKiQHnvsMT322GP6559/rC4OAAAAAAAA9smip+/dy+133LoHAAAAAACAf7P66Xu3S0tL0/79+3Xx4kX5+PioVq1ad73FDwAAAAAAAA+ue06O/vnnHz3//PO6cOGCihQpomvXrqlUqVKaOXOmAgMDbVEjAAAAAAAA7IxFt+/dyYQJE/TYY49p9+7d2rJli/744w/5+vrqrbfeskV9AAAAAAAAsEMWh1Jvv/22rl+/nml7RESEunTposKFC0uSvL291apVK0VERNiqRgAAAAAAANgZi0OpS5cuqVWrVpo9e7ZSU1PN2+vVq6cpU6Zoz549OnXqlDZt2qRZs2apXr16uVIwAAAAAAAACj6LQ6kvvvhCH3/8sRYuXKj27dvr999/lyS9++67KlWqlJ555hm1bt1ao0aNUpUqVTRx4sRcKxoAAAAAAAAFW44WOm/atKkaNWqkuXPnasyYMapVq5beeOMNffTRR/rggw907do1eXl5ydHRMbfqBQAAAAAAgB3I8ULnjo6OeuaZZ7Ru3TqVKlVKnTp10pQpUxQfH6/ixYsTSAEAAAAAAOCuchxKJSUlKSYmRt7e3vq///s/zZ8/X4cOHVKrVq20cOFCGYaRG3UCAAAAAADAjuRoofMhQ4aoVq1aqlevnlq3bq3du3crMDBQ8+bN0/jx4/Xf//5XXbp00e7du3OzZgAAAAAAABRwFodSb7/9ts6ePavvv/9ey5YtU2BgoEaNGqWEhARJUtu2bfXzzz8rJCREQ4cO1YsvvphrRQMAAAAAAKBgsziU2rNnj55++mnVq1dPlStX1pgxY3T9+nUdP37c3MbFxUUvvPCC1q5dK5PJlCsFAwAAAAAAoOCzOJTy8fHRgQMHzK8PHDggk8kkHx+fTG3Lli2r6dOn26RAAAAAAAAA2J9CljZ85ZVXNHr0aO3du1eenp46cuSI+vfvr9KlS+dmfQAAAAAAALBDFodSLVq00Nq1a7V161YlJibqjTfeUJ06dXKzNgAAAAAAANgpi0MpSfL19VXv3r1zqxYAAAAAAAA8IHIUSqU7ffq0Dh06pEuXLikxMVFubm6qWLGigoKC5OHhYesaAQAAAAAAYGdyFErt2bNHkydP1pEjRzJsNwxDJpNJLi4u6t69u0aPHi03NzebFgoAAAAAAAD7YXEotXPnTg0ePFg+Pj7q06ePXFxctHfvXh06dEgvv/yyPDw8tHXrVv34448KCwvT3LlzCaYAAAAAAACQJYtDqU8++USPPvqofvrpJ7m4uJi3T5o0SQsWLNC6devUs2dPhYWFadCgQfryyy81evToXCkaAAAAAAAABZuDpQ2PHDmirl27ZgikJKlv3746deqUjh49KkkKCgpSr169tHbtWttWCgAAAAAAALthcSjl7OyshISETNsTEhJkMpmUnJxs3vboo4/q4sWLtqkQAAAAAAAAdsfiUKpBgwaaM2eOTp06Zd4WHx+vadOmydXVVQEBAebtV65cUdGiRW1bKQAAAAAAAOyGxWtKjRkzRj179lS7du1UpUoVFS5cWH/99Zeio6P11ltvZbitLzQ0VDVr1syVggEAAAAAAFDwWRxKlS9fXsuXL9eXX36pvXv36saNGwoKCtJTTz2lxx57LEPb6dOny8PDw+bFAgAAAAAAwD5YHEpJUunSpfXOO+/ctV3ZsmWtLggAAAAAAAD2z+I1pbKSnJys2NjYDIucAwAAAAAAAHeTo5lSKSkpWrZsmX7++WcdOXJE0dHR5n1FixZVYGCgnnzySXXp0kVOTk42LxYAAAAAAAD2weJQ6urVqxo8eLCOHj2qihUr6rHHHpOPj48KFy6smzdv6vLlyzp48KDefvtt/fjjj/ruu+/k7e2dm7UDAAAAAACggLI4lJoyZYrOnTunWbNmqWHDhtm22759u1566SVNnTpVU6dOtUmRAAAAAAAAsC8Wrym1efNmDR48+I6BlCQ1bNhQgwYN0m+//XavtQEAAAAAAMBOWRxKJSUlyd3d3aK27u7uSkpKsrooAAAAAAAA2DeLQ6mgoCDNmTNHFy9evGO7ixcvas6cOapTp849FwcAAAAAAAD7ZPGaUm+88Yb69u2rNm3a6PHHH1e1atXk4+MjZ2dnJSUl6fLlyzp06JB+++03ubi4aNy4cblZNwAAAAAAAAowi0OpSpUqacWKFfrvf/+rX3/9VT///HOmNt7e3urUqZOGDRum0qVL27RQAAAAAAAA2A+LQylJKlWqlN599129++67unjxoi5fvqzExES5uLjIx8dHpUqVyq06AQAAAAAAYEdyFErdrlSpUoRQAAAAAAAAsIrFC50DAAAAAAAAtmLzUCoyMlLNmzdXixYtbH1oAAAAAAAA2Amrb9+7E8MwZBhGbhwaAAAAAAAAdsDmoZSvr69CQ0NtfVgAAAAAAADYEdaUAgAAAAAAQJ4jlAIAAAAAAECes/j2vaioKHl6esrZ2dm87eLFi/rvf/+r7du36/r16/L29tZjjz2m5557Tt7e3rlSMAAAAAAAAAo+i2dKNWnSRL/88ov59alTp9SlSxctWLBAxYoVU8OGDeXm5qbZs2ere/fuunr1aq4UDAAAAAAAgILP4plS/36a3pQpU5SQkKB58+apTp065u2bN2/WiBEjNHPmTL311lu2qxQAAAAAAAB2w6o1pdLS0rRlyxYNHDgwQyAlSc2aNVPXrl21adMmmxQIAAAAAAAA+2NVKJWQkKDk5GRVrVo1y/1Vq1bV5cuX76kwAAAAAAAA2C+Lb9+TpLNnz+rw4cOSJDc3N8XGxmbZLiYmRq6urvdeHQAAAAAAAOxSjmZKffLJJ+revbu6d++u+Ph47dy5M8t2hw4d0kMPPWSTAgEAAAAAAGB/LJ4pNXny5EzbXFxcMm27evWqjh49qrZt295bZQAAAAAAALBbFodSXbp0saidt7e3fvnlF6sLAgAAAAAAgP2zaqFzAAAAAAAA4F5YFUrFx8fLMIxs90dFRWn37t1WFwUAAAAAAAD7lqNQatmyZXriiSdUp04dBQUFady4cbp8+XKmdlu3btWAAQNsViQAAAAAAADsi8Wh1Pbt2zVu3Di5urrqmWeeUUhIiNauXav27dtr165duVnjHf3www8KCQlR9erV1aNHDx08eDDbtgsXLlSfPn0UHBys4OBgPfPMM3dsDwAAAAAAgNxhcSj1+eefq2rVqlq+fLlef/11ffzxx1q5cqXKlCmjIUOGaO3atblZZ5bWrl2ryZMna8SIEVq2bJkqV66swYMHKyoqKsv2O3fuVLt27TRnzhzNnz9fZcqU0aBBg3Tx4sU8rhwAAAAAAODBZnEo9ffff6tz585ydnY2b6tQoYLmz5+vRo0aacyYMZo3b16uFJmdWbNmqWfPnurWrZseeeQRTZgwQS4uLlqyZEmW7T/++GP17dtXgYGBqlSpkiZOnKi0tDRt3749T+sGAAAAAAB40BWytGFaWpocHR0zbXdxcdHnn3+uN998U5MmTVJUVJQefvhhmxaZlaSkJB0+fFjPPfeceZuDg4MaNWqkffv2WXSMhIQEpaSkqGjRondsZxjGHRd2f5ClXxuuD/ILxiTyG8Yk8hPGI/IbxiTyE8Yj8puCPCYtrdniUKpChQoKCwtTnz59Mu1zcHDQ5MmT5eXlpf/+97+qWLGixYVa69q1a0pNTVXx4sUzbC9evLhOnDhh0TE++ugjlSxZUo0aNbpju9jYWCUnJ1tdqz0zDEPx8fGSJJPJdJ+rARiTyH8Yk8hPGI/IbxiTyE8Yj8hvCvKYvHnzpkXtLA6lmjVrpm+//VbXr1+Xl5dXlm1ef/11FS9eXB999FG+v2BfffWV1q5dqzlz5qhw4cJ3bOvh4SE3N7c8qqxgSU8/ixYtmu9/53gwMCaR3zAmkZ8wHpHfMCaRnzAekd8U5DGZHqbdjcWhVPfu3VWsWDFFRUVlG0pJ0pAhQ1ShQgWFh4dbemirFCtWTI6OjpkWNY+KilKJEiXu2Pfbb7/VV199pVmzZqly5cp3fS+TyVTgBkBeSr8+XCPkF4xJ5DeMSeQnjEfkN4xJ5CeMR+Q3BXVMWlqvxQudly5dWn379lWlSpXu2rZly5YaOXKkpYe2irOzs6pWrZphkfL0Rctr166dbb+vv/5an3/+ub755htVr149V2sEAAAAAABA1iyeKZUfDRw4UK+//rqqVaumGjVqaPbs2UpISFDXrl0lSa+99ppKlSqlV155RdKtW/Y+/fRTffzxxypXrpwuX74sSXJzc5O7u/t9Ow8AAAAAAIAHTYEOpdq2baurV6/q008/1eXLlxUYGKhvvvnGfPve+fPn5eDwv8lg8+fPV3Jysl544YUMxxk5cqRGjRqVp7UDAAAAAAA8yAp0KCVJ/fr1U79+/bLcN3fu3AyvQ0ND86IkAAAAAAAA3IXFa0oBAAAAAAAAtkIoBQAAAAAAgDxHKAUAAAAAAIA8RygFAAAAAACAPGdVKHXixAlVrlxZVapUydE+AAAAAAAAQLLy6Xuurq4KDg7O8T4AAAAAAABAsjKUKlOmjObOnZvjfQAAAAAAAIDEmlIAAAAAAAC4D6yaKZUuKSlJhw8fVlRUlIKCguTt7W2rugAAAAAAAGDHrJ4pNWfOHDVp0kR9+vTRqFGjFB4eLkm6evWq6tevr8WLF9usSAAAAAAAANgXq0KpJUuW6P3331fTpk01adIkGYZh3uft7a0GDRpo7dq1NisSAAAAAAAA9sWqUGrWrFlq3ry5Pv74Yz3xxBOZ9letWlV///33PRcHAAAAAAAA+2RVKHXq1Ck99thj2e738vLS9evXra0JAAAAAAAAds6qUKpIkSK6du1atvuPHz8uHx8fq4sCAAAAAACAfbMqlHrssce0cOFC3bhxI9O+v//+W4sWLVJISMg9FwcAAAAAAAD7VMiaTi+99JJ69uyp9u3b64knnpDJZNLy5cu1ZMkSrV+/Xj4+Pho+fLitawUAAAAAAICdsGqmVKlSpbR06VI1bdpUP//8swzD0IoVK7Rp0ya1a9dOCxculLe3t61rBQAAAAAAgJ2waqaUJBUvXlyTJk3SpEmTdPXqVaWlpcnb21sODlblXAAAAAAAAHiAWB1K3Y5ZUQAAAAAAAMgJq6Y1TZs2TZ06dcp2f+fOnTVjxgyriwIAAAAAAIB9syqU+uWXX/TYY49lu79Zs2Zau3at1UUBAAAAAADAvlkVSp0/f17ly5fPdv9DDz2kc+fOWV0UAAAAAAAA7JtVoZSbm5vOnj2b7f4zZ86ocOHCVhcFAAAAAAAA+2ZVKFWvXj0tWLBAFy9ezLTv/PnzWrBggerXr3/PxQEAAAAAAMA+WfX0vRdffFE9evRQu3bt1L17dz3yyCOSpL///ltLliyRYRh68cUXbVooAAAAAAAA7IdVoZSfn59++OEHTZw4Ud9//32GfcHBwXrzzTdVqVIlW9QHAAAAAAAAO2RVKCVJlStX1rx583T16lWdOXNG0q0Fzr29vW1WHAAAAAAAAOyT1aFUOm9vb4IoAAAAAAAA5IjVoVRqaqq2bNmiyMhIRUdHyzCMDPtNJpNGjBhxzwUCAAAAAADA/lgVSv3555964YUXdOHChUxhVDpCKQAAAAAAAGTHqlBqwoQJSkxM1MyZM1W3bl0VKVLE1nUBAAAAAADAjlkVSoWHh2v06NEKCQmxdT0AAAAAAAB4ADhY06l06dLZ3rYHAAAAAAAA3I1VodTQoUO1cOFCxcbG2roeAAAAAAAAPACsun0vLi5O7u7uatmypdq1a6fSpUvL0dExQxuTyaRnnnnGFjUCAAAAAADAzlgVSk2dOtX887x587JsQygFAAAAAACA7FgVSm3cuNHWdQAAAAAAAOABYlUoVa5cOVvXAQAAAAAAgAeIVQudAwAAAAAAAPfCqplSknTs2DHNmzdPR44cUUxMjNLS0jLsN5lM2rBhwz0XCAAAAAAAAPtj1UypnTt3qkePHvrtt99UsmRJRUZGytfXVyVLltS5c+fk5uam4OBgW9cKAAAAAAAAO2FVKPXpp5/K19dX69at0/vvvy9Jeu655/TTTz9p/vz5unjxotq0aWPTQgEAAAAAAGA/rAqljhw5ou7du8vDw0OOjo6SZL59r2bNmurVq5c++eQT21UJAAAAAAAAu2JVKOXo6Ch3d3dJUpEiRVSoUCFFRUWZ9/v6+uqff/6xTYUAAAAAAACwO1aFUuXLl1dERISkWwua+/n5ZVjU/LffflOJEiVsUiAAAAAAAADsj1WhVLNmzbRmzRqlpKRIkgYOHKj169erVatWatWqlUJDQ9WrVy+bFgoAAAAAAAD7UciaTsOHD9eAAQPM60l16dJFDg4OWr9+vRwdHTVs2DB17drVpoUCAAAAAADAflgVSjk5OalYsWIZtnXq1EmdOnWySVEAAAAAAACwb1bdvgcAAAAAAADcC4tmSvXv318ODg769ttvVahQIQ0YMOCufUwmk2bPnn3PBQIAAAAAAMD+WHz7XlpamvlnwzDu2t6SNgAAAAAAAHgwWRRKzZ07946vAQAAAAAAgJzI8ZpSiYmJmjx5skJDQ3OjHgAAAAAAADwAchxKubi4aMGCBYqKisqNegAAAAAAAPAAsOrpe1WrVtVff/1l61oAAAAAAADwgLAqlHrjjTe0du1aLVq0SCkpKbauCQAAAAAAAHbO4qfv3W7s2LEymUx6++23NXHiRJUqVUqFCxfO0MZkMmnlypU2KRIAAAAAAAD2xapQysvLS15eXnr44YdtXQ8AAAAAAAAeAFaFUnPnzrV1HQAAAAAAAHiAWLWmFAAAAAAAAHAvrJoplS45OVknTpxQTEyMDMPItD84OPheDg8AAAAAAAA7ZVUolZaWpo8//lg//vijEhMTs2139OhRqwsDAAAAAACA/bIqlPriiy/07bffqlevXqpTp45ee+01jRkzRkWKFNGPP/4ok8mkV1991da1AgAAAAAAwE5YtabUsmXL9OSTT2rChAlq2rSpJKlq1arq2bOnFi5cKJPJpB07dti0UAAAAAAAANgPq0KpCxcuqEGDBpIkZ2dnSVJSUpL5dceOHbVixQoblQgAAAAAAAB7Y1Uo5eXlpfj4eEmSu7u7PDw8FBkZmaHNjRs37r06AAAAAAAA2CWr1pSqUqWK/vzzT/Pr+vXra/bs2QoMDJRhGJozZ44CAgJsViQAAAAAAADsi1UzpXr06KGkpCTzLXujR4/WjRs31K9fP/Xr109xcXEaO3asTQsFAAAAAACA/bB4ptTUqVPVqVMnVa5cWS1atFCLFi3M+x555BFt2LBBO3fulKOjo2rXri0vL6/cqBcAAAAAAAB2wOJQatasWfr+++/l5+enjh07qn379ipXrpx5v6enZ4agCgAAAAAAAMiOxbfv/fLLLxoxYoTS0tI0bdo0tWjRQk899ZR++uknXbt2LTdrBAAAAAAAgJ2xOJSqUKGCRo4cqZ9//llLlizR008/rXPnzmnChAlq2rSphg0bprVr1yoxMTE36wUAAAAAAIAdsOrpe1WrVlXVqlX1+uuva+fOnVq9erV+/fVX/fbbb3Jzc1OLFi3UoUMHNW3a1Nb1AgAAAAAAwA5Y9fS9dCaTSQ0aNNDEiRO1ZcsWzZw5U0FBQVq5cqWee+45W9UIAAAAAAAAO2PVTKl/S0pK0m+//abVq1dr165dkqTixYvb4tAAAAAAAAC4zYULFzRjxgxt27ZNly5dUuHCheXt7a1KlSqpWrVqGjlypCQpPDxcX3/9tQ4fPqzLly8rISFBHh4eqly5srp3764OHTrc1/OwOpQyDEPbt2/XqlWrtGHDBsXExMjd3V1t27ZVx44d1aBBA1vWCQAAAAAA8MC7fPmyunfvrsuXL5u3JScnKzY2VqdPn9bvv/9uDqWOHTumVatWZeh//fp17dixQzt27NC5c+fu651uOQ6lDh48qFWrVunnn39WVFSUHB0d1aRJE3Xs2FHNmzdX4cKFc6NOAAAAAACAB968efPMgVTDhg3Vt29fubm56ezZszp48KA2bNhgbuvl5aWePXuqbt26KlmypK5fv67Zs2dr3759kqS5c+cWjFDqk08+0Zo1axQZGSnDMFS7dm0NHz5cbdu2lZeXVy6WCAAAAAAAAEk6fPiw+edx48YpICDA/Lpnz5568803za+bNWumZs2aZehfsWJFde7cWZIUFxeXu8XehcWh1H//+1/5+fnphRdeUIcOHfTQQw/lZl0AAAAAAAD4F3d3d/PP06dP1+DBg1WjRg05OztLklxdXbPsl5aWpsuXL2v+/PnmbfXr18/dYu/C4lBq6dKlqlKlSm7WAgAAAAAAgDto1KiR1q1bJ0kKDQ1VaGionJycVL16dbVs2VK9e/eWm5tbhj49e/bUgQMHzK9NJpMef/xxTZo0KU9r/zcHSxsSSAEAAAAAANxfWT01Lzk5WWFhYZo6dao6deqk6OjoOx7DwcFBjo6OSktLy81S78riUAoAAAAAAAD3l6Ojoz766CMtXLhQgwYNUpUqVeTg8L945/Tp0/r2228z9Hnvvfc0d+5cffDBB6pdu7ZSU1O1YcMGDRs2LK/LzyDHT98DAAAAAADA/VWzZk3VrFlTknTlyhVNmDBB69evl5RxMXRJqly5svnnVq1aqX79+rp586YOHTqkkydP6uGHH867wm/DTCkAAAAAAIACYvfu3ZmemleiRAnzE/UkmW/LS0xMzPIYJpPJ/HNMTIzti7QQM6UAAAAAAAAKiAULFmjz5s1q06aNgoODVbJkSUVFRemLL74wt6levbokqVu3bqpZs6bq1KmjsmXLKioqSj/++KM5rHJxcZGfn999OQ+JUAoAAAAAAOC+MwxDMTExSkxMlIuLizw8PLJte+PGDS1cuFALFy7MtM/Hx0f9+/eXJCUkJGjJkiVasmRJlsd57bXX7vg+uc2iUGrAgAE5PrDJZNLs2bNz3A8AAAAAAOBBERcXp40bN2rp0rUKDz+r1FTJ0VEKCCirVq2aqWPHjhmCo5EjR6py5cravn27Tp8+rStXrig5OVllypRR48aN9fzzz8vHx0eSNGjQIG3atEnHjx/X1atXZRiGSpYsqdq1a+upp55S3bp179dpS7IwlDIMI9O2CxcuKDIyUp6envL19ZUknTlzRjdu3FD58uVVunRp21YKAAAAAABgR8LCwjR+/FRFRNyUydRIXl795OTkodTUWO3evVXbt/+kefNWaOLE1xUUFCRJqlixooYMGaIhQ4bc9fj9+vVTv379cvs0rGZRKDV37twMr/fs2aPhw4fr//7v/9SlSxcVKnTrMCkpKVq6dKk++ugjTZ482fbVAgAAAAAA2IGwsDC99NJ7unIlSL6+o+TsXCzDfm/vxoqPH6iIiP9q9Oj3NG3a2+Zgyl5Y9fS9Dz74QF27dlWPHj3MgZQkFSpUSD179lTXrl01ZcoUmxUJAAAAAABgL+Li4jR+/FRduRIkP783MwVS6ZycisnP701dvhyk8eOnZnrqXkFnVSgVHh5uvmUvKw899JD++usvq4sCAAAAAACwVxs3blRExE35+o6SyeR4x7Ymk6N8fUcqIuKmQkND86jCvGFVKFWyZEmtXbtWKSkpmfalpKRo7dq1Klmy5D0XBwAAAAAAYE8Mw9DSpWslNcp2htS/OTt7y2RqqCVL1mS57ndBZVUoNWTIEO3du1c9e/bUokWLtHPnTu3cuVMLFy5Uz549tW/fPg0ePNjWtQIAAAAAABRoMTExCg8/q2LFGuWon5dXY4WHn1VsbGwuVZb3LFro/N969eolBwcHTZ8+XW+99ZZMJpOkW2mft7e3JkyYoJ49e9q0UAAAAAAAgIIuMTFRqamSk5NHjvo5OnooOVlKSEiQp6dnLlWXt6wKpSSpR48e6tKli/7880+dP39eklS2bFlVq1Ytw+LnAAAAAAAAuMXFxUWOjlJqas5mPKWmxsrRUXJ1dc2lyvLePaVHhQoVUu3atVW7dm1b1QMAAAAAAGC3PD09FRBQTrt3b5O3dxOL+12/vlXBweXk4ZGzGVb5mVVrSklSbGysvvrqKw0ePFidO3fWwYMHJUnXr1/XrFmzdOrUKZsVCQAAAAAAYA9MJpO6dm0rw9impKRrFvVJSroqw9iubt3amZdQsgdWhVIXLlxQ586d9emnn+rChQsKDw9XXFycJMnLy0vz58/X3LlzbVooAAAAAACAPWjevLkqViysyMjPZBipd2xrGKmKjJyhhx4yaevWrQoJCVG1atVUp04dtWzZUsOGDdOMGTOy7f/ss88qICDA/O+ff/6x9elYzapQ6oMPPlBcXJyWL1+uuXPnZnocYYsWLbR9+3abFAgAAAAAAGBP3N3dNXHi6/LxCdOJE5OUlHQ1y3ZJSVd14sQkeXvv0o0b57Vs2TKdPXtWycnJio2N1enTp7Vp0yZ9/vnnWfZfuXKlNm/enJunck+sWlNq69atevrpp/XII4/o2rXMU818fX3Ni58DAAAAAAAgo6CgIE2b9rbGj5+qiIhBMpkaysursRwdPZSaGqvr17coJWWrKlVyU/XqVbV8+XJJUsOGDdW3b1+5ubnp7NmzOnjwoDZs2JDp+FevXtX7778vk8mkQoUKKTk5OY/P8O6sCqUSExPl7e2d7f70W/kAAAAAAACQtaCgIC1a9J1CQ0O1ZMkahYdvUXKy5OgoBQeXVevWfdShQwe99NJL5j7jxo1TQECA+XXPnj315ptvZjr2+++/r2vXrqlXr17asmWLzp49mxenlCNWhVKVKlXS7t271bt37yz3b9iwQVWqVLmnwgAAAAAAAOydu7u7OnTooPbt2ys2NlYJCQlydXWVu7u7bty4IXd3d7m7u5vbT58+XYMHD1aNGjXk7OwsSXJ1dc1wzN9//12rVq1SyZIl9eqrr2rLli15ek6WsmpNqaefflpr167VV199pdjYWEmSYRg6deqUXn31Ve3fv1/PPPOMLesEAAAAAACwWyaTSZ6enipZsqQ8PT0zPGWvUaNG5p9DQ0PVt29fBQUF6amnntJ3332n+Ph48/64uDi9++67kqR33nlHnp6eeXYOOWXVTKlOnTrp3Llz+uSTTzR9+nRJ0pAhQ2QYhhwcHDR69Gi1aNHClnUCAAAAAAA8kLp3767du3dr1apV5m3JyckKCwtTWFiYfvrpJy1evFhFixbVtGnTdPbsWbVp0ybfZzNWhVKS9Pzzz6tTp05av369Tp06pbS0NJUvX16tWrWSr6+vLWsEAAAAAAB4YDk6Ouqjjz5S//79tW7dOu3YsUPHjh1TWlqaJOn06dP69ttv1alTJ/3www8qWrSo3nrrrftc9d1ZHUpJUtmyZblNDwAAAAAAIA/UrFlTNWvWlCRduXJFEyZM0Pr16yVJhw8fVuPGjZWWlqbo6Gg1btw4y2O0bdtWlStX1ooVK/Ks7uxYtabU7eLi4nT+/HmdO3cu07+88MMPPygkJETVq1dXjx49dPDgwTu2//nnn9WmTRtVr15dHTp00ObNm/OkTgAAAAAAAGvs3r1bcXFxGbaVKFFCnTt3Nr9OnzVVkFg1U+rmzZuaMWOGFi9erOvXr2fb7ujRo9bWZZG1a9dq8uTJmjBhgmrWrKnZs2dr8ODBWrdunYoXL56pfVhYmF555RW9/PLLeuKJJ7Rq1SqNGDFCS5culb+/f67WCgAAAAAAYI0FCxZo8+bNatOmjYKDg1WyZElFRUXpiy++MLepXr26KlSooHHjxmXq//nnnys6OlqS9Nxzz+mRRx7Js9rvxKpQ6t1339Xy5cvVokUL1alTR0WLFrV1XRaZNWuWevbsqW7dukmSJkyYoN9++01LlizRs88+m6n9nDlz1LRpUw0ZMkSS9NJLL2nbtm2aN2+e3nvvvTytHQAAAAAAPNgMw1BMTIwSExPl4uKS6al7t7tx44YWLlyohQsXZtrn4+Oj/v37y8fHJ8tllubMmWMOpTp16qRKlSrZ9DysZVUo9euvv6pHjx73NchJSkrS4cOH9dxzz5m3OTg4qFGjRtq3b1+Wffbv35/pl9OkSRNt2LAhN0sFAAAAAAAwi4uL08aNG7V06VqFh59Vaqrk6CgFBJRT165t1bx5c7m5uZnbjxw5UpUrV9b27dt1+vRpXblyRcnJySpTpowaN26s559/Xj4+PvfxjKxjVShlMplUpUoVW9eSI9euXVNqamqm2/SKFy+uEydOZNnnypUrKlGiRKb2V65cueN7GYYhwzDurWA7lX5tuD7ILxiTyG8Yk8hPGI/IbxiTyE8Yj8grYWFheuutDxQRcVMmU0N5efWVk5OHUlNjtXv3Nu3a9Z0qVvxB7733qipVqiTDMFShQgUNHjxYgwcPzva4dxq7GzdutLitLVh6fKtCqebNm2vbtm3q3bu3Nd0LnNjYWCUnJ9/vMvIlwzAUHx8vSdlOMQTyEmMS+Q1jEvkJ4xH5DWMS+QnjEXnhwIEDGjfuQ0VF1dFDD42Qk1OxDPs9PRsoOfma/vlnpl544V29/fZI1a9fv8CNyZs3b1rUzqpQavjw4XrppZf01ltvqVevXipbtqwcHDI/yM/Ly8uaw1ukWLFicnR0VFRUVIbtUVFRmWZDpStRokSmWVF3ap/Ow8Mjw7Q5/E96+lm0aNEC9yGBfWJMIr9hTCI/YTwiv2FMIj9hPCK3xcXFaerUz3X9erAeffRNmUyOWbZzcvLRo4++pX/+maSPP/5KS5c2loeHRx5Xe2/SA967sSqUatWqlSTpyJEjWrx4cbbtcvPpe87Ozqpataq2b9+uFi1aSLr1+MPt27erX79+WfapVauWduzYkWFdqW3btqlWrVp3fC+TycQfpTtIvz5cI+QXjEnkN4xJ5CeMR+Q3jEnkJ4xH5KbQ0FBFRNyUr+8LMpnuHMeYTIVUvvxIRUQ8o02bNqljx455VKVtWPoZsiqUGjFiRL74kA4cOFCvv/66qlWrpho1amj27NlKSEhQ165dJUmvvfaaSpUqpVdeeUWSNGDAAPXv31/fffedmjVrprVr1+rQoUM8eQ8AAAAAAOQawzC0dOlaSY3k7Fzsru0lydnZWyZTQy1dukYdOnTIFzmMrVkVSo0aNcrWdVilbdu2unr1qj799FNdvnxZgYGB+uabb8y3450/fz7DbYVBQUH66KOPNH36dP3nP/9RxYoVNXPmTPn7+9+vUwAAAAAAAHYuJiZG4eFnVaxY1nd2Zado0UYKD9+u2NhYeXp65lJ1949VoVR+0q9fv2xv15s7d26mbU8++aSefPLJ3C4LAAAAAABAkpSYmKjUVMnJKWdrQzk6uislRUpISHhwQ6kZM2bIZDLp+eefl4ODg2bMmHHXPiaTSSNGjLjnAgEAAAAAAAoyFxcXOTpKqamxOeqXmhonR0fJ1dU1lyq7v3IUSg0dOlTOzs6EUgAAAAAAABby9PRUQEA57d69Td7eTSzuFx29TQ0alC1wT9+zlEWh1LFjx+74GgAAAAAAAFkzmUzq2rWtdu36TklJ1yxa7Dwp6aoMY7u6dh1ql4ucS5LD3ZsAAAAAAADgXjRv3lwVKxZWZORnMozUO7Y1jFRFRs5QhQpOCgkJyaMK8x6hFAAAAAAAQC5zd3fXxImvy8cnTCdOTFJS0tUs2yUlXdWJE5NUosQ+jR//ktzd3fO40rxj9dP3jh07pnnz5unIkSOKiYlRWlpahv0mk0kbNmy45wIBAAAAAADsQVBQkKZNe1vjx09VRMQgmUwN5eXVWI6OHkpNjdX161tlGNtVsWJhTZz4tvz8/O53ybnKqlBq586dGjJkiIoWLapq1arpyJEjatCggW7evKn9+/frkUceUbVq1WxdKwAAAAAAQIEWFBSkRYu+U2hoqJYsWaPw8C1KTpYcHaXg4HLq1m2wQkJC5Obmpujo6Ptdbq6yKpT69NNP5evrq4ULFyopKUmNGjXSc889p4YNG+rAgQMaOnSoxowZY+taAQAAAAAACjx3d3d16NBB7du3V2xsrBISEuTq6ioPDw/zouaGYdznKnOfVWtKHTlyRN27d5eHh4ccHR0lyXz7Xs2aNdWrVy998skntqsSAAAAAADAzphMJnl6eqpkyZLy9PS026fsZceqUMrR0dG80FaRIkVUqFAhRUVFmff7+vrqn3/+sU2FAAAAAAAAsDtWhVLly5dXRESEpFupnp+fX4ZFzX/77TeVKFHCJgUCAAAAAADA/lgVSjVr1kxr1qxRSkqKJGngwIFav369WrVqpVatWik0NFS9evWyaaEAAAAAAACwH1YtdD58+HANGDDAvJ5Uly5d5ODgoPXr18vR0VHDhg1T165dbVooAAAAAAAA7IdVoZSTk5OKFSuWYVunTp3UqVMnmxQFAAAAAAAA+2bV7XsAAAAAAADAvbBoptSAAQNyfGCTyaTZs2fnuB8AAAAAAADsn0WhlGEYOT6wNX0AAAAAAADwYLAolJo7d25u1wEAAAAAAIAHCGtKAQAAAAAAIM9Z9fS9dJs2bdLmzZt19uxZSVK5cuXUrFkzPfHEEzYpDgAAAAAAAPbJqlDqxo0bGjFihPbs2SNHR0f5+PhIkrZv364FCxaobt26mjlzpooUKWLTYgEAAAAAAGAfrLp9b9KkSdq7d6/GjBmjXbt2adOmTdq0aZN27dqlV155RXv37tWkSZNsXSsAAAAAAADshFUzpTZs2KA+ffpo8ODBGba7ublpyJAhOn/+vJYvX26L+gAAAAAAAGCHrJopVahQIT388MPZ7vfz81OhQve0XBUAAAAAAADsmFWhVOvWrbVu3TqlpqZm2peSkqKff/5Zbdq0uefiAAAAAAAAYJ+sms7UsWNHvffee+rdu7d69uypChUqSJJOnTqlBQsWKDk5WR06dNDhw4cz9Ktateq9VwwAAAAAAIACz6pQql+/fuaf//zzT5lMJkmSYRjm7f379zf/bBiGTCaTjh49am2dAAAAAAAAsCNWhVLvv/++OYgCAAAAAAAAcsqqUKpr1662rgMAAAAAAAAPEKsWOr98+fJd2xw8eNCaQwMAAAAAAOABYFUo1b59e61evTrLfcnJyfrwww/11FNP3VNhAAAAAAAAsF9WhVLVqlXTq6++qhdeeEFXr141bz906JC6dOmiWbNmZVjoHAAAAAAAALidVWtKffvtt5o/f74++OADtW/fXuPHj1d4eLi++eYblStXTnPmzFHdunVtXSsAAAAAAADshFWhlCT17t1bTZo00ahRo/TKK69Iknr27KmxY8fK1dXVZgUCAAAAAADA/lh1+54kGYahNWvW6Pjx4ypevLhMJpP27dunU6dO2bI+AAAAAAAA2CGrQqkTJ06oV69emjZtmrp27ar169drzpw5SkhIUI8ePfT5558rLS3N1rUCAAAAAADATlgVSnXu3FmXLl3St99+qwkTJsjNzU1169bVypUr1aNHD3322Wfq2bOnrWsFAAAAAACAnbAqlGrbtq1WrVqlxo0bZ9ju6uqqt99+W999912Gp/IBAAAAAAAAt7NqofMpU6bccX/Dhg21atUqqwoCAAAAAACA/bN4plRUVJSSkpIsanv16lUdOXLE6qIAAAAAAABg3ywOpZo0aaJffvnF/DomJkZt27bVgQMHMrXdsmWLBgwYYJsKAQAAAAAAYHcsDqUMw8jwOiUlRSdOnFB8fLzNiwIAAAAAAIB9s2qhcwAAAAAAAOBeEEoBAAAAAAAgzxFKAQAAAAAAIM8VyknjhIQEXb9+XZIUHR0tSYqLizNvS8c6UwAAAAAAALiTHIVS77zzjt55550M20aNGpWpnWEYMplM91YZAAAAAAAA7JbFodTIkSNzsw4AAAAAAAA8QAilAAAAAAAAkOdY6BwAAAAAAAB5zqJQavXq1TIMI8cHNwxDq1evznE/AAAAAAAA2DeLQqn3339frVu31tdff63IyMi7tj916pS++OILtWzZUpMnT77nIgEAAAAAAGBfLFpTasOGDZo9e7ZmzZql//znPypXrpyqVKmihx56SEWLFpVhGIqOjtbZs2d16NAhnT9/Xl5eXurfv7+eeeaZXD4FAAAAAAAAFDQWhVJubm56/vnnNXToUG3atEkbN27Uvn379Ouvv5pv6zOZTCpfvryCg4PVvHlzPfHEE3JycsrV4gEAAAAAAFAwWfz0PUkqVKiQWrZsqZYtW0qSUlNTFR0dLUkqWrSoHB0dbV8hAAAAAAAA7E6OQql/c3R0lLe3t61qAQAAAAAAwAPCooXOAQAAAAAAAFsilAIAAAAAAECeI5QCAAAAAABAniOUAgAAAAAAQJ4jlAIAAAAAAECeI5QCAAAAAABAnstxKLV9+3b98MMPWrt2rWJjY7Nss3//fo0bN+6eiwMAAAAAAIB9KmRpw6SkJA0dOlS7du2SYRiSJE9PT40ZM0a9evXK0Pb06dNavny5Jk+ebNtqAQAAAAAAYBcsnin17bffavfu3Ro5cqRWrlypb7/9VtWqVdO7776rt99+W2lpablZJwAAAAAAAOyIxTOl1q5dqy5dumjEiBGSJH9/fzVu3FhffPGFPvnkE125ckXTp0+Xs7NzrhULAAAAAAAA+2DxTKkzZ86oVq1ambYPGzZMH3/8sf744w8NHDhQMTExtqwPAAAAAAAAdsjiUKpo0aK6evVqlvvatm2rL774QkeOHFHfvn116dIlmxUIAAAAAAAA+2NxKBUYGKhNmzZlu79x48b6/vvvdenSJU2bNs0mxQEAAAAAAMA+WRxKtWzZUvv379f+/fuzbVOzZk398MMP8vHxsUVtAAAAAAAAsFMWL3TepUsXtWnTRk5OTndsV6lSJa1Zs0bXr1+/19oAAAAAAABgpywOpUwmk9zc3Cxq6+7uLnd3d6uLAgAAAAAAgH2z+PY9AAAAAAAAwFYIpQAAAAAAAJDnCKUAAAAAAACQ5wilAAAAAAAAkOcIpQAAAAAAAJDnrAqlAgMDtWrVqmz3r127VoGBgVYXBQAAAAAAAPtmVShlGMYd96empspkMllVEAAAAAAAAOyf1bfvZRc6xcbGasuWLSpWrJjVRQEAAAAAAMC+FbK04YwZMzRz5kxJtwKpV199Va+++mqWbQ3DUP/+/W1TIQAAAAAAAOyOxaFU9erV1adPHxmGoR9//FGNGzdWxYoVM7QxmUxydXVV1apV1apVK1vXCgAAAAAAADthcSjVrFkzNWvWTJKUkJCg3r17q2bNmrlWGAAAAAAAAOyXxaHU7SZPnmzrOgAAAAAAAPAAsSqUkm49YW/Lli2KjIxUdHR0pifymUwmjRgx4p4LBAAAAAAAgP2xKpT6888/9cILL+jChQuZwqh0hFIAAAAAAADIjlWh1IQJE5SYmKiZM2eqbt26KlKkiK3rAgAAAAAAgB2zKpQKDw/X6NGjFRISYut6AAAAAAAA8ABwsKZT6dKls71tDwAAAAAAALgbq0KpoUOHauHChYqNjbV1PQAAAAAAAHgAWHX7XlxcnNzd3dWyZUu1a9dOpUuXlqOjY4Y2JpNJzzzzjC1qBAAAAAAAgJ2xKpSaOnWq+ed58+Zl2YZQCgAAAAAAANmxKpTauHGjresAAAAAAADAA8SqUKpcuXK2rgMAAAAAAAAPEKtCqXQXL17U7t27FRUVpdatW6t06dJKTU1VTEyMPD09M60zBQAAAAAAAEhWhlKGYWjKlCn64YcflJKSIpPJJH9/f5UuXVrx8fEKCQnRCy+8wJpSAAAAAAAAyJKDNZ2++eYbzZkzR4MGDdKsWbNkGIZ5n6enp1q1aqX169fbrEgAAAAAAADYF6tCqUWLFqlz5856+eWXVbly5Uz7AwICFBERca+1AQAAAAAAwE5ZFUqdP39etWvXzna/q6urYmNjrS4KAAAAAAAA9s2qUKp48eI6f/58tvsPHz6sMmXKWF0UAAAAAAAA7JtVoVTLli01f/58RUZGmreZTCZJ0pYtW7Rs2TK1adPGNhUCAAAAAADA7lj19L0XXnhBO3fuVKdOnVS3bl2ZTCZ9/fXX+uSTT7R//34FBgZq2LBhtq4VAAAAAAAAdsKqmVKenp5auHChhgwZoosXL6pw4cLavXu3YmJiNGLE/2vvzuNzOPf/j7/vJGJJJBqJWkIJJ7ETW4+1xNbaqVqKUjRVS1U5toQeRdFSFLWdVFvUUltRtavDwUFpU6qc2hNK0IREyDa/P3xz/9yySG7Jne31fDw8Hrlnrpn5zOS6b+63a64Zom+++UYFCxbM6FoBAAAAAACQS1g1UkqSChQooMGDB2vw4MEZWQ8AAAAAAADyAKtGSgEAAAAAAADPwuqRUufPn9f69esVEhKiiIgIGYZhsd5kMumrr7565gIBAAAAAACQ+1gVSm3atEnjx4+Xg4ODypUrJxcXlyRtngypAAAAAAAAgERWhVLz589XpUqVtHTpUrm5uWV0TWkSHh6uyZMna9++fbKzs1OrVq0UEBAgJyenFNvPmzdPBw8e1PXr1+Xm5qYWLVpo+PDhKly4sI2rBwAAAAAAyNusmlPq5s2bevXVV7MskJKkUaNG6Y8//tCyZcu0aNEiHT9+XBMnTkyx/c2bN3Xz5k2NGTNGW7du1bRp03TgwAEFBATYsGoAAAAAAABIVo6U8vHx0c2bNzO6ljQ7f/68Dhw4oHXr1qlatWqSpMDAQPn7+2v06NF6/vnnk2zj7e2tefPmmV+XKVNG7733nv7xj38oLi5ODg5WT68FAAAAAACAdLIqiRk7dqyGDx+uJk2aqFatWhld01OdPHlSLi4u5kBKkho0aCA7OzsFBwerZcuWadpPZGSknJ2dnxpIGYbBHFkpSLw2XB9kF/RJZDf0SWQn9EdkN/RJZCf0R2Q3OblPprVmq0KppUuXqnDhwurVq5cqVKigEiVKyM7O8k5Ak8mkhQsXWrP7p7p161aSWwcdHBzk6uqqsLCwNO3jzp07+vzzz9W9e/ento2MjFRsbKxVteZ2hmHo/v37kh79zoGsRp9EdkOfRHZCf0R2Q59EdkJ/RHaTk/vkw4cP09TOqlDq3LlzkqQSJUooKipKf/zxR5I21lywmTNnaunSpam22bZtW7r3+6TIyEi9/fbbKl++vIYOHfrU9s7OzipUqNAzHzc3Skw/XV1dc9ybBLkTfRLZDX0S2Qn9EdkNfRLZCf0R2U1O7pOJYdrTWBVK7d2715rNnqp///7q3Llzqm1Kly4td3d33blzx2J5XFycIiIi5OHhker2kZGRGjhwoJycnLRgwQLly5fvqXWZTKYc1wFsKfH6cI2QXdAnkd3QJ5Gd0B+R3dAnkZ3QH5Hd5NQ+mdZ6s9Xs3m5ubml6op+vr6/u3r2rU6dOqWrVqpKkI0eOKCEhQdWrV09xu8jISA0YMECOjo5auHCh8ufPn2G1AwAAAAAAIO2eKZQ6evSofvzxR127dk2SVLJkSTVt2lT16tXLkOJSUr58eTVu3FgTJkzQpEmTFBsbq8mTJ6tt27bmJ+/duHFDffv21ccff6zq1asrMjJS/fv3V3R0tD755BNFRkYqMjJS0qMwzN7ePlNrBgAAAAAAwP9nVSgVExOjkSNHavfu3TIMQy4uLpKku3fvatmyZWrZsqVmzZqVplvjrDVz5kxNnjxZffv2lZ2dnVq1aqXAwEDz+tjYWF28eFHR0dGSpNOnT+uXX36RpCRP59uzZ488PT0zrVYAAAAAAABYsiqUWrBggXbt2qX+/furf//+cnd3lyTdvn1bX3zxhYKCgrRgwQK99957GVmrhSJFimjWrFkprvf09NTZs2fNr1988UWL1wAAAAAAAMg6dtZstGXLFnXu3FmjR482B1KSVLRoUf3jH/9Qp06dtHnz5gwrEgAAAAAAALmLVaFUWFhYqhOKV69eXWFhYVYXBQAAAAAAgNzNqlCqePHiOnr0aIrrjx07puLFi1tdFAAAAAAAAHI3q0KpTp066YcfftDEiRN14cIFxcfHKyEhQRcuXNAHH3yg7du3q3PnzhldKwAAAAAAAHIJqyY6HzRokK5evaq1a9fq22+/lZ3do2wrISFBhmGoc+fOGjRoUIYWCgAAAAAAgNzDqlDK3t5e06dPV79+/bR//35du3ZNklSqVCk1adJEFStWzNAiAQAAAAAAkLtYFUolqlixIgEUAAAAAAAA0u2ZQqlz585p//79Cg0NlSR5enqqcePG8vHxyZDiAAAAAAAAkDtZFUrFxMRo4sSJ+u6772QYhsWcUrNmzVL79u01ZcoUOTo6ZmixAAAAAAAAyB2sCqU++eQTbdq0Sa+//rp69+6tMmXKyGQy6fLly1q+fLlWrVolV1dXBQQEZHS9AAAAAAAAyAXsrNlo8+bN6tixoyZOnCgvLy85ODjI3t5eXl5e+uCDD9S+fXtt3rw5o2sFAAAAAABALmFVKBUXF6caNWqkuN7X11fx8fFWFwUAAAAAAIDczapQqlGjRjp48GCK6w8cOKCGDRtaXRQAAAAAAAByN6tCqeHDhyskJERDhw7V4cOHFRoaqtDQUB06dEhDhgzRtWvXNHz4cIWHh1v8AQAAAAAAACQrJzpv06aNJOncuXPas2ePxTrDMCRJbdu2TbLdmTNnrDkcAAAAAAAAchmrQqkhQ4bIZDJldC0AAAAAAADII6wKpYYNG5bRdQAAAAAAACAPsWpOKQAAAAAAAOBZWDVSSpLu37+vnTt36urVq4qIiEi2TWBgoNWFAQAAAAAAIPeyKpQ6fPiwhg8frrt376bYxmQyEUoBAAAAAAAgWVaFUpMmTVLBggU1e/Zs1ahRQ87OzhldFwAAAAAAAHIxq+aUun79ugYOHKiGDRsSSAEAAAAAACDdrAqlfHx8dO/evYyuBQAAAAAAAHmEVaHUqFGj9M033+jXX3/N6HoAAAAAAACQB1g1p1S9evU0fvx49ejRQ15eXipRooTs7CzzLZPJpIULF2ZIkQAAAAAAAMhdrAqlduzYodGjRys+Pl43btxQVFRUkjYmk+mZiwMAAAAAAEDuZFUoNWvWLJUrV06fffaZypUrl9E1AQAAAAAAIJezak6pmzdvqkePHgRSAAAAAAAAsIpVoVS1atV0/fr1jK4FAAAAAAAAeYRVoVRgYKC2bdumbdu2ZXQ9AAAAAAAAyAOsmlNq1KhRiouL08iRIzVhwgQVL1482afvbd68OUOKBAAAAAAAQO5iVShVpEgRFSlSRC+88EJG1wMAAAAAAIA8wKpQavny5RldBwAAAAAAAPIQq+aUAgAAAAAAAJ6FVSOlJCk+Pl6bN2/Wjz/+qGvXrkmSSpYsqWbNmql9+/ayt7fPsCIBAAAAAACQu1gVSt27d08DBgzQr7/+KicnJ5UuXVqSdOjQIe3cuVOrVq1SUFCQnJ2dM7RYAAAAAAAA5A5WhVKzZ8/W6dOnFRgYqG7duilfvnySpNjYWH377beaOnWqZs+erQkTJmRosQAAAAAAAMgdrJpTateuXerZs6d69eplDqQkKV++fHr99dfVs2dP7dixI8OKBAAAAAAAQO5iVSgVHh6ucuXKpbi+XLlyioiIsLooAAAAAAAA5G5WhVIvvPCC9u7dm+L6vXv3qkyZMlYXBQAAAAAAgNzNqlCqZ8+e+s9//qO33npLBw8eVEhIiEJCQnTgwAH5+/vr0KFD6tWrV0bXCgAAAAAAgFzCqonOe/XqpTt37mjJkiU6ePCg5Q4dHDRkyBC9/vrrGVIgAAAAAAAAch+rQilJGjZsmHr16qXDhw8rNDRUklSqVCnVr19fbm5uGVYgAAAAAAAAch+rQylJcnNzU9u2bTOqFgAAAAAAAOQRaZ5T6t69exowYIAWLVqUaruFCxdq4MCBioqKeubiAAAAAAAAkDulOZRasWKFTp48qW7duqXarlu3bjp58qRWrlz5zMUBAAAAAAAgd0pzKLVr1y61bdv2qfNFFS1aVG3bttXOnTufuTgAAAAAAADkTmkOpS5evKiqVaumqW2VKlV0/vx5q4sCAAAAAABA7pbmUMowjHTtOL3tAQAAAAAAkHekOZQqUaKETp8+naa2p0+fVokSJawuCgAAAAAAALlbmkOppk2bavPmzbp06VKq7S5duqTNmzeradOmz1gaAAAAAAAAcqs0h1IDBw5UgQIF1Lt3b23btk1xcXEW6+Pi4rRt2za98cYbKlCggAYMGJDhxQIAAAAAACB3cEhrw6JFi2rJkiUaOnSoRo4cqQIFCqhs2bJycnJSVFSULl26pAcPHsjd3V1LliyRu7t7ZtYNAAAAAACAHCzNoZQkVa9eXd9//71WrVqlffv26cKFC4qMjJSzs7N8fHzk5+enHj16yMXFJbPqBQAAAAAAQC6QrlBKkgoXLix/f3/5+/tnRj0AAAAAAADIA9I8pxQAAAAAAACQUQilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYXLonOkfu9ueff2r+/Pk6dOiQbt68qfz588vNzU3ly5dX1apVNXToUElSeHi4goKCdOLECf366696+PChJKlz586aPn16Vp4CAAAAAADIAQilYBYWFqauXbsqLCzMvCw2NlaRkZG6cuWK/v3vf5tDqevXr2vJkiVZVSoAAAAAAMjh0hRKbdq0yaqdd+rUyartkDVWrFhhDqTq16+vXr16qVChQgoNDVVwcLB2795tbpsvXz7VrVtXNWvW1J9//qktW7ZkVdkAAAAAACAHSlMoNXbs2HTv2GQyEUrlMKdPnzb/PG7cOPn4+Jhfd+vWTQEBAebXFSpU0IoVK2QYhpYtW0YoBQAAAAAA0iVNodSePXsyuw5kA05OTuaf58yZowEDBqh69epydHSUJBUsWDCrSgMAAAAAALlMmkKpUqVKZXYdyAYaNGig7du3S5L27t2rvXv3Kl++fKpWrZpatmypHj16qFChQllcJQAAAAAAyA3ssroAZB9du3ZV+/btLZbFxsbqxIkTmjFjhjp27KiIiIgsqg4AAAAAAOQmVj99LywsTOvWrdNvv/2me/fuKSEhwWK9yWTSV1999cwFwnbs7e01c+ZM9enTR9u3b9eRI0f0+++/m3+3V65cUVBQkN5///0srhQAAAAAAOR0VoVSv//+u9544w09ePBA5cqV07lz51ShQgXdvXtXN27cUJkyZVS8ePGMrhU2UqNGDdWoUUOSdOvWLU2aNEk7d+6UZDkZOgAAAAAAgLWsun1v1qxZKlSokLZv365ly5bJMAyNHz9e+/fv1+zZsxUREaFRo0ZldK3IZMeOHVNUVJTFMnd3d4unKD45Ig4AAAAAAMAaVo2UOnHihAYOHKiSJUsqPDxckmQYhiTplVde0U8//aSPP/5YK1asyLBCkfnWrFmj/fv36+WXX1bdunVVrFgx3b59W4sWLTK3qVatmiQpOjpa+/fvl2EYOnv2rHl9aGioebL0atWqMUk+AAAAAABIllWhVEJCgtzd3SVJLi4usre3N4dTkuTj46P169dnSIGwrbt372rt2rVau3ZtknUeHh7q06ePJOn27dsaPnx4kjZHjx7V0aNHJUnTpk1Tly5dMrdgAAAAAACQI1kVSnl6eiokJESSZGdnJ09PTx0+fFht2rSR9GgkVeHChTOuSljNMAzdu3dPDx48UIECBVS4cGGZTKZk2w4dOlQVK1bU4cOHdeXKFd26dUuxsbEqUaKEGjZsqHfeeUceHh42PgMAAAAAAJAbWRVKNWrUSNu3b9eIESMkST179tT06dN19epVGYaho0eP6s0338zQQpE+UVFR2rNnj7Zt2KDQs2el+HjJ3l6lfHzUpksXNW/eXE5OThbblC1bVgMHDtTAgQOfun9PT0+dPXtWhmEoIiJCrq6uKYZdAAAAAAAAT7IqlBo0aJDatm2r2NhY5cuXT3379tX9+/e1c+dO2dnZafDgwXr77bczulak0YkTJzQjMFAPL11SA5NJvYsUkXO+fIqMj9ehY8f0xdGjWlm2rMZMmaJatWpldbkAAAAAACAPsiqUcnV1laurq/m1yWTS4MGDNXjw4AwrDNY5ceKEPnzvPdW6dUvDSpfWc46OFusbubnpr5gYzbt0SR+OGKGJs2cTTAEAAAAAAJuze9Yd3L59W8HBwQoODtbt27czoiZYKSoqSjMCA1Xr1i0FeHklCaQSPefoqAAvL9UKC9OMwEBFRUXZuFIAAAAAAJDXWTVSSpIOHz6sTz75RGfOnLFYXqlSJY0aNUoNGjR45uKQPnv27NHDS5c0rHRp2T9lfid7k0lDS5dW/0uXtHfvXrVv395GVQIAAAAAAFgZSu3atUvDhw9X0aJFNXDgQJUtW1aSdPHiRX333Xd66623NGfOHLVs2TIja0UqDMPQtg0b1EBKcYTUk9wcHVXfZNL369erXbt2TFQOAAAAAABsxqpQas6cOfrb3/6mlStXytnZ2WLdoEGD1LNnT0IpG7t3755Cz55V7+eeS9d2DYsU0cGzZxUZGanChQtnUnUAAAAAAACWrJpT6urVq+rSpUuSQEqSnJ2d1bVrV4WEhDxzcUi7Bw8eSPHxcra3T9d2zvb2Uny8oqOjM6kyAAAAAACApKwKpby8vHTnzp0U19++fdt8Sx9so0CBApK9vSLj49O1XWR8vGRvr4IFC2ZSZQAAAAAAAElZFUr94x//0OrVq7V79+4k63bt2qU1a9ZozJgxz1wc0q5w4cIq5eOjQ+Hh6druP+HhKuXjk+yoNwAAAAAAgMxi1ZxSy5cv13PPPadhw4apWLFiKlOmjCTpypUrunnzpsqWLauvv/5aX3/9tXkbk8mkhQsXZkzVSMJkMqlNly764uhR/RUTk6bJzu/ExOiwYWjAq68yyTkAAAAAALApq0Kpc+fOSZJKlCghSQoNDZUk2dvbq0SJEnr48KG5TSJCj8zXvHlzrSxbVvMuXVKAl5fsU7nm8Yah+VevKn/ZsvLz87NhlQAAAAAAAFaGUnv37s3oOpABnJycNGbKFH04YoSmXrigoaVLyy2ZEVN3YmI0/+pVnfDw0AdTp8rJySkLqgUAAAAAAHmZVaEUsq9atWpp4uzZmhEYqP6XLqm+yaSGRYrI+f8mQf9PeLgOG4byly2rD6ZOla+vb1aXDAAAAAAA8qA0hVLXrl2TJJUsWdLi9dMktodt1apVS198+6327t2r79ev18GzZ6XYWMneXqXq1tWAV1+Vn58fI6QAAAAAAECWSVMo5efnJ5PJpF9++UWOjo7m109z5syZZy4Q1nFyclL79u3Vrl07RUZGKjo6WgULFpSzszPzewEAAAAAgCyXplDqo48+kslkUr58+SxeI/szmUwqXLiwChcunNWlAAAAAAAAmKUplOrSpUuqrwEAAAAAAID0sMvqAgAAAAAAAJD3WBVKzZ49Wx07dkxxfadOnTR//nyriwIAAAAAAEDuZlUotWPHDjVp0iTF9S+99JK2bdtmdVEAAAAAAADI3awKpa5fv64yZcqkuN7T01PXrl2zuigAAAAAAADkblaFUoUKFVJoaGiK60NCQpQ/f36riwIAAAAAAEDuZlUoVa9ePa1Zs0Y3btxIsu769etas2aNXnzxxWcuDgAAAAAAALmTgzUbDR8+XK+99pratm2rrl27qkKFCpKk//3vf1q/fr0Mw9Dw4cMztFAAAAAAAADkHlaFUl5eXlq5cqWmTJmiL7/80mJd3bp1FRAQoPLly2dEfQAAAAAAAMiFrAqlJKlixYpasWKF7ty5o5CQEEmPJjh3c3PLsOIAAAAAAACQO1kdSiVyc3MjiAIAAAAAAEC6WB1KxcfH6+DBg7p69aoiIiJkGIbFepPJpCFDhjxzgQAAAAAAAMh9rAqlfv31V7377rv6888/k4RRiQilAAAAAAAAkBKrQqlJkybpwYMHWrBggerUqSMXF5eMrgsAAAAAAAC5mFWh1NmzZzVixAj5+flldD0AAAAAAADIA+ys2ah48eIp3rYHAAAAAAAAPI1VodRbb72ltWvXKjIyMqPrAQAAAAAAQB5g1e17UVFRcnJyUsuWLdW2bVsVL15c9vb2Fm1MJpP69euXETUmKzw8XJMnT9a+fftkZ2enVq1aKSAgQE5OTk/d1jAMvfXWWzpw4IAWLFigFi1aZFqdAAAAAAAASMqqUGrGjBnmn1esWJFsm8wOpUaNGqWwsDAtW7ZMsbGxGj9+vCZOnKhZs2Y9dduvvvpKJpMp02oDAAAAAABA6qwKpfbs2ZPRdaTL+fPndeDAAa1bt07VqlWTJAUGBsrf31+jR4/W888/n+K2Z86c0RdffKH169erUaNGtioZAAAAAAAAj7EqlCpVqlRG15EuJ0+elIuLizmQkqQGDRrIzs5OwcHBatmyZbLbRUdHa+TIkZo4caI8PDzSfDzDMJjYPQWJ14brg+yCPonshj6J7IT+iOyGPonshP6I7CYn98m01mxVKJXVbt26JTc3N4tlDg4OcnV1VVhYWIrbTZs2Tb6+vumeQyoyMlKxsbFW1ZrbGYah+/fvSxK3RCJboE8iu6FPIjuhPyK7oU8iO6E/IrvJyX3y4cOHaWqXplDKz89PdnZ2+uGHH5QvXz75+fk99YKYTCbt3r07TUUkmjlzppYuXZpqm23btqVrn4n27NmjI0eOaOPGjene1tnZWYUKFbLquLldYvrp6uqa494kyJ3ok8hu6JPITuiPyG7ok8hO6I/IbnJyn0wM054mTaFUvXr1ZDKZZGdnZ/E6o/Xv31+dO3dOtU3p0qXl7u6uO3fuWCyPi4tTREREirflHTlyRFeuXFHdunUtlg8bNkx16tTR8uXLUzymyWTKcR3AlhKvD9cI2QV9EtkNfRLZCf0R2Q19EtkJ/RHZTU7tk2mtN02h1PTp01N9nVHc3NyS3JaXHF9fX929e1enTp1S1apVJT0KnRISElS9evVkt/H399drr71msax9+/YaN26cmjVr9uzFAwAAAAAAIM3s0rtBdHS0hg4dqs2bN2dGPWlSvnx5NW7cWBMmTFBwcLB++uknTZ48WW3btjU/ee/GjRt6+eWXFRwcLEny8PCQt7e3xR9JKlmypEqXLp1l5wIAAAAAAJAXpTuUKliwoA4dOqQHDx5kRj1pNnPmTHl5ealv377y9/dXrVq19OGHH5rXx8bG6uLFi4qOjs7CKgEAAAAAAJAcq56+V7t2bZ08eVLdunXL6HrSrEiRIpo1a1aK6z09PXX27NlU9/G09QAAAAAAAMgc6R4pJUkTJ07UTz/9pNmzZ+vPP//M6JoAAAAAAACQy1k1UqpDhw6Kj4/XkiVLtGTJEtnb28vR0dGijclk0k8//ZQhRQIAAAAAACB3sSqUat26dY57HCEAAAAAAACyD6tCqenTp2d0HQAAAAAAAMhD0hVKPXz4UHv27FFISIiee+45vfTSSypWrFhm1QYAAAAAAIBcKs2h1O3bt9WjRw+FhITIMAxJUsGCBbVgwQI1aNAg0woEAAAAAABA7pPmp+99/vnnCg0NVb9+/bR48WKNHz9e+fPn18SJEzOzPgAAAAAAAORCaR4pdfDgQXXs2FFjxowxL3N3d9fIkSN14cIFeXl5ZUqBAAAAAAAAyH3SPFLq+vXrql27tsWy2rVryzAM3b59O8MLAwAAAAAAQO6V5lAqJiZG+fPnt1jm6OgoSYqLi8vYqgAAAAAAAJCrpevpe6GhoTp9+rT59b179yRJly9flouLS5L2VapUecbyAAAAAAAAkBulK5SaO3eu5s6dm2T5pEmTLF4bhiGTyaQzZ848W3UAAAAAAADIldIcSk2bNi0z6wAAAAAAAEAekuZQqnPnzplZBwAAAAAAAPKQNE90DgAAAAAAAGQUQikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzhFIAAAAAAACwOUIpAAAAAAAA2ByhFAAAAAAAAGyOUAoAAAAAAAA2RygFAAAAAAAAmyOUAgAAAAAAgM0RSgEAAAAAAMDmCKUAAAAAAABgc4RSAAAAAAAAsDlCKQAAAAAAANgcoRQAAAAAAABsjlAKAAAAAAAANkcoBQAAAAAAAJsjlAIAAAAAAIDNEUoBAAAAAADA5gilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzhFIAAAAAAACwOUIpAAAAAAAA2ByhFAAAAAAAAGyOUAoAAAAAAAA2RygFAAAAAAAAmyOUAgAAAAAAgM0RSgEAAAAAAMDmCKUAAAAAAABgc4RSAAAAAAAAsDlCKQAAAAAAANgcoRQAAAAAAABsjlAKAAAAAAAANkcoBQAAAAAAAJsjlAIAAAAAAIDNEUoBAAAAAADA5gilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzhFIAAAAAAACwOUIpAAAAAAAA2ByhFAAAAAAAAGyOUAoAAAAAAAA2RygFAAAAAAAAmyOUAgAAAAAAgM0RSgEAAAAAAMDmCKUAAAAAAABgc4RSAAAAAAAAsDlCKQAAAAAAANgcoRQAAAAAAABsjlAKAAAAAAAANkcoBQAAAAAAAJsjlAIAAAAAAIDNEUoBAAAAAADA5gilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzDlldAJAd/Pnnn5o/f74OHTqkmzdvKn/+/HJzc1P58uVVtWpVDR06VJL022+/adu2bTp+/LhCQ0P1119/ydnZWTVr1tTAgQNVp06dLD4TAAAAAAByBkIp5HlhYWHq2rWrwsLCzMtiY2MVGRmpK1eu6N///rc5lFq9erXWrFljsf1ff/2lffv2af/+/Zo7d65atWpl0/oBAAAAAMiJCKWQ561YscIcSNWvX1+9evVSoUKFFBoaquDgYO3evduivYeHh1599VXVrl1bd+/e1fz583Xx4kUlJCRo+vTphFIAAAAAAKQBoRTyvNOnT5t/HjdunHx8fMyvu3XrpoCAAPPrDh06aNy4cSpYsKB5WYUKFdSxY0dJUmhoqG7fvq2iRYvaoHIAAAAAAHIuJjpHnufk5GT+ec6cOTp+/LhiYmLMyx4PoOrUqWPxWpJeeOEFi9cFChTIpEoBAAAAAMg9CKWQ5zVo0MD88969e9WrVy/VqlVLPXv21BdffKH79++nuv2OHTvMP9epU8ci5AIAAAAAAMkjlEKe17VrV7Vv395iWWxsrE6cOKEZM2aoY8eOioiISHbbU6dOacqUKZIkR0dHjRs3LtPrBQAAAAAgNyCUQp5nb2+vmTNnau3aterfv78qV64sO7v//9a4cuWKgoKCkmx3/Phx9e3bV/fu3ZODg4M+/fRTVa1a1ZalAwAAAACQYzHROfB/atSooRo1akiSbt26pUmTJmnnzp2SLCdDl6SDBw9q6NChio6OlqOjo2bPnq0WLVrYvGYAAAAAAHIqRkohzzt27JiioqIslrm7u6tTp07m1wkJCeafd+3apUGDBik6OlqFChXSkiVLCKQAAAAAAEgnRkohz1uzZo3279+vl19+WXXr1lWxYsV0+/ZtLVq0yNymWrVqkqQffvhBI0eOVHx8vEwmk4YMGaJ8+fLp+PHj5rbVq1eXo6Ojzc8DAAAAAICchFAKkHT37l2tXbtWa9euTbLOw8NDffr0kSTt379f8fHxkiTDMPTJJ58kab9nzx55enpmbsEAAAAAAORwOTaUCg8P1+TJk7Vv3z7Z2dmpVatWCggIkJOTU6rbnTx5UrNnz1ZwcLDs7OxUqVIlBQUFqUCBAjaqHLZiGIbu3bunBw8eqECBAipcuLBMJlOSdkOHDlXFihV1+PBhXblyRbdu3VJsbKxKlCihhg0b6p133pGHh0cWnAEAAAAAALmXyTAMI6uLsMbAgQMVFhamDz/8ULGxsRo/fryqVaumWbNmpbjNyZMnNXDgQL399ttq1qyZ7O3t9fvvv6tFixbJ3m51//59nTlzRpUqVVKhQoUy83RyLMMwFBERIVdX12QDn6wQFRWlPXv2aNuGDQo9e1aKj5fs7VXKx0dtunRR8+bNnxpeIufKjn0SeRt9EtkJ/RHZDX0S2Qn9EdlNTu6Tac1TcuRIqfPnz+vAgQNat26dea6fwMBA+fv7a/To0Xr++eeT3W7atGnq06eP/P39zcu8vLxsUjNs48SJE5oRGKiHly6pgcmk3kWKyDlfPkXGx+vQsWP64uhRrSxbVmOmTFGtWrWyulwAAAAAAPKsHPn0vZMnT8rFxcUcSElSgwYNZGdnp+Dg4GS3uX37tn755RcVLVpUPXr0UIMGDdS7d2+LCaqRs504cUIfvveeqly6pGWlS2u0l5caubmppqurGrm5abSXl5aVLq0qly7pwxEjdOLEiawuGQAAAACAPCtHjpS6deuW3NzcLJY5ODjI1dVVYWFhyW5z9epVSdL8+fM1evRoVapUSZs2bVK/fv20detWlS1bNsXjGYahHHqXY6ZLvDZZfX2ioqI0IzBQvrduabyXl+xNJiVXURFHR4338tLUCxc0IzBQQWvXcitfLpNd+iSQiD6J7IT+iOyGPonshP6I7CYn98m01pytQqmZM2dq6dKlqbbZtm2bVftOSEiQJHXv3l2vvvqqJKly5co6fPiw1q9fr5EjR6a4bWRkpGJjY606bm5nGIbu378vSVl6j+u2bdt0//x5vePpKSMuTnFPaT+oeHENPH9eW7Zs0SuvvGKTGmEb2aVPAonok8hO6I/IbuiTyE7oj8hucnKffPjwYZraZatQqn///urcuXOqbUqXLi13d3fduXPHYnlcXJwiIiJSfEpa4vLy5ctbLC9fvryuXbuW6jGdnZ2Z6DwFielnVk68ZhiG9u/cqUb29vJI4++pWL58aujgoB937FD37t1z3BscKcsOfRJ4HH0S2Qn9EdkNfRLZCf0R2U1O7pOJYdrTZKtQys3NLcltecnx9fXV3bt3derUKVWtWlWSdOTIESUkJKh69erJbuPp6alixYrp4sWLFssvXbqkJk2apHo8k8mU4zqALSVen6y6Rvfu3dO1s2fV57nnlJ4KGhUpov+cPauoqCgVLlw40+qD7WV1nwSeRJ9EdkJ/RHZDn0R2Qn9EdpNT+2Ra682RE52XL19ejRs31oQJExQcHKyffvpJkydPVtu2bc1P3rtx44Zefvll88TnJpNJAwYM0PLly7V9+3ZdvnxZc+bM0YULF9S1a9esPB08owcPHkjx8XK2t0/Xds729lJ8vKKjozOpMgAAAAAAkJJsNVIqPWbOnKnJkyerb9++srOzU6tWrRQYGGheHxsbq4sXL1oEDv369VNMTIymTZumiIgIVaxYUV988YXKlCmTFaeADFKgQAHJ3l6R8fHp2i4yPl6yt1fBggUzqTIAAAAAAJCSHBtKFSlSRLNmzUpxvaenp86ePZtkub+/v/z9/TOzNNhY4cKFVcrHR4eOHVOjNNz+meg/4eEqVbeunJ2dM7E6AAAAAACQnBx5+x7wOJPJpDZduuiQYeivmJg0bXMnJkaHDUNtX301x92bCwAAAABAbkAohVyhefPmyl+2rOZdvar4/3tCQUriDUPzr15V/rJl5efnZ6MKAQAAAADA4wilkCs4OTlpzJQpOuHhoakXLuhOCiOm7sTEaOqFCzrh4aGxU6fKycnJxpUCAAAAAAApB88pBTypVq1amjh7tmYEBqr/pUuqbzKpYZEicv6/SdD/Ex6uw4ah/GXL6oOpU+Xr65vVJQMAAAAAkGcRSiFXqVWrlr749lvt3btX369fr4Nnz0qxsZK9vUrVrasBr74qPz8/RkgBAAAAAJDFCKWQ6zg5Oal9+/Zq166dIiMjFR0drYIFC8rZ2ZlJzQEAAAAAyCYIpZBrmUwmFS5cWIULF87qUgAAAAAAwBOY6BwAAAAAAAA2RygFAAAAAAAAmyOUAgAAAAAAgM0RSgEAAAAAAMDmCKUAAAAAAABgc4RSAAAAAAAAsDlCKQAAAAAAANgcoRQAAAAAAABsjlAKAAAAAAAANkcoBQAAAAAAAJsjlAIAAAAAAIDNEUoBAAAAAADA5gilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzDlldQHaWkJAgSYqOjs7iSrIvwzD08OFD3b9/XyaTKavLAeiTyHbok8hO6I/IbuiTyE7oj8hucnKfTMxREnOVlBBKpeLhw4eSpEuXLmVtIQAAAAAAADnMw4cP5ezsnOJ6k2EYhg3ryVHi4uIUERGh/Pnzy86OOx0BAAAAAACeJiEhQQ8fPpSrq6scHFIeD0UoBQAAAAAAAJtj+A8AAAAAAABsjlAKAAAAAAAANkcohXQLDw/XyJEjVatWLdWpU0fjx49XVFRUmrY1DEMDBw6Uj4+Pdu/encmVIq9Ib58MDw/X5MmT1bp1a1WvXl1NmzbVlClTdO/ePRtWjdxi5cqV8vPzU7Vq1fTaa68pODg41fY//PCDXn75ZVWrVk3t27fX/v37bVQp8or09Mm1a9fq9ddfV926dVW3bl3169fvqX0YSK/0fk4m+v777+Xj46PBgwdncoXIS9LbH+/evatJkyapUaNGqlq1qlq3bs3f3chQ6e2TX375pfl7zEsvvaSPPvrI/JC2nIhQCuk2atQo/fHHH1q2bJkWLVqk48ePa+LEiWna9quvvspxj7JE9pfePnnz5k3dvHlTY8aM0datWzVt2jQdOHBAAQEBNqwaucG2bds0bdo0DRkyRBs3blTFihU1YMAA3b59O9n2J06c0MiRI9W1a1dt2rRJzZs315AhQ3Tu3DkbV47cKr198r///a/atm2rr7/+WqtXr1aJEiXUv39/3bhxw8aVI7dKb59MFBISohkzZqhOnTo2qhR5QXr7Y0xMjN58802FhoZq7ty52r59uyZPnqznn3/expUjt0pvn9yyZYtmzZqloUOHatu2bZo6daq2bdumTz/91MaVZyADSIc//vjD8Pb2NoKDg83L9u/fb/j4+Bh//vlnqtv+9ttvRuPGjY2bN28a3t7exq5duzK7XOQBz9InH7dt2zajSpUqRmxsbGaUiVyqa9euxqRJk8yv4+PjjUaNGhmLFy9Otv3w4cMNf39/i2WvvfaaMWHChEytE3lHevvkk+Li4gxfX19j48aNmVQh8hpr+mRcXJzRvXt3Y+3atcaYMWOMd955xxalIg9Ib3/85ptvjObNmxsxMTG2KhF5THr75KRJk4w33njDYtm0adOMHj16ZGqdmYmRUkiXkydPysXFRdWqVTMva9Cggezs7FIdZhgdHa2RI0dq4sSJ8vDwsEWpyCOs7ZNPioyMlLOzc6qPKwUeFxMTo9OnT6tBgwbmZXZ2dmrQoIFOnjyZ7DY///yz6tevb7GsUaNG+vnnnzOzVOQR1vTJJ0VHRysuLk6urq6ZVSbyEGv75IIFC1S0aFG99tprtigTeYQ1/XHv3r2qWbOmPvzwQzVo0EDt2rXTokWLFB8fb6uykYtZ0yd9fX11+vRp8/ecq1evav/+/XrppZdsUnNm4NsX0uXWrVtyc3OzWObg4CBXV1eFhYWluN20adPk6+urFi1aZHaJyGOs7ZOPu3Pnjj7//HN17949M0pELvXXX38pPj5eRYsWtVhetGhRXbhwIdltbt26JXd39yTtb926lWl1Iu+wpk8+aebMmSpWrJjFP5ABa1nTJ48fP65169Zp06ZNNqgQeYk1/fHq1as6cuSI2rdvryVLlujKlSuaNGmS4uLiNHToUFuUjVzMmj7Zvn17/fXXX3r99ddlGIbi4uLUo0cPDRo0yBYlZwpCKUh69I/QpUuXptpm27ZtVu17z549OnLkiDZu3GjV9sibMrNPPi4yMlJvv/22ypcvzz8uAORpS5Ys0bZt2/T1118rf/78WV0O8qDIyEiNHj1akydPTvIfTkBWMAxDRYsW1eTJk2Vvb6+qVavqxo0bCgoK4t+NyBL//e9/tXjxYn3wwQeqXr26rly5oqlTp2rBggUaMmRIVpdnFUIpSJL69++vzp07p9qmdOnScnd31507dyyWx8XFKSIiIsXb8o4cOaIrV66obt26FsuHDRumOnXqaPny5c9WPHKlzOyTiSIjIzVw4EA5OTlpwYIFypcv3zPXjbzjueeek729fZKJKG/fvp1kNFQid3f3JKOiUmsPpIc1fTJRUFCQlixZomXLlqlixYqZWSbykPT2yatXryo0NFTvvPOOeVlCQoIkqXLlytq+fbvKlCmTuUUj17LmM9LDw0MODg6yt7c3L/Py8lJYWJhiYmLk6OiYqTUjd7OmT86dO1cdOnQw397s4+Oj+/fva+LEiXrnnXdkZ5fzZmgilIIkyc3NLU3/I+Xr66u7d+/q1KlTqlq1qqRHoVNCQoKqV6+e7Db+/v5J5gRo3769xo0bp2bNmj178ciVMrNPSo8CqQEDBsjR0VELFy5kVADSzdHRUVWqVNHhw4fNtyYnJCTo8OHD6t27d7Lb1KxZU0eOHFG/fv3Myw4dOqSaNWvaoGLkdtb0SUlaunSpFi1apKCgIIv5+YBnld4+6eXlpS1btlgsmzNnjqKiohQQEKDixYvbpG7kTtZ8RtaqVUtbt25VQkKC+cv+pUuX5OHhQSCFZ2ZNn3zw4EGS4CkxNDUMI3MLziQ5L0ZDlipfvrwaN26sCRMmKDg4WD/99JMmT56stm3bmh+NeuPGDb388svmydc8PDzk7e1t8UeSSpYsqdKlS2fZuSB3sKZPRkZGqn///rp//76mTp2qyMhIhYWFKSwsjIkrkS5vvvmm1q5dq40bN+r8+fP65z//qejoaHXp0kWSNHr0aM2aNcvc/o033tCBAwf0xRdf6Pz585o3b55OnTqVamAApEd6++SSJUs0d+5cffTRRypVqpT5szAqKiqrTgG5THr6ZP78+ZP8m9HFxUVOTk7y9vYmBMAzS+9nZM+ePRUeHq6pU6fq4sWL+vHHH7V48WL16tUrq04BuUx6+2SzZs20atUqff/997p69ar+85//aO7cuWrWrJnFiL6chJFSSLeZM2dq8uTJ6tu3r+zs7NSqVSsFBgaa18fGxurixYuKjo7OwiqRl6S3T54+fVq//PKLJKlly5YW+9qzZ488PT1tVzxytDZt2ujOnTv67LPPFBYWpkqVKulf//qXecj19evXLf43q1atWpo5c6bmzJmjTz/9VGXLltWCBQvMYT3wrNLbJ1evXq3Y2Fi9++67FvsZOnSohg0bZtPakTult08CmSm9/bFEiRIKCgrStGnT1KFDBz3//PN644039NZbb2XVKSCXSW+ffOedd2QymTRnzhzduHFDbm5uatasmUaMGJFVp/DMTEZOHeMFAAAAAACAHIv/lgAAAAAAAIDNEUoBAAAAAADA5gilAAAAAAAAYHOEUgAAAAAAALA5QikAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAGRbY8eOlZ+fX6btf8OGDfLx8VFISEimHSMkJEQ+Pj7asGFDph0D2VtUVJTq16+vzZs3Z3UpkqQ+ffqoT58+Vm3r4+OjefPmZXBFafMsdWeW7FgTnt0ff/yhypUr69y5c1ldCgDkeg5ZXQAAIHfasGGDxo0bZ37t6OiokiVLqmHDhho8eLDc3d2zsLrs69atWwoKCtK+fft0/fp1mUwmeXl5qUWLFurdu7dcXFyyusQcbf/+/QoODtawYcPS1D44OFgbNmxQcHCwzp49q7i4OJ09ezZdx/z666/l5OSktm3bmpfNmzdP8+fPT3GbgwcPysPDI13HyetOnz6tLl26aNCgQRoxYkSybS5duqTWrVurX79+Fp9PSJ6Pj0+yy0eOHCl/f3/z6wsXLmj16tUKDg7W6dOnFRMToz179sjT0zNNxxk7dqw2btyYZHm5cuW0fft28+uQkBA1b9482X18+umnFu+x3bt3a+bMmQoLC1PdunU1adIkPf/88xbbDBo0SB4eHpo8ebLF8goVKuill17SZ599lur7FADw7AilAACZ6t1335Wnp6diYmL0008/adWqVdq/f7+2bt2qggULprrt5MmTZRhGptXWsWNHtW3bVo6Ojpl2jPQIDg6Wv7+/7t+/rw4dOqhKlSqSpFOnTmnp0qU6fvy4vvjiiyyuMmfbv3+/Vq5cmeZQav/+/Vq3bp28vb3l6empS5cupet4sbGx+vrrr9WvXz/Z29snWf/Pf/5ThQoVSrI8u4aPwcHByZ5HdlClShV5eXnp+++/TzGU2rp1qySpQ4cOGXLMoKCgDNlPdtawYUN17NjRYlnlypUtXv/8889avny5KlSooPLly+vMmTPpPo6jo6OmTJlisaxw4cLJtm3Xrp2aNGlisaxmzZrmn69evar33ntPbdq0Uc2aNfXVV19p/PjxFr+vAwcO6Pjx49q5c2eyx+jRo4f8/f115coVlSlTJt3nAwBIG0IpAECmatKkiapVqyZJeu2111SkSBEtW7ZMe/bsUbt27ZLd5v79+ypUqJDy5cuXqbXZ29tnmy/Yd+/e1dChQ2Vvb6+NGzeqfPnyFutHjBihtWvXZlF1eVfPnj311ltvqUCBAvrwww/THUr9+OOPunPnjl555ZVk17du3Vpubm4ZUKlt5M+fP6tLSFX79u01d+5c/fzzzxYhRaKtW7fKy8vLHPhaKzo6WgULFsw2gXZmKlu2bJJQ6kl+fn46duyYnJ2dFRQUZFUo5eDg8NTjJKpcuXKqbQ8ePKjixYtrxowZMplMKl++vPr27auHDx8qf/78iouL07Rp0zRkyJAU338NGjSQq6urNm7cqOHDh6f7fAAAacOcUgAAm/r73/8uSeZ5nMaOHStfX19duXJFb731lnx9fTVq1CjzusfnlEqcnykoKEhr1qxRixYtVLVqVb366qsKDg5Ocqzz589r+PDh+vvf/67q1aurdevWmj17tnl9cnNK+fn56e2339bBgwfVsWNHVatWTW3atEnyv+nh4eGaMWOG2rdvL19fX9WqVUsDBw7U77//btV1Wb16tW7cuKGxY8cmCaQkyd3dXYMHD7ZYtnLlSrVt21ZVq1ZVo0aNNGnSJN29e9eiTZ8+fdSuXTv9/vvv6t27t2rUqKGWLVuab4k5evSoXnvtNfP1OXTokMX28+bNk4+Pj/la1qpVSy+++KKmTJmihw8fWrSNi4vTggULzL8XPz8/ffrpp4qJibFol3iNjx8/rq5du6patWpq3ry5Nm3alOS87969q6lTp+qll15S1apV1bJlSy1ZskQJCQnmNmntF2PHjtXKlSslPbotKfFPatzd3VWgQIFU26Rm9+7dKlWqlNUjLcaMGaNq1arp/PnzFssHDBigunXr6saNG5L+f18+duyYJk6cqBdffFG1atXS6NGjFRERkeoxYmJiNHfuXHXp0kW1a9dWzZo19frrr+vIkSNJ2j45p1Ri/7h8+bLGjh2rOnXqqHbt2ho3bpyio6OTbP/dd9+pS5cuql69uurVq6cRI0bo+vXrSdol/h6rV6+url276vjx42m6Xu3bt5f0/0dEPe7UqVO6ePGiuc3u3bvl7++vRo0aqWrVqmrRooUWLFig+Ph4i+0S30OnTp1Sr169VKNGDX366afmdY/PKZXWa5nRn2WSdOPGDY0bN04NGjRQ1apV1bZtW61bty7Jvq5du5akPz3NgwcPkrzfH1ekSBE5Ozuna5/JiY+PV2RkZJra3r9/P8lnS6KHDx/KxcVFJpNJkuTq6irDMPTgwQNJ0ooVKxQfH6/evXunuP98+fKpXr162rNnTzrPAgCQHoRSAACbunLliqRHX2ISxcXFacCAASpatKjGjBmjVq1apbqPrVu3KigoSN27d9d7772n0NBQDRs2TLGxseY2v//+u7p166YjR46oW7duCggIUIsWLbR3796n1njp0iWNGDFCTZo00ciRI2Vvb6/hw4frP//5j7nN1atXtXv3bjVt2lRjx47VgAEDdO7cOfXu3dscFKTH3r17VaBAAbVu3TpN7efNm6cPP/xQxYoV09ixY9W6dWutWbNG/fv3t7gOkhQREaFBgwapevXq+sc//iFHR0e9//772rZtm95//3299NJLGjlypKKjo/Xuu+8m+6Xwvffe08OHDzVy5Eg1adJEy5cv14QJEyzaBAYG6rPPPlPlypU1btw41a1bV4sXL072VqrLly9r+PDhatiwocaOHStXV1eNHTtW//vf/8xtoqOj1bt3b23evFmdOnVSYGCgatWqpU8//VTTpk1Lss+n9Yvu3burYcOGkqSPP/7Y/CcznTx5MtVROREREbpz547Fn8eDxYCAALm5uWnMmDHmsGT16tU6ePCgAgMDk8yR8+GHH+r8+fMaOnSoOnXqpC1btmjIkCGp3gYbGRmpb7/9VvXq1dOoUaM0dOhQ3blzRwMHDkzziJf33ntPUVFRev/99/XKK69ow4YNSebiWbhwocaMGaMXXnhBY8eO1RtvvKHDhw+rV69eFuf87bffauLEiXJ3d9c//vEP1apVS++8806y4dWTSpcuLV9fX/3www9JwqXEoCoxlNq4caMKFSqkN998UwEBAapSpYo+++wzzZw5M8l+w8PD9dZbb6lSpUoaP368XnzxxWSPn95rmVGfZbdu3VK3bt3M1zMgIEBlypRRQECAvvzyS4tjjhkzRm3atHnqtUy0ceNG1axZU9WrV1ebNm20ZcuWNG+bHtHR0apdu7Zq166tevXqadKkSYqKikq27fz58+Xr66vq1avr1Vdf1cGDBy3WV61aVb/99pu2bt2qq1evatGiRXrhhRfk6uqqO3fuaMGCBRo3btxTR+NWqVJF//vf/9IclAEArGAAAJAJ1q9fb3h7exuHDh0ybt++bVy/ft34/vvvjXr16hnVq1c3/vzzT8MwDGPMmDGGt7e3MXPmzCT7GDNmjNGsWTPz66tXrxre3t5GvXr1jPDwcPPy3bt3G97e3sbevXvNy3r16mX4+voaoaGhFvtMSEhIUuPVq1fNy5o1a2Z4e3sbO3bsMC+7d++e0bBhQ6NTp07mZQ8fPjTi4+Mt9n316lWjatWqxvz585PUvH79+lSvV926dY0OHTqk2ibR7du3jSpVqhj9+/e3qGHFihWGt7e3sW7dOvOy3r17G97e3saWLVvMy86fP294e3sbFStWNH7++Wfz8gMHDiSp9bPPPjO8vb2NQYMGWdTwz3/+0/D29jbOnDljGIZhnDlzxvD29jYCAgIs2k2fPt3w9vY2Dh8+bF6WeI2PHTtmcU5Vq1Y1pk+fbl62YMECo2bNmsbFixct9jlz5kyjUqVKxrVr1wzDSF+/mDRpkuHt7Z3cZX2q9G4bGxtr+Pj4WJxTosTrmtyf1q1bW7RN/L18/vnnxpUrV4yaNWsagwcPtmiT2Jc7d+5sxMTEmJcvXbrU8Pb2Nnbv3m1e1rt3b6N3797m13FxccbDhw8t9hcREWE0aNDAGDdunMVyb29v47PPPktyHk+2GzJkiFGvXj3z65CQEKNSpUrGwoULLdqdPXvWqFy5snl5TEyMUb9+faNjx44WNa1Zs8bw9va2qDslie+DAwcOmJfFx8cbjRs3Nrp3725eFh0dnWTbCRMmGDVq1LA4duJ7aNWqVUnaW3stM/qzbPz48UbDhg2NO3fuWLQZMWKEUbt2bYtzTTyftOjevbvx5ZdfGrt37za++eYbo127doa3t7excuXKFLf517/+leRz9WlmzpxpfPLJJ8b3339vbN261fz3Qo8ePYzY2Fhzu9DQUKN///7GN998Y+zZs8f48ssvjaZNmxoVK1Y09u3bZ7HPyZMnm99T9erVM38GBQYGGgMGDEhTXVu2bDG8vb2NX375Jc3nAgBIH0ZKAQAyVb9+/VS/fn299NJLGjFihJycnDR//vwkIzx69uyZ5n22adNGrq6u5td16tSR9Gj0kiTduXNHx44d06uvvqqSJUtabJt4O0dqihUrppYtW5pfOzs7q1OnTvrtt98UFhYm6dGkvHZ2j/4ajY+P119//aVChQqpXLly+u2339J8LokiIyPl5OSUpraHDh1SbGys3njjDXMN0qM5u5ydnbV//36L9oUKFbJ4KpWXl5dcXFxUvnx51ahRw7w88efE6/i4Xr16WbxOvO3l3//+tySZj/nmm29atOvfv7/F+kQVKlQw/94kyc3NTeXKlbM49vbt21W7dm25uLhYjCRq0KCB4uPjdezYMYt9Pq1f2FpERIQMw0h10vJ58+Zp2bJlFn+eHAXWqFEjde/eXQsWLNCwYcOUP39+ffjhh8nur3v37hajP3r27CkHB4ck1/9x9vb25rmREhISFB4erri4OPNok7To0aOHxes6deooPDzcPMJk165dSkhI0CuvvGLxu3R3d9cLL7yg//73v5Ie3WJ3+/Zt9ejRw2K+ps6dO6c46fWT2rRpo3z58lncwnf06FHduHHDPEpKksVtmZGRkbpz547q1Kmj6OhoXbhwwWKfjo6O6tKly1OPnd5rmRGfZYZhaOfOnfLz85NhGBbXt1GjRrp3755Onz5t3m758uVpfoLk6tWr1bdvXzVv3lw9e/bU+vXr5e3trdmzZ5tvhcsII0eO1KhRo9SmTRu1bdtW06dP14gRI3TixAnt2LHD3K5kyZIKCgpSz5495efnp759+2rjxo1yc3PT9OnTLfYZGBioffv26dtvv9XevXv197//XWfOnNGmTZs0btw43bt3T6NGjVLjxo3Vp0+fZG9pTHzv/vXXXxl2rgAAS0x0DgDIVBMnTlS5cuVkb28vd3d3lStXziJIkR5NcFu8ePE077NEiRIWrxO/1CXeApT4hc7b29uqml944YUk4VXZsmUlSaGhofLw8FBCQoK+/vprffPNNwoJCbG4VejxWxPTytnZOcVbVZ507do1SY/Cpcc5OjqqdOnSCg0NtVhevHjxJOdTuHDhJNc88Uv/k/NSSY+uyePKlCkjOzs783xcoaGhsrOzSzJ3koeHh1xcXJLU9OTvUHr0e3x8/qPLly/r7Nmzql+/fpK20qMv7Knt88l+kVWMVG6dq1OnTpomOh8zZoz27t2rM2fOaNasWSpatGiy7Z78PTk5OcnDwyPJ9X/Sxo0b9cUXX+jixYsWt455eno+tTZJSQKTxC/zERERcnZ21qVLl2QYRoq35jo4PPonaWLffvI88uXLp9KlS6eplueee06NGjXSrl27NGnSJOXPn19bt26Vg4ODxYTz//vf/zRnzhwdOXIkye1Z9+7ds3j9/PPPp3lS8/Rcy4z4LEu85XPNmjVas2ZNim0ygqOjo3r16qUPPvhAp06dsgiWM1q/fv00d+5cHTp0yCJUf1KRIkXUpUsXLVmyRH/++afF51rJkiUt+uaUKVPUo0cPlS9fXqNGjdKff/6pzz//XJs2bdKgQYP0ww8/mPuilPp7FwCQMQilAACZqnr16uan76Xk8VFHaZHSE/Ns+QVi0aJFmjt3rl599VUNHz5crq6usrOz00cffWRVHV5eXjpz5oxiYmIy/IleKV2vZ7mOKY04S8tItNSO/biEhAQ1bNhQAwcOTHZ9YlD4tH1m1RdLV1dXmUymDAnFzpw5o9u3b0uSzp0798z7e9x3332nsWPHqkWLFua53ezt7bV48eI0jzJL6f2beO0TEhJkMpm0dOnSZH9PhQoVsv4EktGhQwft27dP+/btk5+fn3bu3KmGDRuaA8C7d++qd+/ecnZ21rvvvqsyZcoof/78On36tGbOnGkxkb6kNE92n95rmRF9NrHWDh06qHPnzsm2edqE/umRGKQ9bQL9Z1WgQAEVKVIkTcdJDKLCw8NT/A+Obdu26fz581q4cKHi4+O1fft2BQUFqVq1avrb3/6mtWvX6ueff7YI2hLfu88991wGnBEAIDmEUgCAXCdxRIW1X94vX74swzAsApZLly5JkkqVKiVJ2rFjh1588UV99NFHFtvevXvXqi8wzZo108mTJ7Vz5061a9cu1baJ//N/4cIFi9EjMTExCgkJUYMGDdJ9/Ke5fPmyxbEuX76shIQE8+iPUqVKKSEhQZcvX7Z4euCtW7d09+5d83VLjzJlyuj+/fsZej5pDc0ygoODg8qUKWPxdEdr3L9/X+PGjVOFChXk6+urf/3rX+Yn0z3p8uXL5idcSlJUVJTCwsLUpEmTFPe/Y8cOlS5dWvPnz7e4Pp999tkz1f24MmXKyDAMeXp6qly5cim2S+zbly9fthghFxsbq5CQEFWsWDFNx/Pz85OTk5N5hFRERITFrXtHjx5VeHi45s+fr7p165qXP+vvKqOvZVo+y9zc3OTk5KSEhIRMee8/KTFcS8sIv2cRGRmpv/76K03HSfy9pdQ2OjpaH3/8sYYPHy4XFxfdunVLsbGxKlasmKRHAZirq6tu3ryZZL92dnap9lkAwLNhTikAQK7j5uamunXrav369ebbgRKlZQTCzZs3tWvXLvPryMhIbdq0SZUqVZKHh4ekRyMcntzXDz/8YNWT96RHc/J4eHho+vTpunjxYpL1t2/f1ueffy5JatCggfLly6fly5db1LBu3Trdu3dPL730klU1pGblypUWr1esWCFJ5rAj8ZhfffWVRbtly5ZZrE+PV155RSdPntSBAweSrLt7967i4uLSvc+CBQuat7eFmjVr6tSpU8+0j5kzZ+r69euaPn26xo4dq1KlSmns2LGKiYlJ0nbNmjUWt4ytWrVKcXFxqYZSiaN1Hu9Lv/zyi37++ednqvtxrVq1kr29vebPn5/kfWMYhnnOnqpVq8rNzU2rV6+2OL+NGzem63dWoEABtWzZUvv379eqVatUqFAhNW/e3Lw+cWTX47XExMTom2++ser8EmX0tUzLZ5m9vb1at26tHTt2JBtePXnr3rVr15KdP+lp20mPPgu/+uorPffcc6k+VTI1V65cMT+FVZIePnyY7NPtPv/8cxmGocaNG6da040bN7R+/Xr5+PiYQ6YnLV26VC4uLurWrZukR7f8OTg4mOcOe3yOs8edPn1aFSpUSPN8ZgCA9GOkFAAgVwoMDFTPnj3VuXNnde/eXZ6engoNDdWPP/6o7777LtVty5Ytq4CAAP36668qWrSo1q9fr9u3b1tMQN20aVPzY8V9fX117tw5bdmyJc3z3jzJ1dVVCxYskL+/vzp16qQOHTqYv/QlPtrc19dX0qMvqm+//bbmz5+vgQMHys/PTxcvXtQ333yjatWqqUOHDlbVkJqQkBANGjRIjRs31s8//6zNmzerXbt25pErFStWVOfOnbVmzRrdvXtXdevW1a+//qqNGzeqRYsWFqN30mrAgAHau3evBg0apM6dO6tKlSqKjo7WuXPntGPHDu3ZsyfdozUSr+mUKVPUqFEj2dvbpzpfTWhoqLm/JIZLieFgyZIl1alTp1SP17x5c3333Xe6ePFisqMtduzYkeytaw0bNpS7u7sOHz6sb775RkOHDjXXPm3aNPXp00dz5szR6NGjLbaLjY1Vv3799Morr5j7RO3atS0CmSc1bdpUO3fu1JAhQ9S0aVOFhIRo9erVqlChgu7fv5/q+aVVmTJl9N5772nWrFkKDQ1VixYt5OTkpJCQEO3evVvdunXTgAEDlC9fPr333nuaOHGi+vbtqzZt2igkJEQbNmxI93urQ4cO2rRpkw4ePKj27dtbXGdfX1+5urpq7Nix6tOnj0wmk7777rtnvtUzM65lWj7LRo4cqf/+97/q1q2bXnvtNVWoUEERERE6ffq0Dh8+rKNHj5r3N2bMGB09evSpk52vXLlSu3fvVrNmzVSyZEndvHlTGzZs0LVr1/Txxx9b3GZ87949LV++XJJ04sQJ8/aFCxeWi4uL+cEI0qO5oiRp7969kqSwsDB17txZbdu2Nc+Td/DgQe3fv1+NGze26LuffPKJrly5ovr166tYsWIKDQ3V6tWrdf/+fQUEBCR7HteuXVNQUJAWL15sDg0dHBzUvHlzffTRR7p+/bp27dqlYsWKqWbNmubtYmNjdezYsXQ9hAMAkH6EUgCAXKlixYpau3at5s6dq1WrVunhw4cqWbKkxUTHKSlbtqwmTJigjz/+WBcvXpSnp6dmz55t8T/2gwYNUnR0tLZs2aJt27apcuXKWrx4sWbNmmV1zTVq1NCWLVsUFBRk/sJpZ2cnLy8v+fv7W3yxGzZsmNzc3LRixQpNmzZNrq6u6tatm95//32Lp69llDlz5mju3LmaNWuWHBwc1Lt37ySByJQpU+Tp6amNGzdq9+7dcnd319tvv62hQ4dadcyCBQtq+fLlWrx4sbZv365NmzbJ2dlZZcuW1bBhw6wavdCqVSv16dNH33//vTZv3izDMFINpUJCQjR37lyLZYmv69Wr99RQqlmzZnruuef0ww8/aPDgwUnW//Of/0x2u6+//loFChRQQECAKleurEGDBpnX1alTR2+88YaWLVumVq1aWXyRnjhxorZs2aLPPvtMsbGxatu2rQIDA1O9bbFLly66deuW1qxZo4MHD6pChQr65JNPtH37dosw41n5+/urbNmy+vLLL7VgwQJJj+YCatiwofz8/Mztunfvrvj4eAUFBenjjz+Wt7e3Fi5cmOT38DR///vf5eHhobCwMItb96RHcwQtWrRIM2bM0Jw5c+Ti4qIOHTqofv36GjBggNXnmBnXMi2fZe7u7vr222+1YMEC7dq1S6tWrVKRIkVUoUIFjRo1yqrj1qpVSydPntS6desUHh6uggULqnr16po6dWqShw9EREQk+f188cUXkh7d2vv4Z9eTXFxc1LRpUx06dEibNm1SfHy8XnjhBb3//vvq37+/xXxlDRs21NWrV7Vy5UrdvXtXhQsXVt26dfXOO++kOHJrxowZatKkSZJg/IMPPlBAQIA+/fRTlS1bVvPnz7cI2g4fPqzw8PAU5+kCAGQMk8FjJQAAMPPz89Pf/vY3LV68OKtLyRbmzZun+fPn6/Dhw5k+h0xutWDBAm3YsEE7d+5M0wTv1tiwYYPGjRundevWPfXBAgCebvDgwTKZTOYAFQCQOZhTCgAAIBP169dP9+/f1/fff5/VpQBIg/Pnz+vHH3/U8OHDs7oUAMj1uH0PAAAgEzk5Oenw4cNZXQaANCpfvrx+++23rC4DAPIERkoBAAAAAADA5phTCgAAAAAAADbHSCkAAAAAAADYHKEUAAAAAAAAbI5QCgAAAAAAADZHKAUAAAAAAACbI5QCAAAAAACAzRFKAQAAAAAAwOYIpQAAAAAAAGBzhFIAAAAAAACwOUIpAAAAAAAA2Nz/A4KNjPtAGKSmAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Notice how sentences on the same topic cluster together in the 2D space.\n", "The two principal components capture 76.64% of the total variance.\n"]}], "source": ["# Reduce embeddings to 2 dimensions using PCA\n", "pca = PCA(n_components=2)\n", "embeddings_2d = pca.fit_transform(embeddings)\n", "\n", "# Set up colors for topics\n", "topic_colors = {'AI/ML': 'red', 'Weather': 'blue', 'Python': 'green'}\n", "colors = [topic_colors[topic] for topic in topics]\n", "\n", "# Plot the 2D embeddings\n", "plt.figure(figsize=(12, 8))\n", "for i, (x, y) in enumerate(embeddings_2d):\n", "    plt.scatter(x, y, c=colors[i], s=100, alpha=0.7, edgecolors='black')\n", "    plt.annotate(f\"S{i+1}\", \n", "                xy=(x, y), \n", "                xytext=(5, 5), \n", "                textcoords='offset points',\n", "                fontsize=12,\n", "                weight='bold')\n", "\n", "# Add a legend\n", "for topic, color in topic_colors.items():\n", "    plt.scatter([], [], c=color, label=topic, s=100, alpha=0.7)\n", "plt.legend(loc='upper right')\n", "\n", "# Add title and labels\n", "plt.title('2D PCA Projection of Sentence Embeddings', fontsize=15)\n", "plt.xlabel(f'Principal Component 1 (Explained Variance: {pca.explained_variance_ratio_[0]:.2%})', fontsize=12)\n", "plt.ylabel(f'Principal Component 2 (Explained Variance: {pca.explained_variance_ratio_[1]:.2%})', fontsize=12)\n", "plt.grid(alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Notice how sentences on the same topic cluster together in the 2D space.\")\n", "print(f\"The two principal components capture {sum(pca.explained_variance_ratio_):.2%} of the total variance.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Testing with New Sentences\n", "\n", "Let's see how our model handles new sentences related to our original topics."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "New: \"Deep learning has revolutionized computer vision.\"\n", "Most similar to: \"AI and ML are fascinating fields of study.\"\n", "Similarity score: 0.4022\n", "Topic: AI/ML\n", "\n", "New: \"The forecast predicts rain for tomorrow.\"\n", "Most similar to: \"The weather is beautiful today.\"\n", "Similarity score: 0.4943\n", "Topic: Weather\n", "\n", "New: \"NumPy and Pandas are essential Python libraries.\"\n", "Most similar to: \"I enjoy coding in Python for data analysis.\"\n", "Similarity score: 0.6019\n", "Topic: Python\n"]}], "source": ["# Define new sentences\n", "new_sentences = [\n", "    \"Deep learning has revolutionized computer vision.\",  # AI/ML related\n", "    \"The forecast predicts rain for tomorrow.\",           # Weather related\n", "    \"NumPy and Pandas are essential Python libraries.\"    # Python related\n", "]\n", "\n", "# Create embeddings for the new sentences\n", "new_embeddings = model.encode(new_sentences)\n", "\n", "# Calculate similarity between new and original sentences\n", "similarity_to_original = cosine_similarity(new_embeddings, embeddings)\n", "\n", "# Find the most similar original sentence for each new sentence\n", "for i, new_sent in enumerate(new_sentences):\n", "    # Get index of most similar original sentence\n", "    most_similar_idx = np.argmax(similarity_to_original[i])\n", "    print(f\"\\nNew: \\\"{new_sent}\\\"\")\n", "    print(f\"Most similar to: \\\"{sentences[most_similar_idx]}\\\"\")\n", "    print(f\"Similarity score: {similarity_to_original[i][most_similar_idx]:.4f}\")\n", "    print(f\"Topic: {topics[most_similar_idx]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualizing Original and New Sentences Together"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Notice how new sentences (stars) appear close to their semantically related original sentences.\n"]}], "source": ["# Combine original and new embeddings\n", "all_embeddings = np.vstack([embeddings, new_embeddings])\n", "all_topics = topics + ['AI/ML', 'Weather', 'Python']\n", "\n", "# Project to 2D using PCA\n", "pca = PCA(n_components=2)\n", "all_embeddings_2d = pca.fit_transform(all_embeddings)\n", "\n", "# Create visualization\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Plot original sentences\n", "for i in range(len(sentences)):\n", "    x, y = all_embeddings_2d[i]\n", "    plt.scatter(x, y, c=topic_colors[all_topics[i]], s=100, alpha=0.7, edgecolors='black')\n", "    plt.annotate(f\"S{i+1}\", xy=(x, y), xytext=(5, 5), textcoords='offset points', fontsize=10)\n", "\n", "# Plot new sentences with star markers\n", "for i in range(len(sentences), len(sentences) + len(new_sentences)):\n", "    x, y = all_embeddings_2d[i]\n", "    plt.scatter(x, y, c=topic_colors[all_topics[i]], s=150, alpha=0.9, marker='*', edgecolors='black')\n", "    plt.annotate(f\"N{i-len(sentences)+1}\", xy=(x, y), xytext=(5, 5), textcoords='offset points', fontsize=10, weight='bold')\n", "\n", "# Add a legend\n", "for topic, color in topic_colors.items():\n", "    plt.scatter([], [], c=color, label=topic, s=100, alpha=0.7)\n", "plt.scatter([], [], c='gray', marker='o', s=100, label='Original', alpha=0.7)\n", "plt.scatter([], [], c='gray', marker='*', s=150, label='New', alpha=0.9)\n", "plt.legend(loc='lower right')\n", "\n", "plt.title('PCA Projection of Original and New Sentences', fontsize=15)\n", "plt.grid(alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Notice how new sentences (stars) appear close to their semantically related original sentences.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Real-World Applications of Embeddings\n", "\n", "Embeddings power many modern AI applications including:\n", "\n", "1. **Semantic Search**: Finding documents based on meaning rather than just keywords\n", "2. **Document Clustering**: Automatically grouping similar documents\n", "3. **Recommendation Systems**: Suggesting similar items based on semantic content\n", "4. **Question Answering**: Finding relevant information to answer queries\n", "5. **Retrieval Augmented Generation (RAG)**: Combining LLMs with knowledge bases using embeddings\n", "\n", "## Conclusion\n", "\n", "In this notebook, we've explored the fundamentals of text embeddings:\n", "- How embeddings represent text as numerical vectors capturing semantic meaning\n", "- Using cosine similarity to measure semantic relationships\n", "- Visualizing high-dimensional embeddings in 2D space\n", "- Testing embedding models with new sentences\n", "\n", "Embeddings serve as the bridge between human language and machine understanding, forming the foundation of many modern NLP systems."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}