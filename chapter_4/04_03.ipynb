{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comparing Embeddings Models\n", "\n", "In this video, we'll explore different embedding models available in the Sentence Transformers ecosystem and understand their trade-offs.\n", "\n", "Embedding models differ in many ways, including:\n", "1. Size and computational requirements\n", "2. Language support (monolingual vs. multilingual)\n", "3. Context length limitations\n", "4. Embedding dimensionality \n", "5. Performance on specific tasks\n", "\n", "Sentence Transformers provides access to a wide range of pre-trained models optimized for different use cases. Some popular models include:\n", "\n", "- all-MiniLM-L6-v2: A compact, efficient model that produces 384-dimensional embeddings\n", "- all-mpnet-base-v2: A more powerful model with 768-dimensional embeddings\n", "- paraphrase-multilingual-MiniLM-L12-v2: Supports 50+ languages\n", "- multi-qa-mpnet-base-dot-v1: Optimized for question-answering tasks\n", "\n", "When selecting an embedding model for your application, consider these factors:\n", "\n", "1. Accuracy requirements: More powerful models typically produce better embeddings but require more computation\n", "2. Inference speed: Smaller models are faster but may sacrifice some accuracy\n", "3. Resource constraints: Model size affects memory usage and deployment options\n", "4. Multilingual needs: Some models support multiple languages, while others are optimized for English\n", "5. Specific tasks: Models fine-tuned for particular tasks often perform better on those tasks\n", "\n", "Let's compare several embedding models to understand their trade-offs in a real-world production context."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 9ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import random\n", "import matplotlib.pyplot as plt\n", "from sentence_transformers import SentenceTransformer, util\n", "import time\n", "import pandas as pd\n", "\n", "import numpy as np\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set a nice visual style\n", "sns.set_theme(style=\"whitegrid\")\n", "plt.rcParams.update({'font.size': 11})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Different embeddings models\n", "models = [\n", "    'all-MiniLM-L6-v2',  # Small model (384d)\n", "    'all-MiniLM-L12-v2',  # Medium model (384d)\n", "    'all-mpnet-base-v2',  # Large model (768d)\n", "    'paraphrase-multilingual-MiniLM-L12-v2'  # Multilingual model (384d)\n", "]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Example sentences for benchmarking, organized by topic pairs\n", "sentence_pairs = [\n", "    # Technology pairs (semantically similar)\n", "    [\"Machine learning models require significant computational resources.\",\n", "     \"AI systems need a lot of computing power to train.\"],\n", "\n", "    # Programming pairs (semantically similar)\n", "    [\"What's the best algorithm for text classification?\",\n", "     \"How can I optimize my neural network training time?\"],\n", "\n", "    # Weather pairs (semantically similar)\n", "    [\"The weather forecast predicts rain tomorrow.\",\n", "     \"It's going to be wet outside tomorrow according to meteorologists.\"],\n", "\n", "    # Office pairs (semantically similar)\n", "    [\"I need to get a new computer for my office.\",\n", "     \"My workplace needs updated computing equipment.\"],\n", "\n", "    # Health pairs (semantically similar)\n", "    [\"Regular exercise improves cardiovascular health.\",\n", "     \"Working out frequently is good for your heart.\"],\n", "\n", "    # Food pairs (semantically similar)\n", "    [\"The restaurant serves authentic Italian pasta dishes.\",\n", "     \"You can get genuine Italian noodle recipes at that dining place.\"]\n", "]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Generate dissimilar pairs by mixing topics\n", "dissimilar_pairs = []\n", "for i in range(len(sentence_pairs)):\n", "    for j in range(len(sentence_pairs)):\n", "        if i != j:  # Different topics\n", "            dissimilar_pairs.append(\n", "                [sentence_pairs[i][0], sentence_pairs[j][0]])\n", "\n", "# Just use 6 dissimilar pairs to match our similar pairs count\n", "random.seed(42)  # For reproducibility\n", "dissimilar_pairs = random.sample(dissimilar_pairs, 6)\n", "\n", "# Create flat list of all sentences for encoding\n", "all_sentences = []\n", "for pair in sentence_pairs + dissimilar_pairs:\n", "    all_sentences.extend(pair)\n", "all_sentences = list(set(all_sentences))  # Remove duplicates"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define the evaluation function\n", "def evaluate_model(model_name, all_sentences, sentence_pairs, dissimilar_pairs):\n", "    start_time = time.time()\n", "\n", "    # Load model\n", "    model_load_time = time.time()\n", "    model = SentenceTransformer(model_name)\n", "    model_load_time = time.time() - model_load_time\n", "\n", "    # Encode all sentences at once (more efficient)\n", "    encoding_time = time.time()\n", "    embeddings_dict = {sentence: model.encode(\n", "        sentence) for sentence in all_sentences}\n", "    encoding_time = time.time() - encoding_time\n", "\n", "    # Calculate embedding dimensionality\n", "    dim = next(iter(embeddings_dict.values())).shape[0]\n", "\n", "    # Get model size (parameters)\n", "    model_size_mb = sum(p.numel() for p in model.parameters()\n", "                        ) * 4 / 1024 / 1024  # Approx size in MB\n", "\n", "    # Calculate similarities for similar pairs\n", "    similar_scores = []\n", "    for s1, s2 in sentence_pairs:\n", "        score = util.cos_sim(\n", "            embeddings_dict[s1].reshape(1, -1),\n", "            embeddings_dict[s2].reshape(1, -1)\n", "        ).item()\n", "        similar_scores.append(score)\n", "\n", "    # Calculate similarities for dissimilar pairs\n", "    dissimilar_scores = []\n", "    for s1, s2 in dissimilar_pairs:\n", "        score = util.cos_sim(\n", "            embeddings_dict[s1].reshape(1, -1),\n", "            embeddings_dict[s2].reshape(1, -1)\n", "        ).item()\n", "        dissimilar_scores.append(score)\n", "\n", "    # Calculate average scores and contrast\n", "    avg_similar = sum(similar_scores) / len(similar_scores)\n", "    avg_dissimilar = sum(dissimilar_scores) / len(dissimilar_scores)\n", "    contrast = avg_similar - avg_dissimilar\n", "\n", "    total_time = time.time() - start_time\n", "\n", "    # Return results\n", "    return {\n", "        'Model': model_name,\n", "        'Dimensions': dim,\n", "        'Size (MB)': model_size_mb,\n", "        'Load Time (s)': model_load_time,\n", "        'Encoding Time (s)': encoding_time,\n", "        'Total Time (s)': total_time,\n", "        'Avg Similar Score': avg_similar,\n", "        'Avg Different Score': avg_dissimilar,\n", "        'Contrast': contrast,\n", "        'Similar Scores': similar_scores,\n", "        'Dissimilar Scores': dissimilar_scores\n", "    }"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluating all-MiniLM-L6-v2...\n", "Completed evaluation of all-MiniLM-L6-v2\n", "Evaluating all-MiniLM-L12-v2...\n", "Completed evaluation of all-MiniLM-L12-v2\n", "Evaluating all-mpnet-base-v2...\n", "Completed evaluation of all-mpnet-base-v2\n", "Evaluating paraphrase-multilingual-MiniLM-L12-v2...\n", "Completed evaluation of paraphrase-multilingual-MiniLM-L12-v2\n"]}], "source": ["# Run the evaluation for all models\n", "results = []\n", "for model_name in models:\n", "    print(f\"Evaluating {model_name}...\")\n", "    model_results = evaluate_model(model_name, all_sentences, sentence_pairs, dissimilar_pairs)\n", "    results.append(model_results)\n", "    print(f\"Completed evaluation of {model_name}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Model Comparison Summary ===\n", "                                Model  Dimensions  Size (MB)  Encoding Time (s)  Avg Similar Score  Avg Different Score  Contrast\n", "                     all-MiniLM-L6-v2         384    86.6440             0.1288             0.5765               0.0352    0.5413\n", "                    all-MiniLM-L12-v2         384   127.2583             0.2226             0.6130               0.0898    0.5231\n", "                    all-mpnet-base-v2         768   417.6577             0.5943             0.6623               0.0336    0.6286\n", "paraphrase-multilingual-MiniLM-L12-v2         384   448.8135             0.2749             0.6352               0.0844    0.5507\n"]}], "source": ["# Convert to DataFrame for easier analysis\n", "df = pd.DataFrame(results)\n", "\n", "# Create a more readable version of the results for display\n", "display_df = df[['Model', 'Dimensions', '<PERSON><PERSON> (MB)', 'Encoding Time (s)',\n", "                'Avg Similar Score', 'Avg Different Score', 'Contrast']]\n", "print(\"\\n=== Model Comparison Summary ===\")\n", "print(display_df.to_string(index=False, float_format=lambda x: f\"{x:.4f}\"))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Detailed Pair Analysis ===\n", "\n", "Model: all-MiniLM-L6-v2\n", "Similar pairs:\n", "  - Pair 1: Machine learning models require significant computational resources. | AI systems need a lot of computing power to train. | Score: 0.5377\n", "  - Pair 2: What's the best algorithm for text classification? | How can I optimize my neural network training time? | Score: 0.2465\n", "  - Pair 3: The weather forecast predicts rain tomorrow. | It's going to be wet outside tomorrow according to meteorologists. | Score: 0.7471\n", "  - Pair 4: I need to get a new computer for my office. | My workplace needs updated computing equipment. | Score: 0.5624\n", "  - Pair 5: Regular exercise improves cardiovascular health. | Working out frequently is good for your heart. | Score: 0.6454\n", "  - Pair 6: The restaurant serves authentic Italian pasta dishes. | You can get genuine Italian noodle recipes at that dining place. | Score: 0.7201\n", "Dissimilar pairs:\n", "  - Pair 1: Regular exercise improves cardiovascular health. | Machine learning models require significant computational resources. | Score: -0.0237\n", "  - Pair 2: Machine learning models require significant computational resources. | Regular exercise improves cardiovascular health. | Score: -0.0237\n", "  - Pair 3: Machine learning models require significant computational resources. | What's the best algorithm for text classification? | Score: 0.2028\n", "  - Pair 4: Regular exercise improves cardiovascular health. | I need to get a new computer for my office. | Score: -0.0013\n", "  - Pair 5: What's the best algorithm for text classification? | Regular exercise improves cardiovascular health. | Score: -0.0026\n", "  - Pair 6: What's the best algorithm for text classification? | I need to get a new computer for my office. | Score: 0.0599\n", "\n", "Model: all-MiniLM-L12-v2\n", "Similar pairs:\n", "  - Pair 1: Machine learning models require significant computational resources. | AI systems need a lot of computing power to train. | Score: 0.6005\n", "  - Pair 2: What's the best algorithm for text classification? | How can I optimize my neural network training time? | Score: 0.2577\n", "  - Pair 3: The weather forecast predicts rain tomorrow. | It's going to be wet outside tomorrow according to meteorologists. | Score: 0.7617\n", "  - Pair 4: I need to get a new computer for my office. | My workplace needs updated computing equipment. | Score: 0.6439\n", "  - Pair 5: Regular exercise improves cardiovascular health. | Working out frequently is good for your heart. | Score: 0.7200\n", "  - Pair 6: The restaurant serves authentic Italian pasta dishes. | You can get genuine Italian noodle recipes at that dining place. | Score: 0.6939\n", "Dissimilar pairs:\n", "  - Pair 1: Regular exercise improves cardiovascular health. | Machine learning models require significant computational resources. | Score: 0.0205\n", "  - Pair 2: Machine learning models require significant computational resources. | Regular exercise improves cardiovascular health. | Score: 0.0205\n", "  - Pair 3: Machine learning models require significant computational resources. | What's the best algorithm for text classification? | Score: 0.2777\n", "  - Pair 4: Regular exercise improves cardiovascular health. | I need to get a new computer for my office. | Score: 0.0066\n", "  - Pair 5: What's the best algorithm for text classification? | Regular exercise improves cardiovascular health. | Score: 0.0508\n", "  - Pair 6: What's the best algorithm for text classification? | I need to get a new computer for my office. | Score: 0.1630\n", "\n", "Model: all-mpnet-base-v2\n", "Similar pairs:\n", "  - Pair 1: Machine learning models require significant computational resources. | AI systems need a lot of computing power to train. | Score: 0.6598\n", "  - Pair 2: What's the best algorithm for text classification? | How can I optimize my neural network training time? | Score: 0.2119\n", "  - Pair 3: The weather forecast predicts rain tomorrow. | It's going to be wet outside tomorrow according to meteorologists. | Score: 0.8649\n", "  - Pair 4: I need to get a new computer for my office. | My workplace needs updated computing equipment. | Score: 0.7309\n", "  - Pair 5: Regular exercise improves cardiovascular health. | Working out frequently is good for your heart. | Score: 0.7791\n", "  - Pair 6: The restaurant serves authentic Italian pasta dishes. | You can get genuine Italian noodle recipes at that dining place. | Score: 0.7270\n", "Dissimilar pairs:\n", "  - Pair 1: Regular exercise improves cardiovascular health. | Machine learning models require significant computational resources. | Score: -0.0106\n", "  - Pair 2: Machine learning models require significant computational resources. | Regular exercise improves cardiovascular health. | Score: -0.0106\n", "  - Pair 3: Machine learning models require significant computational resources. | What's the best algorithm for text classification? | Score: 0.1648\n", "  - Pair 4: Regular exercise improves cardiovascular health. | I need to get a new computer for my office. | Score: -0.0554\n", "  - Pair 5: What's the best algorithm for text classification? | Regular exercise improves cardiovascular health. | Score: 0.0117\n", "  - Pair 6: What's the best algorithm for text classification? | I need to get a new computer for my office. | Score: 0.1020\n", "\n", "Model: paraphrase-multilingual-MiniLM-L12-v2\n", "Similar pairs:\n", "  - Pair 1: Machine learning models require significant computational resources. | AI systems need a lot of computing power to train. | Score: 0.5139\n", "  - Pair 2: What's the best algorithm for text classification? | How can I optimize my neural network training time? | Score: 0.2961\n", "  - Pair 3: The weather forecast predicts rain tomorrow. | It's going to be wet outside tomorrow according to meteorologists. | Score: 0.7529\n", "  - Pair 4: I need to get a new computer for my office. | My workplace needs updated computing equipment. | Score: 0.6045\n", "  - Pair 5: Regular exercise improves cardiovascular health. | Working out frequently is good for your heart. | Score: 0.7424\n", "  - Pair 6: The restaurant serves authentic Italian pasta dishes. | You can get genuine Italian noodle recipes at that dining place. | Score: 0.9011\n", "Dissimilar pairs:\n", "  - Pair 1: Regular exercise improves cardiovascular health. | Machine learning models require significant computational resources. | Score: 0.0199\n", "  - Pair 2: Machine learning models require significant computational resources. | Regular exercise improves cardiovascular health. | Score: 0.0199\n", "  - Pair 3: Machine learning models require significant computational resources. | What's the best algorithm for text classification? | Score: 0.3063\n", "  - Pair 4: Regular exercise improves cardiovascular health. | I need to get a new computer for my office. | Score: 0.0238\n", "  - Pair 5: What's the best algorithm for text classification? | Regular exercise improves cardiovascular health. | Score: 0.0370\n", "  - Pair 6: What's the best algorithm for text classification? | I need to get a new computer for my office. | Score: 0.0996\n"]}], "source": ["# Display detailed pair analysis\n", "print(\"\\n=== Detailed Pair Analysis ===\")\n", "for idx, model_data in enumerate(results):\n", "    print(f\"\\nModel: {model_data['Model']}\")\n", "    print(\"Similar pairs:\")\n", "    for i, score in enumerate(model_data['Similar Scores']):\n", "        print(\n", "            f\"  - Pair {i+1}: {sentence_pairs[i][0]} | {sentence_pairs[i][1]} | Score: {score:.4f}\")\n", "    print(\"Dissimilar pairs:\")\n", "    for i, score in enumerate(model_data['Dissimilar Scores']):\n", "        print(\n", "            f\"  - Pair {i+1}: {dissimilar_pairs[i][0]} | {dissimilar_pairs[i][1]} | Score: {score:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results - Boxplot comparison\n", "plt.figure(figsize=(12, 6))\n", "# Prepare data for boxplot\n", "boxplot_data = []\n", "model_names = []\n", "\n", "for model_data in results:\n", "    boxplot_data.append(model_data['Similar Scores'])\n", "    boxplot_data.append(model_data['Dissimilar Scores'])\n", "    model_names.append(f\"{model_data['Model']} Similar\")\n", "    model_names.append(f\"{model_data['Model']} Different\")\n", "\n", "# Create a color map for the boxes\n", "colors = ['lightgreen', 'lightcoral'] * len(models)\n", "\n", "# Create boxplot\n", "bp = plt.boxplot(boxplot_data, patch_artist=True, vert=False)\n", "plt.yticks(range(1, len(model_names) + 1), model_names)\n", "plt.xlabel('Cosine Similarity')\n", "plt.title('Distribution of Similarity Scores by Model')\n", "\n", "# Color the boxes\n", "for patch, color in zip(bp['boxes'], colors):\n", "    patch.set_facecolor(color)\n", "    \n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results - Efficiency metrics\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(df['<PERSON><PERSON> (MB)'], df['Encoding Time (s)'],\n", "            s=df['Dimensions']/5, alpha=0.7)\n", "for i, model in enumerate(df['Model']):\n", "    plt.annotate(model, (df['<PERSON><PERSON> (MB)'].iloc[i], df['Encoding Time (s)'].iloc[i]),\n", "                 xytext=(5, 5), textcoords='offset points')\n", "plt.xlabel('Model Size (MB)')\n", "plt.ylabel('Encoding Time (s)')\n", "plt.title('Model Efficiency (bubble size = dimensions)')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results - Performance vs Resources\n", "plt.figure(figsize=(10, 6))\n", "sc = plt.scatter(df['Total Time (s)'], df['Contrast'], c=df['Size (MB)'],\n", "                 s=200, cmap='viridis', alpha=0.7)\n", "plt.colorbar(sc, label='Model Size (MB)')\n", "\n", "for i, model in enumerate(df['Model']):\n", "    plt.annotate(model, (df['Total Time (s)'].iloc[i], df['Contrast'].iloc[i]),\n", "                 xytext=(5, 5), textcoords='offset points')\n", "plt.xlabel('Total Processing Time (s)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> (Similar - Different)')\n", "plt.title('Performance vs. Resource Requirements')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Takeaways\n", "\n", "- <PERSON><PERSON><PERSON> is king: The contrast score is often the most important metric. A model with higher contrast will be better at distinguishing relevant from irrelevant information.\n", "- Consider the trade-offs: Larger models typically have better semantic understanding but require more resources and run slower. Ask yourself what's more important for your application: accuracy or speed?\n", "- Test with domain-specific data: While our comparison uses general examples, you'll get the best results by testing with examples from your specific domain.\n", "- Think about your constraints: If you're deploying on mobile or edge devices, model size matters more. For server applications, you might prioritize accuracy.\n", "- Consider multilingual needs: If your application needs to work across languages, a multilingual model might be essential even if it's not the top performer in English.\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}