{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Efficient Embeddings Generation\n", "\n", "## Overview\n", "\n", "This notebook demonstrates techniques for efficiently generating embeddings at scale through:\n", "\n", "1. **Batching**: Processing multiple inputs together rather than one at a time\n", "2. **Caching**: Storing previously generated embeddings to avoid regeneration\n", "\n", "These optimization strategies can significantly improve performance in production systems that rely on embeddings for natural language processing tasks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install the required packages:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 8ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages (only run this once)\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's import the necessary libraries and set up our environment:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1000 example sentences\n"]}], "source": ["from sentence_transformers import SentenceTransformer\n", "import time\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import tqdm\n", "import hashlib\n", "from functools import lru_cache\n", "import pandas as pd\n", "\n", "# Load model - using a lightweight model suitable for demonstration\n", "model_name = 'all-MiniLM-L6-v2'\n", "model = SentenceTransformer(model_name)\n", "\n", "# Generate example sentences for benchmarking\n", "sentences = [\n", "    f\"This is a sample sentence for benchmarking embeddings generation {i}.\"\n", "    for i in range(1000)\n", "]\n", "\n", "print(f\"Created {len(sentences)} example sentences\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON> of <PERSON><PERSON> Size on Performance\n", "\n", "Batching is a crucial optimization technique. Instead of processing each input one by one, we process multiple inputs together. This maximizes computational efficiency, especially with GPU acceleration. Let's analyze how different batch sizes affect:\n", "\n", "- **Throughput**: How many embeddings we can generate per second\n", "- **Latency**: How much time each batch takes to process"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Measuring impact of batch size on throughput and latency...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "70d07993482f4424a3a0e5eb7d6544b1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/8 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["   Batch Size  Total Time (s)  Throughput (samples/s)  Avg Batch Latency (s)\n", "0           1       11.095434               90.127164               0.011095\n", "1           4        4.771030              209.598349               0.019084\n", "2           8        3.577166              279.550919               0.028617\n", "3          16        3.012807              331.916342               0.048205\n", "4          32        3.004729              332.808739               0.096151\n", "5          64        2.879524              347.279592               0.184290\n", "6         128        2.906728              344.029463               0.372061\n", "7         256        2.878667              347.382965               0.736939\n"]}], "source": ["# Measure impact of batch size on throughput and latency\n", "batch_sizes = [1, 4, 8, 16, 32, 64, 128, 256]\n", "results = []\n", "\n", "print(\"Measuring impact of batch size on throughput and latency...\")\n", "\n", "for batch_size in tqdm(batch_sizes):\n", "    start_time = time.time()\n", "\n", "    # Process data in batches\n", "    embeddings = []\n", "    for i in range(0, len(sentences), batch_size):\n", "        batch = sentences[i:i+batch_size]\n", "        batch_embeddings = model.encode(batch)\n", "        embeddings.extend(batch_embeddings)\n", "\n", "    # Calculate metrics\n", "    total_time = time.time() - start_time\n", "    throughput = len(sentences) / total_time\n", "    avg_latency = total_time / (len(sentences) / batch_size)\n", "\n", "    results.append({\n", "        'Batch Size': batch_size,\n", "        'Total Time (s)': total_time,\n", "        'Throughput (samples/s)': throughput,\n", "        'Avg Batch Latency (s)': avg_latency\n", "    })\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(results)\n", "print(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's visualize the relationship between batch size and performance:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results\n", "plt.figure(figsize=(15, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(df['<PERSON><PERSON>'], df['Throughput (samples/s)'], 'o-', linewidth=2)\n", "plt.xlabel('<PERSON><PERSON> Size')\n", "plt.ylabel('Throughput (samples/s)')\n", "plt.title('<PERSON><PERSON> vs <PERSON><PERSON>')\n", "plt.grid(True)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(df['<PERSON><PERSON>'], df['Avg Batch Latency (s)'], 'o-', linewidth=2)\n", "plt.xlabel('<PERSON><PERSON> Size')\n", "plt.ylabel('Average Batch Latency (s)')\n", "plt.title('<PERSON><PERSON> vs Latency')\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Observations:\n", "\n", "1. **Throughput Increases with Batch Size**: As we increase the batch size, we can generate more embeddings per second up to a certain point.\n", "\n", "2. **Diminishing Returns**: Beyond a certain batch size (usually around 32-64), the throughput gains tend to plateau or even decrease due to memory limitations.\n", "\n", "3. **Latency Trade-off**: While larger batches improve throughput, they increase the average time to process each batch, which may be important for real-time applications.\n", "\n", "4. **Batch Size Selection**: The optimal batch size depends on your specific requirements:\n", "   - For real-time applications: Smaller batches (lower latency)\n", "   - For bulk processing jobs: Larger batches (higher throughput)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing Embedding Caches\n", "\n", "In many real-world applications, we often encounter the same text multiple times. Caching the embeddings can avoid redundant computation and significantly improve performance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Dictionary-Based Cache"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class SimpleEmbeddingCache:\n", "    def __init__(self, model):\n", "        self.model = model\n", "        self.cache = {}  # Dictionary that maps text to embeddings\n", "        self.hits = 0    # Counter for cache hits\n", "        self.misses = 0  # Counter for cache misses\n", "\n", "    def _get_hash(self, text):\n", "        \"\"\"Generate a stable hash for a text string\"\"\"\n", "        return hashlib.md5(text.encode('utf-8')).hexdigest()\n", "\n", "    def encode(self, texts, batch_size=32):\n", "        \"\"\"Encode texts using cache when available\"\"\"\n", "        results = []\n", "        texts_to_encode = []\n", "        text_indices = []\n", "\n", "        # Check cache for each text\n", "        for i, text in enumerate(texts):\n", "            text_hash = self._get_hash(text)\n", "            if text_hash in self.cache:\n", "                results.append((i, self.cache[text_hash]))\n", "                self.hits += 1\n", "            else:\n", "                texts_to_encode.append(text)\n", "                text_indices.append(i)\n", "                self.misses += 1\n", "\n", "        # Generate embeddings for cache misses\n", "        if texts_to_encode:\n", "            # Process in batches\n", "            new_embeddings = []\n", "            for i in range(0, len(texts_to_encode), batch_size):\n", "                batch = texts_to_encode[i:i+batch_size]\n", "                batch_embeddings = model.encode(batch)\n", "                new_embeddings.extend(batch_embeddings)\n", "\n", "            # Update cache with new embeddings\n", "            for i, text in enumerate(texts_to_encode):\n", "                text_hash = self._get_hash(text)\n", "                self.cache[text_hash] = new_embeddings[i]\n", "                results.append((text_indices[i], new_embeddings[i]))\n", "\n", "        # Sort by original index and extract embeddings\n", "        results.sort(key=lambda x: x[0])\n", "        return np.array([emb for _, emb in results])\n", "\n", "    def get_stats(self):\n", "        \"\"\"Return cache performance statistics\"\"\"\n", "        total = self.hits + self.misses\n", "        hit_rate = self.hits / total if total > 0 else 0\n", "        return {\n", "            \"hits\": self.hits,\n", "            \"misses\": self.misses,\n", "            \"total\": total,\n", "            \"hit_rate\": hit_rate\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LRU Cache-Based Implementation\n", "\n", "Python's built-in `functools.lru_cache` provides a convenient way to implement a Least Recently Used (LRU) cache, which automatically evicts the least recently used items when the cache reaches its size limit."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["@lru_cache(maxsize=1024)\n", "def hash_text(text):\n", "    return hashlib.md5(text.encode('utf-8')).hexdigest()\n", "\n", "class LRUEmbeddingCache:\n", "    def __init__(self, model, maxsize=1024):\n", "        self.model = model\n", "        self.encode_single = lru_cache(maxsize=maxsize)(self._encode_single)\n", "        self.hits = 0\n", "        self.misses = 0\n", "        self.hash_to_text = {}\n", "\n", "    def _encode_single(self, text_hash):\n", "        \"\"\"Generate embedding for a single text (identified by hash)\"\"\"\n", "        self.misses += 1\n", "        # Convert hash back to the original text\n", "        text = self.hash_to_text[text_hash]\n", "        return self.model.encode([text])[0]  # Note: we're encoding a single text\n", "\n", "    def encode(self, texts, batch_size=32):\n", "        \"\"\"Encode texts using LRU cache when available\"\"\"\n", "        self.hash_to_text = {}\n", "        results = []\n", "\n", "        for text in texts:\n", "            # Use the hash as the cache key\n", "            text_hash = hash_text(text)\n", "            self.hash_to_text[text_hash] = text\n", "\n", "            # Check if it's in the cache\n", "            cache_info_before = self.encode_single.cache_info()\n", "            embedding = self.encode_single(text_hash)\n", "            cache_info_after = self.encode_single.cache_info()\n", "            \n", "            # Update hit/miss counters\n", "            if cache_info_after.hits > cache_info_before.hits:\n", "                self.hits += 1\n", "                \n", "            results.append(embedding)\n", "\n", "        return np.array(results)\n", "\n", "    def get_stats(self):\n", "        \"\"\"Return cache performance statistics\"\"\"\n", "        total = self.hits + self.misses\n", "        hit_rate = self.hits / total if total > 0 else 0\n", "        return {\n", "            \"hits\": self.hits,\n", "            \"misses\": self.misses,\n", "            \"total\": total,\n", "            \"hit_rate\": hit_rate,\n", "            \"cache_info\": self.encode_single.cache_info()\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Cache Performance\n", "\n", "Let's compare the performance with and without caching:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Demonstrating caching benefits:\n", "Test data size: 500 sentences\n", "\n", "1. Without cache:\n", "Processing time: 1.4682s\n", "\n", "2. First run with cache:\n", "Processing time: 1.4120s\n", "Cache stats: 0 hits, 500 misses (0.0% hit rate)\n", "\n", "3. Second run with cache (should be all hits):\n", "Processing time: 0.0007s\n", "Cache stats: 500 hits, 0 misses (100.0% hit rate)\n", "\n", "Performance summary:\n", "- Without cache: 1.4682s\n", "- First run with cache: 1.4120s\n", "- Second run with cache: 0.0007s\n", "- Speed improvement: 2018.3x faster with cache\n"]}], "source": ["# Create a test dataset with some repetition to demonstrate cache benefits\n", "print(\"\\nDemonstrating caching benefits:\")\n", "test_data = []\n", "for i in range(500):\n", "    # Every 5th sentence is repeated from the first 100 sentences\n", "    test_data.append(sentences[i % 100] if i % 5 == 0 else sentences[i])\n", "print(f\"Test data size: {len(test_data)} sentences\")\n", "\n", "# Benchmark 1: Without cache\n", "print(\"\\n1. Without cache:\")\n", "start = time.time()\n", "embeddings_no_cache = model.encode(test_data, batch_size=32)\n", "no_cache_time = time.time() - start\n", "print(f\"Processing time: {no_cache_time:.4f}s\")\n", "\n", "# Benchmark 2: First run with cache (should be all misses)\n", "print(\"\\n2. First run with cache:\")\n", "cache = SimpleEmbeddingCache(model)\n", "start = time.time()\n", "embeddings_with_cache = cache.encode(test_data, batch_size=32)\n", "first_run_time = time.time() - start\n", "print(f\"Processing time: {first_run_time:.4f}s\")\n", "print(\n", "    f\"Cache stats: {cache.hits} hits, {cache.misses} misses ({cache.hits/len(test_data)*100:.1f}% hit rate)\")\n", "\n", "# Benchmark 3: Second run with fresh cache object but reusing the populated cache dictionary\n", "print(\"\\n3. Second run with cache (should be all hits):\")\n", "# Create a new cache instance but reuse the cache dictionary from the first run\n", "second_cache = SimpleEmbeddingCache(model)\n", "second_cache.cache = cache.cache  # Copy the populated cache from first run\n", "start = time.time()\n", "embeddings_with_cache = second_cache.encode(test_data, batch_size=32)\n", "second_run_time = time.time() - start\n", "print(f\"Processing time: {second_run_time:.4f}s\")\n", "print(f\"Cache stats: {second_cache.hits} hits, {second_cache.misses} misses ({second_cache.hits/len(test_data)*100:.1f}% hit rate)\")\n", "\n", "# Performance summary\n", "print(\"\\nPerformance summary:\")\n", "print(f\"- Without cache: {no_cache_time:.4f}s\")\n", "print(f\"- First run with cache: {first_run_time:.4f}s\")\n", "print(f\"- Second run with cache: {second_run_time:.4f}s\")\n", "print(\n", "    f\"- Speed improvement: {no_cache_time/second_run_time:.1f}x faster with cache\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "Efficient embedding generation is critical for building performant AI applications. \n", "\n", "By implementing batching and caching strategies, you can significantly improve throughput and reduce latency, especially in production environments with repetitive content or high-volume processing needs.\n", "\n", "The specific optimization strategy depends on your application's requirements:\n", "- **Latency-sensitive applications**: Focus on smaller batch sizes and efficient caching\n", "- **High-throughput batch processing**: Use larger batch sizes and persistent caches\n", "- **Mixed workloads**: Implement adaptive strategies with priority queues\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}