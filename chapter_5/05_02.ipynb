{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ChromaDB Semantic Search Tutorial\n", "\n", "This notebook demonstrates using ChromaDB for semantic search. ChromaDB is a vector database that makes it easy to build semantic search systems."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Initialization"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 44ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import chromadb\n", "from chromadb.utils import embedding_functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing Chroma client...\n"]}], "source": ["# Initialize an in-memory Chroma client\n", "print(\"Initializing Chroma client...\")\n", "client = chromadb.Client()\n", "\n", "# Create embedding function using SentenceTransformer\n", "embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(\n", "    model_name=\"all-MiniLM-L6-v2\"  # Lightweight, effective model\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Collections\n", "\n", "In ChromaDB, documents are organized into collections."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create a new collection for documents\n", "collection = client.create_collection(\n", "    name=\"documents\",\n", "    embedding_function=embedding_function\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper Function for Results Display"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Helper function to display search results in a readable format\n", "def display_results(results):\n", "    \"\"\"Display ChromaDB search results\"\"\"\n", "    print(\"\\nResults:\")\n", "    for i, (doc, doc_id, metadata, distance) in enumerate(zip(\n", "        results['documents'][0],\n", "        results['ids'][0],\n", "        results['metadatas'][0] if results['metadatas'] else [\n", "            None] * len(results['ids'][0]),\n", "        results['distances'][0]\n", "    )):\n", "        print(f\"{i+1}. Document: {doc}\")\n", "        print(f\"   ID: {doc_id}\")\n", "        if metadata:\n", "            print(f\"   Metadata: {metadata}\")\n", "        print(f\"   Distance: {distance:.4f}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Vector Operations\n", "\n", "Adding documents and performing simple semantic search."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== BASIC VECTOR OPERATIONS ===\n", "Adding documents to collection...\n", "Collection now contains 8 documents\n", "\n", "Performing similarity search for: 'AI and technology trends'\n", "\n", "Results:\n", "1. Document: Artificial intelligence is transforming the technology landscape\n", "   ID: doc4\n", "   Distance: 0.6473\n", "\n", "2. Document: Machine learning algorithms find patterns in data\n", "   ID: doc8\n", "   Distance: 1.3642\n", "\n", "3. Document: Deep learning models require substantial computational resources\n", "   ID: doc6\n", "   Distance: 1.4002\n", "\n"]}], "source": ["# Basic Vector Operations\n", "print(\"\\n=== BASIC VECTOR OPERATIONS ===\")\n", "\n", "# Example documents covering various topics\n", "documents = [\n", "    \"The quick brown fox jumps over the lazy dog\",\n", "    \"A man is walking his dog in the park\",\n", "    \"The weather is sunny and warm today\",\n", "    \"Artificial intelligence is transforming the technology landscape\",\n", "    \"Vector databases are essential for semantic search applications\",\n", "    \"Deep learning models require substantial computational resources\",\n", "    \"The city skyline looks beautiful at sunset\",\n", "    \"Machine learning algorithms find patterns in data\"\n", "]\n", "ids = [\"doc1\", \"doc2\", \"doc3\", \"doc4\", \"doc5\", \"doc6\", \"doc7\", \"doc8\"]\n", "\n", "# Add documents to collection\n", "print(\"Adding documents to collection...\")\n", "collection.add(\n", "    documents=documents,\n", "    ids=ids\n", ")\n", "\n", "# Get collection count\n", "count = collection.count()\n", "print(f\"Collection now contains {count} documents\")\n", "\n", "# Perform a semantic search\n", "query_text = \"AI and technology trends\"\n", "print(f\"\\nPerforming similarity search for: '{query_text}'\")\n", "\n", "results = collection.query(\n", "    query_texts=[query_text],\n", "    n_results=3  # Return top 3 most similar documents\n", ")\n", "\n", "# Display results\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with Metada<PERSON> and Filtering\n", "\n", "ChromaDB allows attaching metadata to documents and filtering searches based on this metadata."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== METADATA AND FILTERING ===\n", "Adding documents with metadata...\n", "Filtered Docs Collection now contains 8 documents.\n"]}], "source": ["print(\"\\n=== METADATA AND FILTERING ===\")\n", "\n", "# Create a new collection for filtered documents\n", "filtered_docs_collection = client.create_collection(\n", "    name=\"filtered_documents\",\n", "    embedding_function=embedding_function\n", ")\n", "\n", "# Metadata for each document\n", "metadatas = [\n", "    {\"category\": \"animal\", \"length\": \"short\", \"year\": 2021},\n", "    {\"category\": \"lifestyle\", \"length\": \"short\", \"year\": 2022},\n", "    {\"category\": \"weather\", \"length\": \"short\", \"year\": 2023},\n", "    {\"category\": \"technology\", \"length\": \"medium\", \"year\": 2023},\n", "    {\"category\": \"technology\", \"length\": \"medium\", \"year\": 2024},\n", "    {\"category\": \"technology\", \"length\": \"long\", \"year\": 2024},\n", "    {\"category\": \"travel\", \"length\": \"short\", \"year\": 2023},\n", "    {\"category\": \"technology\", \"length\": \"medium\", \"year\": 2024}\n", "]\n", "\n", "# Add documents with metadata\n", "print(\"Adding documents with metadata...\")\n", "filtered_docs_collection.add(\n", "    documents=documents,\n", "    ids=ids,\n", "    metadatas=metadatas\n", ")\n", "\n", "# Get collection count\n", "count = filtered_docs_collection.count()\n", "print(f\"Filtered Docs Collection now contains {count} documents.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Metadata Filtering"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Filtering by category 'technology':\n", "\n", "Results:\n", "1. Document: Artificial intelligence is transforming the technology landscape\n", "   ID: doc4\n", "   Metadata: {'category': 'technology', 'length': 'medium', 'year': 2023}\n", "   Distance: 0.8661\n", "\n", "2. Document: Machine learning algorithms find patterns in data\n", "   ID: doc8\n", "   Metadata: {'category': 'technology', 'length': 'medium', 'year': 2024}\n", "   Distance: 1.3540\n", "\n", "3. Document: Deep learning models require substantial computational resources\n", "   ID: doc6\n", "   Metadata: {'category': 'technology', 'length': 'long', 'year': 2024}\n", "   Distance: 1.3605\n", "\n"]}], "source": ["# Simple metadata filtering - find technology documents about AI\n", "print(\"\\nFiltering by category 'technology':\")\n", "results = filtered_docs_collection.query(\n", "    query_texts=[\"AI advancements\"],\n", "    n_results=3,\n", "    where={\"category\": \"technology\"}  # Only search technology documents\n", ")\n", "\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complex Metadata Filtering\n", "\n", "Using logical operators for more advanced filtering."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Complex filtering (technology documents from 2024):\n", "\n", "Results:\n", "1. Document: Machine learning algorithms find patterns in data\n", "   ID: doc8\n", "   Metadata: {'category': 'technology', 'length': 'medium', 'year': 2024}\n", "   Distance: 1.3540\n", "\n", "2. Document: Deep learning models require substantial computational resources\n", "   ID: doc6\n", "   Metadata: {'category': 'technology', 'length': 'long', 'year': 2024}\n", "   Distance: 1.3605\n", "\n", "3. Document: Vector databases are essential for semantic search applications\n", "   ID: doc5\n", "   Metadata: {'category': 'technology', 'length': 'medium', 'year': 2024}\n", "   Distance: 1.5393\n", "\n"]}], "source": ["# Complex filtering - technology documents from 2024\n", "print(\"\\nComplex filtering (technology documents from 2024):\")\n", "results = filtered_docs_collection.query(\n", "    query_texts=[\"AI advancements\"],\n", "    n_results=3,\n", "    where={\"$and\": [\n", "        {\"category\": {\"$eq\": \"technology\"}},  # Category must be technology\n", "        {\"year\": {\"$eq\": 2024}}               # Year must be 2024\n", "    ]}\n", ")\n", "\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Content-Based Filtering"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Filtering documents containing 'Artificial intelligence ':\n", "\n", "Results:\n", "1. Document: Artificial intelligence is transforming the technology landscape\n", "   ID: doc4\n", "   Metadata: {'category': 'technology', 'length': 'medium', 'year': 2023}\n", "   Distance: 0.8661\n", "\n"]}], "source": ["# Using where_document to filter by document content\n", "print(\"\\nFiltering documents containing 'Artificial intelligence ':\")\n", "results = filtered_docs_collection.query(\n", "    query_texts=[\"AI advancements\"],\n", "    n_results=3,\n", "    where_document={\"$contains\": \"Artificial intelligence\"}  # Content filter\n", ")\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Management\n", "\n", "ChromaDB provides methods for updating and deleting documents."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Getting document by ID: doc1\n", "Original document: The quick brown fox jumps over the lazy dog\n", "\n", "Updating document...\n", "Updated document: The quick silver fox leaps over the sleepy hound\n", "\n", "Deleting document doc2...\n", "Collection now has 7 documents\n"]}], "source": ["# Get document by ID\n", "print(f\"Getting document by ID: doc1\")\n", "result = collection.get(ids=[\"doc1\"])\n", "print(f\"Original document: {result['documents'][0]}\")\n", "\n", "# Update document\n", "print(\"\\nUpdating document...\")\n", "collection.update(\n", "    ids=[\"doc1\"],\n", "    documents=[\"The quick silver fox leaps over the sleepy hound\"]\n", ")\n", "\n", "# Verify update\n", "result = collection.get(ids=[\"doc1\"])\n", "print(f\"Updated document: {result['documents'][0]}\")\n", "\n", "# Delete document\n", "print(\"\\nDeleting document doc2...\")\n", "collection.delete(ids=[\"doc2\"])\n", "\n", "# Verify deletion\n", "count = collection.count()\n", "print(f\"Collection now has {count} documents\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}