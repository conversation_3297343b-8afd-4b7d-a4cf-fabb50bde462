{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Persistence and Performance\n", "\n", "## Why Data Persistence Matters\n", "\n", "- **Business Continuity**: Ensures your data survives system restarts\n", "- **Cost Effectiveness**: Avoid recomputing embeddings\n", "- **Horizontal Scaling**: Enables distributed architectures"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Initialization"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 66ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PERSISTENT STORAGE ===\n", "Creating a persistent Chroma client...\n", "Setting up embedding function...\n"]}], "source": ["# Import required libraries\n", "import chromadb\n", "from chromadb.utils import embedding_functions\n", "import time\n", "import numpy as np\n", "import os\n", "\n", "print(\"\\n=== PERSISTENT STORAGE ===\")\n", "# Set up the storage path with error handling\n", "db_path = \"../scratch/chroma_db\"\n", "os.makedirs(db_path, exist_ok=True)  # Create directory if it doesn't exist\n", "\n", "# Initialize a persistent client that stores data on disk\n", "print(\"Creating a persistent Chroma client...\")\n", "client = chromadb.PersistentClient(path=db_path)\n", "\n", "# Create an embedding function using the SentenceTransformer model\n", "# all-MiniLM-L6-v2 is a good balance of speed and quality for most use cases\n", "print(\"Setting up embedding function...\")\n", "embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(\n", "    model_name=\"all-MiniLM-L6-v2\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Helper function to display query results in a readable format\n", "def display_results(results):\n", "    print(\"\\nResults:\")\n", "    for i, (doc, doc_id, metadata, distance) in enumerate(zip(\n", "        results['documents'][0],\n", "        results['ids'][0],\n", "        results['metadatas'][0],\n", "        results['distances'][0]\n", "    )):\n", "        print(f\"{i+1}. Document: {doc}\")\n", "        print(f\"   ID: {doc_id}\")\n", "        print(f\"   Metadata: {metadata}\")\n", "        print(f\"   Distance: {distance}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating and Using a Persistent Collection\n", "\n", "Below we create a persistent collection and add sample documents to demonstrate basic operations."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating a persistent collection...\n", "\n", "Results:\n", "1. Document: This is a document that will be stored persistently\n", "   ID: pdoc1\n", "   Metadata: None\n", "   Distance: 0.7845402441433474\n", "\n"]}], "source": ["# Create a new collection with our embedding function\n", "print(\"Creating a persistent collection...\")\n", "persistent_collection = client.create_collection(\n", "    name=\"persistent_docs\",\n", "    embedding_function=embedding_function\n", ")\n", "\n", "# Sample documents about data persistence\n", "documents = [\n", "    \"This is a document that will be stored persistently\",\n", "    \"Vector databases need to persist data for production use\",\n", "    \"Data persistence ensures your embeddings survive restarts\"\n", "]\n", "\n", "# Add documents with unique IDs\n", "ids = [\"pdoc1\", \"pdoc2\", \"pdoc3\"]\n", "persistent_collection.add(\n", "    documents=documents,\n", "    ids=ids\n", ")\n", "\n", "# Query the collection to verify it works\n", "results = persistent_collection.query(\n", "    query_texts=[\"persistent data storage\"],\n", "    n_results=1\n", ")\n", "\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Why Performance Matters\n", "\n", "When working with vector databases in production, performance becomes critical for several reasons:\n", "\n", "- **User Experience**: Slow query responses lead to poor user experiences\n", "- **Resource Utilization**: Inefficient operations consume excessive computational resources\n", "- **Scaling Challenges**: Performance problems compound as your data grows\n", "- **Cost Implications**: In cloud environments, inefficient operations directly impact costs\n", "\n", "Below we'll conduct a simple performance test with a larger dataset."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PERFORMANCE CONSIDERATIONS ===\n", "Generating synthetic documents for performance testing...\n", "Adding 1000 documents to collection...\n", "Time to add 1000 documents: 4.57 seconds\n", "\n", "Testing query performance...\n", "Query 1: 0.0148 seconds\n", "Query 2: 0.0134 seconds\n", "Query 3: 0.0131 seconds\n", "Query 4: 0.0135 seconds\n", "Query 5: 0.0141 seconds\n", "Average query time: 0.0138 seconds\n"]}], "source": ["print(\"\\n=== PERFORMANCE CONSIDERATIONS ===\")\n", "\n", "# Create a separate collection for performance testing\n", "collection = client.create_collection(\n", "    name=\"performance_test\",\n", "    embedding_function=embedding_function\n", ")\n", "\n", "# Generate 1000 synthetic documents using random combinations of AI-related words\n", "print(\"Generating synthetic documents for performance testing...\")\n", "words = [\"AI\", \"machine\", \"learning\", \"vector\", \"database\", \"embedding\", \"neural\",\n", "         \"network\", \"transformer\", \"data\", \"science\", \"engineering\", \"model\",\n", "         \"algorithm\", \"optimization\", \"natural\", \"language\", \"processing\"]\n", "\n", "num_docs = 1000\n", "documents = []\n", "\n", "for i in range(num_docs):\n", "    # Create a random document of 10-20 words\n", "    doc_len = np.random.randint(10, 20)\n", "    doc = \" \".join(np.random.choice(words, size=doc_len))\n", "    documents.append(doc)\n", "\n", "ids = [f\"perf_doc_{i}\" for i in range(num_docs)]\n", "\n", "# Measure time to add documents in batches\n", "print(f\"Adding {num_docs} documents to collection...\")\n", "start_time = time.time()\n", "\n", "# Process in batches of 100 for better performance\n", "batch_size = 100\n", "for i in range(0, num_docs, batch_size):\n", "    end_idx = min(i + batch_size, num_docs)\n", "    collection.add(\n", "        documents=documents[i:end_idx],\n", "        ids=ids[i:end_idx]\n", "    )\n", "\n", "add_time = time.time() - start_time\n", "print(f\"Time to add {num_docs} documents: {add_time:.2f} seconds\")\n", "\n", "# Measure query performance across multiple queries\n", "print(\"\\nTesting query performance...\")\n", "query_times = []\n", "num_queries = 5\n", "\n", "for i in range(num_queries):\n", "    # Generate a random 5-word query\n", "    query = \" \".join(np.random.choice(words, size=5))\n", "    start_time = time.time()\n", "    collection.query(\n", "        query_texts=[query],\n", "        n_results=10\n", "    )\n", "    query_time = time.time() - start_time\n", "    query_times.append(query_time)\n", "    print(f\"Query {i+1}: {query_time:.4f} seconds\")\n", "\n", "print(f\"Average query time: {np.mean(query_times):.4f} seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance Test Results\n", "\n", "Let's visualize the query performance results with a chart:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualizing query performance\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 5))\n", "plt.bar(range(1, num_queries + 1), query_times, color='skyblue')\n", "plt.axhline(y=np.mean(query_times), color='r', linestyle='-', label=f'Average: {np.mean(query_times):.4f}s')\n", "plt.xlabel('Query Number')\n", "plt.ylabel('Time (seconds)')\n", "plt.title('Query Performance')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "1. **Persistence**: ChromaDB's PersistentClient provides an easy way to store vector embeddings on disk\n", "2. **Performance**: \n", "   - Batch processing improves throughput when adding documents\n", "   - Query performance remains fast even with 1000 documents (~13ms)\n", "   - Consider monitoring performance as your collection size grows\n", "3. **Production Readiness**:\n", "   - Use persistent storage in production environments\n", "   - Implement proper error handling\n", "   - Consider scaling strategies for larger datasets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Best Practices for Production Use\n", "\n", "1. **Indexing Strategy**: For larger collections (>100K documents), consider using HNSW indexing (see next chapter for info).\n", "   ```python\n", "   collection = client.create_collection(\n", "       name=\"large_collection\",\n", "       metadata={\"hnsw:space\": \"cosine\", \"hnsw:construction_ef\": 100, \"hnsw:search_ef\": 50}\n", "   )\n", "   ```\n", "\n", "2. **Metadata Usage**: Enhance documents with metadata for filtering\n", "   ```python\n", "   collection.add(\n", "       documents=[\"Document text...\"],\n", "       ids=[\"unique_id\"],\n", "       metadatas=[{\"category\": \"finance\", \"date\": \"2025-04-01\"}]\n", "   )\n", "   ```\n", "\n", "3. **Hybrid Search**: Combine vector search with metadata filtering\n", "   ```python\n", "   results = collection.query(\n", "       query_texts=[\"finance report\"],\n", "       where={\"category\": \"finance\"},\n", "       n_results=5\n", "   )\n", "   ```\n", "\n", "4. **Regular Backups**: Implement a backup strategy for your database directory"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}