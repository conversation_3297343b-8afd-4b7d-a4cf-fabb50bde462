{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Scaling Strategies (ANN)\n", "\n", "## Key Scaling Considerations\n", "\n", "1. **Speed vs. Accuracy** - Understanding the tradeoffs between query performance and result quality\n", "2. **Resource Limitations** - Managing memory, CPU, and storage constraints\n", "3. **<PERSON><PERSON>** - Distributing the workload across multiple instances"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approximate Nearest Neighbor (ANN) Implementations\n", "\n", "ANN algorithms like HNSW (Hierarchical Navigable Small World) allow us to trade some accuracy for significant performance improvements at scale. We'll explore different HNSW configurations and their impact on search performance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup and Initialization"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 9ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import chromadb\n", "from chromadb.utils import embedding_functions\n", "import time\n", "\n", "# Initialize ChromaDB client\n", "client = chromadb.Client()\n", "embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(\n", "  model_name=\"all-MiniLM-L6-v2\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating Collections with Different HNSW Configurations\n", "\n", "We'll create three collections with different index settings:\n", "\n", "1. **Default** - Uses ChromaDB's default configuration\n", "2. **High Accuracy** - Prioritizes result quality with higher `ef` and `M` values\n", "3. **Fast Search** - Prioritizes speed with lower `ef` and `M` values\n", "\n", "**Parameter Explanation:**\n", "- `hnsw:space`: The distance metric used (cosine, euclidean, etc.)\n", "- `hnsw:construction_ef`: Controls index build quality (higher = better quality, slower build)\n", "- `hnsw:search_ef`: Controls search quality (higher = better quality, slower search)\n", "- `hnsw:M`: Controls the maximum number of connections per node (higher = better quality, more memory)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create collections with different HNSW configurations\n", "collections = {}\n", "\n", "# 1. Default settings\n", "collections[\"default\"] = client.create_collection(\n", "    name=\"default_index\",\n", "    embedding_function=embedding_function\n", ")\n", "\n", "# 2. High accuracy configuration\n", "collections[\"high_accuracy\"] = client.create_collection(\n", "    name=\"high_accuracy_index\",\n", "    embedding_function=embedding_function,\n", "    metadata={\"hnsw:space\": \"cosine\", \"hnsw:construction_ef\": 1000, \"hnsw:search_ef\": 1250, \"hnsw:M\": 36}\n", ")\n", "\n", "# 3. Fast search configuration\n", "collections[\"fast_search\"] = client.create_collection(\n", "    name=\"fast_search_index\",\n", "    embedding_function=embedding_function,\n", "    metadata={\"hnsw:space\": \"cosine\", \"hnsw:construction_ef\": 80, \"hnsw:search_ef\": 40, \"hnsw:M\": 12}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating Sample Documents\n", "\n", "Now let's create some sample documents across different categories to populate our collections."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating 10000 sample documents...\n", "['This is document 0 about technology with some additional text to make it more unique.', 'This is document 1 about science with some additional text to make it more unique.', 'This is document 2 about health with some additional text to make it more unique.', 'This is document 3 about business with some additional text to make it more unique.', 'This is document 4 about entertainment with some additional text to make it more unique.']\n"]}], "source": ["# Generate sample data\n", "num_docs = 10000\n", "print(f\"Generating {num_docs} sample documents...\")\n", "\n", "# Create documents with some patterns for testing\n", "categories = [\"technology\", \"science\", \"health\", \"business\", \"entertainment\"]\n", "documents = []\n", "ids = []\n", "\n", "for i in range(num_docs):\n", "    category = categories[i % len(categories)]\n", "    document = f\"This is document {i} about {category} with some additional text to make it more unique.\"\n", "    documents.append(document)\n", "    ids.append(f\"doc_{i}\")\n", "\n", "print(documents[0:5])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding Documents to Collections\n", "\n", "Let's add the generated documents to all three collections."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding documents to collections with different index configurations...\n", "  Added 10000 documents to default collection in 35.3865 seconds\n", "  Added 10000 documents to high_accuracy collection in 36.5990 seconds\n", "  Added 10000 documents to fast_search collection in 34.1713 seconds\n"]}], "source": ["import time\n", "\n", "print(\"Adding documents to collections with different index configurations...\")\n", "for name, collection in collections.items():\n", "    start_time = time.time()\n", "\n", "    collection.add(\n", "        documents=documents,\n", "        ids=ids\n", "    )\n", "\n", "    end_time = time.time()\n", "    elapsed_time = end_time - start_time\n", "\n", "    print(\n", "        f\"  Added {num_docs} documents to {name} collection in {elapsed_time:.4f} seconds\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Benchmark Query Performance\n", "\n", "Now let's evaluate how each configuration performs with a set of representative queries."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Benchmarking query performance across different configurations...\n"]}], "source": ["# Benchmark query performance\n", "print(\"\\nBenchmarking query performance across different configurations...\")\n", "\n", "# Prepare queries\n", "query_texts = [\n", "    \"Latest technology trends in artificial intelligence\",\n", "    \"Scientific research on climate change\",\n", "    \"Health benefits of regular exercise\",\n", "    \"Business strategies for startups\",\n", "    \"Entertainment news about recent movie releases\"\n", "]\n", "\n", "# Set up benchmark parameters\n", "results = {}\n", "num_trials = 5"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing default configuration:\n", "  Query: 'Latest technology trends in ar...': 0.0144 seconds\n", "  Query: 'Scientific research on climate...': 0.0121 seconds\n", "  Query: 'Health benefits of regular exe...': 0.0119 seconds\n", "  Query: 'Business strategies for startu...': 0.0128 seconds\n", "  Query: 'Entertainment news about recen...': 0.0125 seconds\n", "\n", "Testing high_accuracy configuration:\n", "  Query: 'Latest technology trends in ar...': 0.0134 seconds\n", "  Query: 'Scientific research on climate...': 0.0148 seconds\n", "  Query: 'Health benefits of regular exe...': 0.0140 seconds\n", "  Query: 'Business strategies for startu...': 0.0128 seconds\n", "  Query: 'Entertainment news about recen...': 0.0129 seconds\n", "\n", "Testing fast_search configuration:\n", "  Query: 'Latest technology trends in ar...': 0.0122 seconds\n", "  Query: 'Scientific research on climate...': 0.0115 seconds\n", "  Query: 'Health benefits of regular exe...': 0.0116 seconds\n", "  Query: 'Business strategies for startu...': 0.0116 seconds\n", "  Query: 'Entertainment news about recen...': 0.0118 seconds\n"]}], "source": ["# Run benchmark for each collection\n", "for name, collection in collections.items():\n", "    print(f\"\\nTesting {name} configuration:\")\n", "    times = []\n", "    \n", "    for query in query_texts:\n", "        query_times = []\n", "        \n", "        for _ in range(num_trials):\n", "            start_time = time.time()\n", "            collection.query(\n", "                query_texts=[query],\n", "                n_results=10\n", "            )\n", "            query_time = time.time() - start_time\n", "            query_times.append(query_time)\n", "        \n", "        avg_time = sum(query_times) / len(query_times)\n", "        times.append(avg_time)\n", "        print(f\"  Query: '{query[:30]}...': {avg_time:.4f} seconds\")\n", "    \n", "    results[name] = {\n", "        \"mean\": sum(times) / len(times),\n", "        \"min\": min(times),\n", "        \"max\": max(times),\n", "        \"times\": times\n", "    }"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Performance Summary:\n", "  default: Mean=0.0127s, Min=0.0119s, Max=0.0144s\n", "  high_accuracy: Mean=0.0136s, Min=0.0128s, Max=0.0148s\n", "  fast_search: Mean=0.0117s, Min=0.0115s, Max=0.0122s\n"]}], "source": ["# Print summary of benchmark results\n", "print(\"\\nPerformance Summary:\")\n", "for name, metrics in results.items():\n", "    print(f\"  {name}: Mean={metrics['mean']:.4f}s, Min={metrics['min']:.4f}s, Max={metrics['max']:.4f}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion and Key Takeaways\n", "\n", "In this notebook, we've explored practical approaches to scaling vector databases for production use using ANNs:\n", "\n", "**ANN Implementations**\n", "   - Configuring HNSW parameters allows for customized speed-accuracy tradeoffs\n", "   - The right configuration depends on your specific application requirements"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}