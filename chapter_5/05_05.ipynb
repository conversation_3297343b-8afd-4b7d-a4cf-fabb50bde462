{"cells": [{"cell_type": "markdown", "id": "ed3625d1", "metadata": {}, "source": ["## Scaling Strategies (Caching)\n", "\n", "Caching is a crucial optimization technique for production systems. It can significantly reduce latency and computational load by storing frequently accessed results.\n", "\n", "### Why Implement Caching?\n", "\n", "1. **Reduced Latency** - Cached results can be returned instantly without computing embeddings or searching the vector space\n", "2. **Lower Computational Costs** - Fewer embedding calculations mean lower GPU/CPU usage\n", "3. **Better Scalability** - Handle more queries with the same resources\n", "\n", "We'll implement a simple LRU (Least Recently Used) cache and measure its performance impact."]}, {"cell_type": "markdown", "id": "a66599d3", "metadata": {}, "source": ["# Setup and Initialization"]}, {"cell_type": "code", "execution_count": 1, "id": "2c121134", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.9 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m2 packages\u001b[0m \u001b[2min 9ms\u001b[0m\u001b[0m\n"]}], "source": ["# Install the required packages\n", "!uv pip install accelerate==1.6.0 sentence-transformers==4.0.2"]}, {"cell_type": "code", "execution_count": 2, "id": "b3903959", "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import chromadb\n", "from chromadb.utils import embedding_functions\n", "import time\n", "import random"]}, {"cell_type": "markdown", "id": "4541a3e8", "metadata": {}, "source": ["### LRU Cache Implementation\n", "\n", "Let's implement a simple LRU (Least Recently Used) cache. This type of cache keeps track of which queries are used most frequently and evicts the least recently used entries when the cache is full."]}, {"cell_type": "code", "execution_count": 3, "id": "000972fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CACHING IMPLEMENTATION ===\n"]}], "source": ["print(\"\\n=== CACHING IMPLEMENTATION ===\")\n", "\n", "# Implement a simple LRU cache\n", "class LRUCache:\n", "    def __init__(self, capacity=100):\n", "        self.capacity = capacity  # Maximum number of items the cache can hold\n", "        self.cache = {}           # Dictionary to store cache items\n", "        self.usage_order = []     # List to track access order\n", "    \n", "    def get(self, key):\n", "        if key in self.cache:\n", "            # Update usage order - move to end of list (most recently used)\n", "            self.usage_order.remove(key)\n", "            self.usage_order.append(key)\n", "            return self.cache[key]\n", "        return None  # <PERSON><PERSON> miss\n", "    \n", "    def put(self, key, value):\n", "        if key in self.cache:\n", "            # Update existing entry\n", "            self.cache[key] = value\n", "            self.usage_order.remove(key)\n", "            self.usage_order.append(key)\n", "        else:\n", "            # Add new entry\n", "            if len(self.cache) >= self.capacity:\n", "                # Evict least recently used item\n", "                lru_key = self.usage_order.pop(0)\n", "                del self.cache[lru_key]\n", "            \n", "            self.cache[key] = value\n", "            self.usage_order.append(key)\n", "    \n", "    def clear(self):\n", "        self.cache = {}\n", "        self.usage_order = []\n", "    \n", "    def __len__(self):\n", "        return len(self.cache)"]}, {"cell_type": "markdown", "id": "86240000", "metadata": {}, "source": ["### Setting Up the Collection\n", "\n", "Now let's create a collection and populate it with sample documents for our caching experiment."]}, {"cell_type": "code", "execution_count": 4, "id": "2ad47ad8", "metadata": {}, "outputs": [], "source": ["# Initialize Chroma\n", "client = chromadb.Client()\n", "embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(\n", "    model_name=\"all-MiniLM-L6-v2\"\n", ")\n", "\n", "# Create a collection\n", "collection = client.create_collection(\n", "    name=\"cache_test\",\n", "    embedding_function=embedding_function\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "cc0b50b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Added 1000 documents to the collection\n"]}], "source": ["# Add sample documents\n", "num_docs = 1000\n", "documents = [\n", "    f\"This is a sample document {i} with various content for testing caching\" for i in range(num_docs)]\n", "ids = [f\"cache_doc_{i}\" for i in range(num_docs)]\n", "\n", "# Add documents in batches to avoid overwhelming the system\n", "for i in range(0, num_docs, 100):\n", "    end_idx = min(i + 100, num_docs)\n", "\n", "    collection.add(\n", "        documents=documents[i:end_idx],\n", "        ids=ids[i:end_idx]\n", "    )\n", "\n", "print(f\"Added {num_docs} documents to the collection\")"]}, {"cell_type": "markdown", "id": "bc82672f", "metadata": {}, "source": ["### Cached Query Function\n", "\n", "Let's implement a function that uses our cache to store and retrieve query results."]}, {"cell_type": "code", "execution_count": 6, "id": "b87c0f8d", "metadata": {}, "outputs": [], "source": ["# Initialize cache with a capacity of 50 entries\n", "query_cache = LRUCache(capacity=50)\n", "\n", "# Function to query with caching\n", "def cached_query(query_text, n_results=10, use_cache=True):\n", "    # Create a unique cache key from the query text and number of results\n", "    cache_key = f\"{query_text}:{n_results}\"\n", "\n", "    if use_cache:\n", "        # Check cache first\n", "        cached_result = query_cache.get(cache_key)\n", "        if cached_result is not None:\n", "            return cached_result, True  # Cache hit\n", "\n", "    # Cache miss or cache disabled, perform actual query\n", "    result = collection.query(\n", "        query_texts=[query_text],\n", "        n_results=n_results\n", "    )\n", "\n", "    if use_cache:\n", "        # Update cache\n", "        query_cache.put(cache_key, result)\n", "\n", "    return result, False  # <PERSON><PERSON> miss"]}, {"cell_type": "markdown", "id": "babcac93", "metadata": {}, "source": ["### Preparing Query Mix\n", "\n", "To simulate a realistic workload, we'll create a mix of common (frequently repeated) and unique queries."]}, {"cell_type": "code", "execution_count": 7, "id": "b1315272", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing query performance with caching:\n"]}], "source": ["# Test queries with varying cache hit rates\n", "print(\"\\nTesting query performance with caching:\")\n", "\n", "# Prepare query mix (some repeated, some unique)\n", "common_queries = [\n", "    \"document with content\",\n", "    \"sample document\",\n", "    \"testing caching\",\n", "    \"various content\"\n", "]\n", "\n", "unique_queries = [f\"unique query {i}\" for i in range(50)]\n", "\n", "# Mix queries with different distributions to test cache performance\n", "mixed_queries = []\n", "for _ in range(20):\n", "    # Add common queries (higher probability)\n", "    mixed_queries.extend(common_queries)\n", "    \n", "    # Add some unique queries\n", "    mixed_queries.extend(random.sample(unique_queries, 5))\n", "\n", "# Shuffle to ensure realistic query pattern\n", "random.shuffle(mixed_queries)"]}, {"cell_type": "markdown", "id": "52f599ab", "metadata": {}, "source": ["### Benchmark: <PERSON>ache vs. <PERSON> Cache\n", "\n", "Now let's measure the performance difference between running queries without a cache versus with a cache."]}, {"cell_type": "code", "execution_count": 8, "id": "a891debb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running queries without cache...\n"]}], "source": ["# Run without cache\n", "print(\"Running queries without cache...\")\n", "start_time = time.time()\n", "\n", "for query in mixed_queries:\n", "    _, _ = cached_query(query, use_cache=False)\n", "\n", "no_cache_time = time.time() - start_time"]}, {"cell_type": "code", "execution_count": 9, "id": "b2606e6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running queries with cache...\n"]}], "source": ["# Run with cache\n", "print(\"Running queries with cache...\")\n", "query_cache.clear()  # Clear the cache\n", "\n", "start_time = time.time()\n", "hits = 0\n", "\n", "for query in mixed_queries:\n", "    _, is_hit = cached_query(query, use_cache=True)\n", "    if is_hit:\n", "        hits += 1\n", "\n", "with_cache_time = time.time() - start_time\n", "hit_rate = hits / len(mixed_queries)"]}, {"cell_type": "code", "execution_count": 10, "id": "21571b5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Cache Performance Results:\n", "  Without cache: 2.1609 seconds\n", "  With cache: 0.5848 seconds\n", "  Time saved: 1.5761 seconds (72.9%)\n", "  Cache hit rate: 72.8%\n", "  Cache size: 49\n"]}], "source": ["# Report results\n", "print(\"\\nCache Performance Results:\")\n", "print(f\"  Without cache: {no_cache_time:.4f} seconds\")\n", "print(f\"  With cache: {with_cache_time:.4f} seconds\")\n", "print(\n", "    f\"  Time saved: {no_cache_time - with_cache_time:.4f} seconds ({(1 - with_cache_time/no_cache_time) * 100:.1f}%)\")\n", "print(f\"  Cache hit rate: {hit_rate:.1%}\")\n", "print(f\"  Cache size: {len(query_cache)}\")"]}, {"cell_type": "markdown", "id": "894b85f9", "metadata": {}, "source": ["## Advanced Scaling Strategies\n", "\n", "### <PERSON><PERSON> Scaling Approaches\n", "\n", "As your vector database grows beyond the capacity of a single machine, you'll need to implement horizontal scaling strategies. Here are some common approaches:\n", "\n", "1. **Sharding** - Partitioning your vector space across multiple instances\n", "   - **By ID range** - Deterministic but may lead to unbalanced shards\n", "   - **By vector clustering** - Better search performance but more complex\n", "\n", "2. **Replication** - Creating copies of your data across multiple instances\n", "   - Improves read throughput and fault tolerance\n", "   - Requires synchronization mechanisms for writes\n", "\n", "3. **Hybrid approaches** - Combining sharding and replication\n", "   - Example: ChromaDB cluster with data sharded across nodes and each shard replicated\n", "\n", "### Resource Management Best Practices\n", "\n", "1. **Memory Optimization**\n", "   - Use quantization to reduce vector size (e.g., 32-bit to 8-bit)\n", "   - Implement disk-based storage for less frequently accessed vectors\n", "\n", "2. **CPU Utilization**\n", "   - Batch similar operations\n", "   - Use asynchronous processing where possible\n", "\n", "3. **Network Efficiency**\n", "   - Minimize data transfer between components\n", "   - Compress payloads when possible\n", "\n", "### Real-world Implementation Considerations\n", "\n", "1. **Monitoring and Observability**\n", "   - Track latency, throughput, and error rates\n", "   - Set up alerts for performance degradation\n", "\n", "2. **Failure Handling**\n", "   - Implement graceful degradation strategies\n", "   - Consider fallback search methods\n", "\n", "3. **Update Strategies**\n", "   - Batch updates to reduce index rebuilding frequency\n", "   - Consider incremental index updates\n", "\n", "4. **Hybrid Search Approaches**\n", "   - Combine vector search with keyword search for better results\n", "   - Filter vectors based on metadata before computing distances"]}, {"cell_type": "markdown", "id": "539ab38c", "metadata": {}, "source": ["## Conclusion and Key Takeaways\n", "\n", "In this notebook, we've explored practical approaches to scaling vector databases for production use:\n", "\n", "**Caching**\n", "   - Can significantly reduce latency (70%+ in our example)\n", "   - Most effective when query patterns show temporal locality\n", "\n", "**Advanced Scaling Strategies**\n", "   - Horizontal scaling through sharding and replication\n", "   - Resource optimization across memory, CPU, and network\n", "   - Operational considerations for production deployments"]}, {"cell_type": "markdown", "id": "89cc98e7", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}