{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Implementing BM25 and Lexical Search\n", "\n", "## Overview\n", "This notebook demonstrates how to implement and compare two different search approaches:\n", "1. **BM25 (Lexical Search)** - A ranking function that uses term frequency for information retrieval\n", "2. **Vector Search** - Uses embeddings to capture semantic meaning\n", "\n", "We'll explore how these methods differ in their approach and when one might be preferred over the other."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Dependencies\n", "First, let's install the necessary dependencies."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.10 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 176ms\u001b[0m\u001b[0m\n"]}], "source": ["# This installs the embeddings, it can take a while to install so I didn't include in the the default requirements.txt\n", "!uv pip install llama-index-embeddings-huggingface"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample Documents\n", "We'll create a collection of sample documents related to machine learning concepts to test our search methods."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Sample docs\n", "from llama_index.core import Document\n", "\n", "# Creating a diverse set of AI/ML related documents for our retrieval experiments\n", "documents = [\n", "    Document(text=\"Machine learning is a branch of artificial intelligence focused on building systems that learn from data.\",\n", "             metadata={\"title\": \"Machine Learning Basics\"}),\n", "    Document(text=\"Transformers are neural network models that use self-attention mechanisms to process sequential data.\",\n", "             metadata={\"title\": \"Transformer Architecture\"}),\n", "    Document(text=\"Python code for neural networks typically uses libraries like TensorFlow or PyTorch.\",\n", "             metadata={\"title\": \"Neural Network Code\"}),\n", "    Document(text=\"The backpropagation algorithm calculates gradients by applying the chain rule backwards through the network.\",\n", "             metadata={\"title\": \"Backpropagation Algorithm\"}),\n", "    Document(text=\"BM25 is a ranking function used in information retrieval systems based on term frequency.\",\n", "             metadata={\"title\": \"BM25 Algorithm\"}),\n", "    Document(text=\"Artificial intelligence concepts include reasoning, learning, and adaptation in complex environments.\",\n", "             metadata={\"title\": \"AI Concepts\"}),\n", "    Document(text=\"Deep learning is a subset of machine learning that uses multi-layered neural networks to extract complex patterns.\",\n", "             metadata={\"title\": \"Deep Learning Introduction\"}),\n", "    Document(text=\"Convolutional Neural Networks (CNNs) are specialized neural architectures designed for image processing and computer vision tasks.\",\n", "             metadata={\"title\": \"CNN Architecture\"}),\n", "    Document(text=\"Natural Language Processing (NLP) uses computational techniques to analyze and understand human language text and speech.\",\n", "             metadata={\"title\": \"NLP Fundamentals\"}),\n", "    Document(text=\"Reinforcement learning is a training method based on rewarding desired behaviors and punishing undesired ones.\",\n", "             metadata={\"title\": \"Reinforcement Learning\"}),\n", "    Document(text=\"Vector databases store high-dimensional vectors for efficient similarity search and retrieval.\",\n", "             metadata={\"title\": \"Vector Database Systems\"}),\n", "    Document(text=\"The BERT language model uses bidirectional training to understand context from both directions in text.\",\n", "             metadata={\"title\": \"BERT Model\"}),\n", "    Document(text=\"Hybrid retrieval systems combine multiple search techniques like BM25 and vector search for improved results.\",\n", "             metadata={\"title\": \"Hybrid Retrieval\"})\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Helper Function\n", "This function will help us test our retrieval methods with different queries and display the results."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from llama_index.core.schema import QueryBundle\n", "\n", "# Helper function to display retrieval results in a readable format\n", "def test_bm25_retrieval(retriever, queries):\n", "    \"\"\"Test BM25 retriever with a list of queries.\"\"\"\n", "    for query in queries:\n", "        print(f\"\\n{'='*80}\\nQuery: {query}\\n{'='*80}\")\n", "        query_bundle = QueryBundle(query_str=query)\n", "        results = retriever.retrieve(query_bundle)\n", "\n", "        print(f\"Found {len(results)} relevant documents\\n\")\n", "        for i, result in enumerate(results):\n", "            print(f\"Result {i+1} (Score: {result.score:.8f}):\")\n", "            print(f\"  {result.node.get_content()[:200]}...\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BM25 Retrieval\n", "\n", "### What is BM25?\n", "BM25 (Best Matching 25) is a ranking function used in information retrieval. It's a lexical search method that scores documents based on the query terms appearing in each document, using term frequency and document length. \n", "\n", "Key characteristics:\n", "- Purely lexical (keyword-based)\n", "- No embeddings or neural networks required\n", "- Works well for exact term matching\n", "- Cannot understand semantic relationships between words"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 13 documents\n", "\n", "================================================================================\n", "Query: What is machine learning?\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 1.72518969):\n", "  Machine learning is a branch of artificial intelligence focused on building systems that learn from data....\n", "\n", "Result 2 (Score: 1.36624670):\n", "  Deep learning is a subset of machine learning that uses multi-layered neural networks to extract complex patterns....\n", "\n", "Result 3 (Score: 0.65637398):\n", "  Reinforcement learning is a training method based on rewarding desired behaviors and punishing undesired ones....\n", "\n", "\n", "================================================================================\n", "Query: How do transformers work?\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 1.29171598):\n", "  Transformers are neural network models that use self-attention mechanisms to process sequential data....\n", "\n", "Result 2 (Score: 0.00000000):\n", "  The BERT language model uses bidirectional training to understand context from both directions in text....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  Vector databases store high-dimensional vectors for efficient similarity search and retrieval....\n", "\n", "\n", "================================================================================\n", "Query: Explain the limitations of BM25\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 1.01910138):\n", "  BM25 is a ranking function used in information retrieval systems based on term frequency....\n", "\n", "Result 2 (Score: 0.65928197):\n", "  Hybrid retrieval systems combine multiple search techniques like BM25 and vector search for improved results....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  The BERT language model uses bidirectional training to understand context from both directions in text....\n", "\n", "\n", "================================================================================\n", "Query: Python code examples for neural networks\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 3.39699340):\n", "  Python code for neural networks typically uses libraries like TensorFlow or PyTorch....\n", "\n", "Result 2 (Score: 0.98580790):\n", "  Convolutional Neural Networks (CNNs) are specialized neural architectures designed for image processing and computer vision tasks....\n", "\n", "Result 3 (Score: 0.84174478):\n", "  Transformers are neural network models that use self-attention mechanisms to process sequential data....\n", "\n", "\n", "\n", "Testing with technical queries:\n", "\n", "================================================================================\n", "Query: dropout regularization technique\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 0.65928197):\n", "  Hybrid retrieval systems combine multiple search techniques like BM25 and vector search for improved results....\n", "\n", "Result 2 (Score: 0.65928197):\n", "  Natural Language Processing (NLP) uses computational techniques to analyze and understand human language text and speech....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  The BERT language model uses bidirectional training to understand context from both directions in text....\n", "\n", "\n", "================================================================================\n", "Query: backpropagation algorithm\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 2.34038115):\n", "  The backpropagation algorithm calculates gradients by applying the chain rule backwards through the network....\n", "\n", "Result 2 (Score: 0.72356200):\n", "  BM25 is a ranking function used in information retrieval systems based on term frequency....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  Vector databases store high-dimensional vectors for efficient similarity search and retrieval....\n", "\n", "\n", "================================================================================\n", "Query: cross-entropy loss function\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 0.93810874):\n", "  BM25 is a ranking function used in information retrieval systems based on term frequency....\n", "\n", "Result 2 (Score: 0.00000000):\n", "  The BERT language model uses bidirectional training to understand context from both directions in text....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  Vector databases store high-dimensional vectors for efficient similarity search and retrieval....\n", "\n", "\n", "================================================================================\n", "Query: BERT pre-training objective\n", "================================================================================\n", "Found 3 relevant documents\n", "\n", "Result 1 (Score: 1.94284701):\n", "  The BERT language model uses bidirectional training to understand context from both directions in text....\n", "\n", "Result 2 (Score: 0.70078641):\n", "  Reinforcement learning is a training method based on rewarding desired behaviors and punishing undesired ones....\n", "\n", "Result 3 (Score: 0.00000000):\n", "  Hybrid retrieval systems combine multiple search techniques like BM25 and vector search for improved results....\n", "\n"]}], "source": ["# Import necessary modules\n", "from llama_index.retrievers.bm25 import BM25Retriever\n", "from llama_index.core.node_parser import SentenceSplitter\n", "\n", "print(f\"Loaded {len(documents)} documents\")\n", "\n", "# Split documents into nodes (chunks)\n", "# The SentenceSplitter breaks documents into smaller chunks for processing\n", "splitter = SentenceSplitter(chunk_size=200)\n", "nodes = splitter.get_nodes_from_documents(documents)\n", "\n", "# Create BM25 Retriever - Note: no embeddings model needed for BM25!\n", "# BM25 works purely on lexical matching (word frequencies)\n", "bm25_retriever = BM25Retriever.from_defaults(\n", "    nodes=nodes,\n", "    similarity_top_k=3  # Return the top 3 most relevant documents\n", ")\n", "\n", "# Run test queries - see how BM25 performs on basic questions\n", "test_queries = [\n", "    \"What is machine learning?\",\n", "    \"How do transformers work?\",\n", "    \"Explain the limitations of BM25\",\n", "    \"Python code examples for neural networks\"\n", "]\n", "test_bm25_retrieval(bm25_retriever, test_queries)\n", "\n", "# Compare with specific technical terms - BM25 should do well with exact terminology\n", "technical_queries = [\n", "    \"dropout regularization technique\",\n", "    \"backpropagation algorithm\",\n", "    \"cross-entropy loss function\",\n", "    \"BERT pre-training objective\"\n", "]\n", "print(\"\\n\\nTesting with technical queries:\")\n", "test_bm25_retrieval(bm25_retriever, technical_queries)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Part 2: Comparing BM25 vs. Vector Search\n", "\n", "### Vector Search Overview\n", "Vector search uses embeddings to represent documents and queries in a high-dimensional space, then finds documents that are \"close\" to the query in this space.\n", "\n", "Key characteristics:\n", "- Based on semantic similarity (meaning) rather than exact words\n", "- Uses neural networks to create embeddings\n", "- Can understand synonyms and related concepts\n", "- May miss exact keyword matches that BM25 would catch\n", "\n", "Let's compare both approaches on the same queries:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 13 sample documents\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ba10a65bab2849c8a1a2198ccc8cfce7", "version_major": 2, "version_minor": 0}, "text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e8b40c1dd6674bb390b4a568324a57ce", "version_major": 2, "version_minor": 0}, "text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0f181aee5aae4ff5aeac1de73a60e432", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/10.5k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5da21152346437bae771e354dc4a74b", "version_major": 2, "version_minor": 0}, "text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a2ea0a8f7774e5fa0919c2ea13e9560", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "94635ee9676443a5b38304ec9be059e1", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2d569ea3563446b7892c5fe33bf305e4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5a8d67ecfc34fefa23fde4cf45285ee", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ecbae6d225024e98af4af42bfe16b3cd", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6c9c47b87824e60ad451a3877ea3a13", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1ee8a10112cc4eafa4976b618f2491b0", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Running BM25 retriever on: What is machine learning?\n", "Running BM25 retriever on: transformer architecture\n", "Running BM25 retriever on: python neural network code\n", "Running BM25 retriever on: backpropagation algorithm\n", "Running BM25 retriever on: information retrieval\n", "Running Vector retriever on: What is machine learning?\n", "Running Vector retriever on: transformer architecture\n", "Running Vector retriever on: python neural network code\n", "Running Vector retriever on: backpropagation algorithm\n", "Running Vector retriever on: information retrieval\n", "\n", "\n", "Query: What is machine learning?\n", "--------------------------------------------------\n", "BM25 found: Machine Learning Basics, Deep Learning Introduction, Reinforcement Learning, AI Concepts, Hybrid Retrieval\n", "Vector found: Machine Learning Basics, Deep Learning Introduction, Reinforcement Learning, AI Concepts, NLP Fundamentals\n", "Overlap: 4 documents (AI Concepts, Machine Learning Basics, Reinforcement Learning, Deep Learning Introduction)\n", "Unique to BM25: Hybrid Retrieval\n", "Unique to Vector: NLP Fundamentals\n", "\n", "\n", "Query: transformer architecture\n", "--------------------------------------------------\n", "BM25 found: Transformer Architecture, CNN Architecture, Vector Database Systems, BERT Model, Hybrid Retrieval\n", "Vector found: Transformer Architecture, CNN Architecture, Neural Network Code, Deep Learning Introduction, Machine Learning Basics\n", "Overlap: 2 documents (CNN Architecture, Transformer Architecture)\n", "Unique to BM25: Hybrid Retrieval, BERT Model, Vector Database Systems\n", "Unique to Vector: Machine Learning Basics, Deep Learning Introduction, Neural Network Code\n", "\n", "\n", "Query: python neural network code\n", "--------------------------------------------------\n", "BM25 found: Neural Network Code, CNN Architecture, Transformer Architecture, Deep Learning Introduction, Backpropagation Algorithm\n", "Vector found: Neural Network Code, Backpropagation Algorithm, Deep Learning Introduction, Transformer Architecture, CNN Architecture\n", "Overlap: 5 documents (Transformer Architecture, Backpropagation Algorithm, CNN Architecture, Deep Learning Introduction, Neural Network Code)\n", "Unique to BM25: None\n", "Unique to Vector: None\n", "\n", "\n", "Query: backpropagation algorithm\n", "--------------------------------------------------\n", "BM25 found: Backpropagation Algorithm, BM25 Algorithm, Vector Database Systems, BERT Model, Hybrid Retrieval\n", "Vector found: Backpropagation Algorithm, Deep Learning Introduction, Neural Network Code, Reinforcement Learning, Machine Learning Basics\n", "Overlap: 1 documents (Backpropagation Algorithm)\n", "Unique to BM25: Hybrid Retrieval, BERT Model, Vector Database Systems, BM25 Algorithm\n", "Unique to Vector: Machine Learning Basics, Reinforcement Learning, Deep Learning Introduction, Neural Network Code\n", "\n", "\n", "Query: information retrieval\n", "--------------------------------------------------\n", "BM25 found: BM25 Algorithm, Hybrid Retrieval, Vector Database Systems, Reinforcement Learning, BERT Model\n", "Vector found: Hybrid Retrieval, BM25 Algorithm, Vector Database Systems, NLP Fundamentals, Machine Learning Basics\n", "Overlap: 3 documents (Hybrid Retrieval, Vector Database Systems, BM25 Algorithm)\n", "Unique to BM25: BERT Model, Reinforcement Learning\n", "Unique to Vector: Machine Learning Basics, NLP Fundamentals\n"]}], "source": ["from llama_index.core import VectorStoreIndex\n", "from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.retrievers.bm25 import BM25Retriever\n", "from llama_index.core.schema import QueryBundle\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "\n", "print(f\"Created {len(documents)} sample documents\")\n", "\n", "# Create both retriever types for comparison\n", "def create_retrievers(documents):\n", "    # Parse into nodes - Using a large chunk size to keep each document as one node\n", "    parser = SentenceSplitter(chunk_size=2000, chunk_overlap=0)\n", "    nodes = parser.get_nodes_from_documents(documents)\n", "    \n", "    # 1. BM25 Retriever - lexical search based on term frequencies\n", "    bm25_retriever = BM25Retriever.from_defaults(\n", "        nodes=nodes, similarity_top_k=5)\n", "\n", "    # 2. Vector Retriever - semantic search based on embeddings\n", "    # Load a pre-trained embedding model\n", "    embed_model = HuggingFaceEmbedding(\n", "        model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n", "\n", "    # Create vector index and retriever\n", "    vector_index = VectorStoreIndex(\n", "        nodes,         \n", "        embed_model=embed_model\n", "        )\n", "    vector_retriever = vector_index.as_retriever(similarity_top_k=5)\n", "\n", "    return {\"BM25\": bm25_retriever, \"Vector\": vector_retriever}\n", "\n", "# Compare the two retrieval methods\n", "def compare_retrievers(retrievers, queries):\n", "    results = {}\n", "\n", "    for name, retriever in retrievers.items():\n", "        method_results = []\n", "\n", "        for query in queries:\n", "            print(f\"Running {name} retriever on: {query}\")\n", "            query_bundle = QueryBundle(query_str=query)\n", "            retrieved = retriever.retrieve(query_bundle)\n", "\n", "            # Store results with titles for better readability\n", "            result = {\n", "                \"query\": query,\n", "                \"titles\": [node.node.metadata.get(\"title\") for node in retrieved]\n", "            }\n", "            method_results.append(result)\n", "\n", "        results[name] = method_results\n", "\n", "    return results\n", "\n", "# Display the results in a human-readable format\n", "def display_results(results):\n", "    for query_idx, query in enumerate([r[\"query\"] for r in results[\"BM25\"]]):\n", "        print(f\"\\n\\nQuery: {query}\")\n", "        print(\"-\" * 50)\n", "\n", "        # Show what each method found\n", "        for method in results:\n", "            titles = results[method][query_idx][\"titles\"]\n", "            print(f\"{method} found: {', '.join(titles)}\")\n", "\n", "        # Calculate and display overlap and differences\n", "        bm25_titles = set(results[\"BM25\"][query_idx][\"titles\"])\n", "        vector_titles = set(results[\"Vector\"][query_idx][\"titles\"])\n", "        overlap = bm25_titles.intersection(vector_titles)\n", "\n", "        print(\n", "            f\"Overlap: {len(overlap)} documents ({', '.join(overlap) if overlap else 'None'})\")\n", "        print(\n", "            f\"Unique to BM25: {', '.join(bm25_titles - vector_titles) if bm25_titles - vector_titles else 'None'}\")\n", "        print(\n", "            f\"Unique to Vector: {', '.join(vector_titles - bm25_titles) if vector_titles - bm25_titles else 'None'}\")\n", "\n", "\n", "# Run comparison tests\n", "queries = [\n", "    \"What is machine learning?\",\n", "    \"transformer architecture\",\n", "    \"python neural network code\",\n", "    \"backpropagation algorithm\",\n", "    \"information retrieval\"\n", "]\n", "\n", "retrievers = create_retrievers(documents)\n", "results = compare_retrievers(retrievers, queries)\n", "display_results(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Key observations:\n", "\n", "1. **BM25 performs better on keyword-heavy and technical queries**\n", "   - When exact terminology matters (e.g., \"backpropagation algorithm\")\n", "   - Less susceptible to semantic drift\n", "\n", "2. **Vector search performs better on semantic queries**\n", "   - Better at understanding meaning beyond the exact keywords\n", "   - Can find related concepts even when terminology differs\n", "\n", "3. **The overlap between results is often surprisingly small**\n", "   - Different retrieval methods often return different documents\n", "   - This suggests they're complementary rather than redundant\n", "\n", "4. **This suggests that combining both methods could yield better results**\n", "   - Hybrid approaches can leverage the strengths of both methods\n", "   - Many production systems use both approaches in tandem"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've explored two fundamental retrieval approaches:\n", "- BM25 (lexical search) which works with keywords and term frequencies\n", "- Vector search which uses embeddings to capture semantic meaning\n", "\n", "Each method has its strengths and weaknesses. BM25 excels at finding exact matches and specific terminology, while vector search is better at understanding the meaning behind queries. \n", "\n", "For production systems, consider using a hybrid approach that combines the best of both worlds."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}