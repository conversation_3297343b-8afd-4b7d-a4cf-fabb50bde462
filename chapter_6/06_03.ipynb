{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hybrid Retrieval: Combining BM25 and Vector Search\n", "\n", "- **BM25**: Excels at finding exact keyword matches but misses semantic relationships\n", "- **Vector Search**: Captures semantic meaning but may miss exact term matches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Hybrid Retrieval Concepts\n", "\n", "### 1. Fusion Approaches\n", "Fusion approaches retrieve documents separately with each method, then combine the results.\n", "\n", "- **Simple fusion**: Taking the union of results from multiple retrievers\n", "- **Weighted fusion**: Adjusting scores from each retriever and combining them\n", "- **Reciprocal rank fusion**: Considering the rank position of documents in each result set\n", "\n", "### 2. Ensemble Approaches\n", "Ensemble approaches use multiple retrievers in series or with more complex logic.\n", "\n", "- **Sequential retrieval**: Using one retriever's output as input to another\n", "- **Filter-then-rank**: Using one method to create a candidate pool, another to rank\n", "\n", "In this notebook, we'll implement and evaluate different hybrid retrieval strategies."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Dependencies\n", "First, let's install the necessary dependencies."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2mUsing Python 3.12.10 environment at: /workspaces/fundamentals-of-ai-engineering-principles-and-practical-applications-6026542/.venv\u001b[0m\n", "\u001b[2mAudited \u001b[1m1 package\u001b[0m \u001b[2min 11ms\u001b[0m\u001b[0m\n"]}], "source": ["# This installs the embeddings, it can take a while to install so I didn't include in the the default requirements.txt\n", "!uv pip install llama-index-embeddings-huggingface"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports\n", "\n", "First, let's import the necessary libraries:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import pandas as pd\n", "from llama_index.core import Document, VectorStoreIndex\n", "from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.retrievers.bm25 import BM25Retriever\n", "from llama_index.core.schema import QueryBundle, NodeWithScore\n", "from llama_index.core.retrievers import BaseRetriever\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Sample Documents\n", "\n", "Let's create a collection of AI-related documents to test our retrieval methods:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Create sample AI-related documents\n", "documents = [\n", "    Document(text=\"Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\"),\n", "    Document(text=\"BM25, or Best Match 25, also known as Okapi BM25, is a ranking algorithm for information retrieval and search engines that determines a document's relevance to a given query and ranks documents based on their relevance scores.\"),\n", "    Document(text=\"Neural networks are computational models inspired by the human brain. They consist of layers of interconnected nodes or 'neurons' that process and transform input data to produce meaningful outputs.\"),\n", "    Document(text=\"Transformers are a type of deep learning architecture introduced in the paper 'Attention is All You Need'. They have revolutionized natural language processing tasks such as translation, summarization, and question answering.\"),\n", "    Document(text=\"Backpropagation is a key algorithm for training neural networks. It calculates the gradient of the loss function with respect to the network weights, allowing for efficient optimization.\"),\n", "    Document(text=\"Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\"),\n", "    Document(text=\"Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize a reward signal. It has been used to achieve superhuman performance in games like chess, Go, and various Atari games.\"),\n", "    Document(text=\"Large Language Models (LLMs) like GPT-4 and <PERSON> are transformer-based models trained on vast amounts of text data. They can generate human-like text, answer questions, and perform a variety of language tasks.\"),\n", "    Document(text=\"Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\"),\n", "    Document(text=\"Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\"),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building Individual Retrievers\n", "\n", "Now, let's create both BM25 and Vector retrievers that we'll use as components of our hybrid approach:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create the individual BM25 and Vector retrievers\n", "def create_retrievers(documents, top_k=5):\n", "    \"\"\"Create BM25 and Vector retrievers from documents.\n", "    \n", "    Args:\n", "        documents: List of Document objects\n", "        top_k: Number of results to return\n", "        \n", "    Returns:\n", "        Dictionary containing BM25 and Vector retrievers\n", "    \"\"\"\n", "    # First, parse documents into nodes (chunks)\n", "    parser = SentenceSplitter(chunk_size=2000, chunk_overlap=0)\n", "    nodes = parser.get_nodes_from_documents(documents)\n", "    \n", "    # Create BM25 retriever (lexical search)\n", "    bm25_retriever = BM25Retriever.from_defaults(nodes=nodes, similarity_top_k=top_k)\n", "    \n", "    # Create Vector retriever (semantic search)\n", "    # First load the embedding model\n", "    embed_model = HuggingFaceEmbedding(model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n", "    vector_index = VectorStoreIndex(nodes, embed_model=embed_model)\n", "    vector_retriever = vector_index.as_retriever(similarity_top_k=top_k)\n", "\n", "    # Return both retrievers in a dictionary\n", "    return {\n", "        \"BM25\": bm25_retriever,\n", "        \"Vector\": vector_retriever,\n", "        \"nodes\": nodes  # Also return nodes for reference\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Fusion Retriever\n", "\n", "A simple fusion retriever that combines the results from both BM25 and vector search without any complex re-ranking. This approach:\n", "\n", "1. Runs both retrievers independently\n", "2. Takes the union of their results\n", "3. <PERSON><PERSON>ves duplicate documents\n", "\n", "This is the most straightforward hybrid approach but can be very effective."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Create a simple hybrid retriever\n", "class SimpleFusionRetriever(BaseRetriever):\n", "    \"\"\"Simple fusion retriever that combines results from multiple retrievers.\n", "    \n", "    This retriever runs multiple retrieval methods independently and takes the union\n", "    of their results, removing duplicates.\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        retrievers: dict,\n", "    ):\n", "        \"\"\"Initialize with retrievers dictionary.\"\"\"\n", "        self.retrievers = retrievers\n", "        super().__init__()\n", "\n", "    def _retrieve(self, query_bundle: QueryBundle) -> list[NodeWithScore]:\n", "        \"\"\"Retrieve nodes given query.\"\"\"\n", "        # Dictionary to store unique nodes by ID to remove duplicates\n", "        all_nodes = {}\n", "\n", "        # Run each retriever and collect results\n", "        for name, retriever in self.retrievers.items():\n", "            results = retriever.retrieve(query_bundle)\n", "\n", "            # Add to results dictionary (keyed by node_id for deduplication)\n", "            for node in results:\n", "                all_nodes[node.node.node_id] = node\n", "                \n", "        # Return all unique nodes\n", "        return list(all_nodes.values())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing Our Retrievers\n", "\n", "Now, let's create instances of each retriever and see how they compare on a sample query. We'll format the results to make them easier to read and analyze."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: algorithm\n", "==============================\n", "\n", "BM25 Retriever:\n", "1. [Score: 0.5282] Backpropagation is a key algorithm for training neural networks. It calculates the gradient of the loss function with respect to the network weights, allowing for efficient optimization.\n", "2. [Score: 0.4553] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "3. [Score: 0.4465] BM25, or Best Match 25, also known as Okapi BM25, is a ranking algorithm for information retrieval and search engines that determines a document's relevance to a given query and ranks documents based on their relevance scores.\n", "4. [Score: 0.0000] Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\n", "5. [Score: 0.0000] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "\n", "Vector Retriever:\n", "1. [Score: 0.3135] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "2. [Score: 0.2828] BM25, or Best Match 25, also known as Okapi BM25, is a ranking algorithm for information retrieval and search engines that determines a document's relevance to a given query and ranks documents based on their relevance scores.\n", "3. [Score: 0.2613] Backpropagation is a key algorithm for training neural networks. It calculates the gradient of the loss function with respect to the network weights, allowing for efficient optimization.\n", "4. [Score: 0.2085] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "5. [Score: 0.1984] Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\n", "\n", "Simple Fusion Retriever:\n", "1. [Score: 0.2613] Backpropagation is a key algorithm for training neural networks. It calculates the gradient of the loss function with respect to the network weights, allowing for efficient optimization.\n", "2. [Score: 0.3135] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "3. [Score: 0.2828] BM25, or Best Match 25, also known as Okapi BM25, is a ranking algorithm for information retrieval and search engines that determines a document's relevance to a given query and ranks documents based on their relevance scores.\n", "4. [Score: 0.0000] Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\n", "5. [Score: 0.2085] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "6. [Score: 0.1984] Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\n"]}], "source": ["# Create individual retrievers\n", "retrievers_dict = create_retrievers(documents, top_k=5)\n", "\n", "# Create simple fusion retriever\n", "hybrid_retriever = SimpleFusionRetriever(\n", "    retrievers={k: v for k, v in retrievers_dict.items() if k != \"nodes\"}\n", ")\n", "\n", "# Format results helper function\n", "def format_results(results, name):\n", "    \"\"\"Format retrieval results for display in a clean, readable format.\"\"\"\n", "    lines = [f\"\\n{name}:\"]\n", "\n", "    for i, node in enumerate(results):\n", "        # Include result number, score, and first 130 chars of text on one line\n", "        text = node.node.text\n", "        summary = text\n", "\n", "        # Format with clear separation between result number and content\n", "        lines.append(f\"{i+1}. [Score: {node.score:.4f}] {summary}\")\n", "\n", "    return \"\\n\".join(lines)\n", "\n", "# Test the retrievers\n", "query_text = \"algorithm\"\n", "query_bundle = QueryBundle(query_text)\n", "\n", "print(f\"Query: {query_text}\")\n", "print(\"=\" * 30)\n", "\n", "# Get results from each retriever\n", "bm25_results = retrievers_dict[\"BM25\"].retrieve(query_bundle)\n", "vector_results = retrievers_dict[\"Vector\"].retrieve(query_bundle)\n", "hybrid_results = hybrid_retriever.retrieve(query_bundle)\n", "\n", "# Print results\n", "print(format_results(bm25_results, \"BM25 Retriever\"))\n", "print(format_results(vector_results, \"Vector Retriever\"))\n", "print(format_results(hybrid_results, \"Simple Fusion Retriever\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analysis of Simple Fusion Results\n", "\n", "Looking at the results above, we can see:\n", "\n", "1. **BM25 Retriever**: Found documents with the exact term \"algorithm\" with high scores.\n", "2. **Vector Retriever**: Found semantically related documents, even those that may not contain the exact term.\n", "3. **Simple Fusion**: Combined the results from both retrievers, giving us a more comprehensive set of documents.\n", "\n", "However, we can see a limitation: the simple fusion approach doesn't rerank or adjust scores, so the quality of the combined results depends entirely on the individual retrievers' scores, which aren't directly comparable.\n", "\n", "This brings us to our next approach: weighted fusion."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Weighted Fusion Retriever\n", "\n", "The weighted fusion approach improves upon simple fusion by:\n", "\n", "1. Allowing us to assign different weights to each retriever\n", "2. Combining scores in a way that preserves their relative importance\n", "3. Reranking the final results based on the combined scores\n", "\n", "This gives us more control over the balance between lexical and semantic search."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Create a more advanced hybrid retriever with weighted scoring\n", "class WeightedFusionRetriever(BaseRetriever):\n", "    \"\"\"Weighted fusion retriever that combines and rescores results.\n", "    \n", "    This retriever assigns weights to different retrieval methods, then combines \n", "    their scores and reranks the results based on the combined score.\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        retrievers: dict,\n", "        weights: dict\n", "    ):\n", "        \"\"\"Initialize with retrievers and weights.\n", "        \n", "        Args:\n", "            retrievers: Dictionary of retrievers (name -> retriever)\n", "            weights: Dictionary of weights for each retriever (name -> weight)\n", "        \"\"\"\n", "        self.retrievers = retrievers\n", "        self.weights = weights\n", "        super().__init__()\n", "\n", "    def _retrieve(self, query_bundle: QueryBundle) -> list[NodeWithScore]:\n", "        \"\"\"Retrieve nodes with weighted fusion approach.\"\"\"\n", "        # Get results from each retriever\n", "        all_results = {}\n", "\n", "        for name, retriever in self.retrievers.items():\n", "            results = retriever.retrieve(query_bundle)\n", "            weight = self.weights.get(name, 1.0)  # Default weight 1.0\n", "\n", "            for node_with_score in results:\n", "                node_id = node_with_score.node.node_id\n", "\n", "                # Apply weight to score\n", "                weighted_score = node_with_score.score * weight\n", "\n", "                # Store the node and its scores\n", "                if node_id not in all_results:\n", "                    all_results[node_id] = {\n", "                        \"node\": node_with_score.node,\n", "                        \"scores\": {}\n", "                    }\n", "\n", "                all_results[node_id][\"scores\"][name] = weighted_score\n", "\n", "        # Combine scores and create final results\n", "        final_results = []\n", "        for node_id, data in all_results.items():\n", "            # Sum up the scores from different retrievers\n", "            combined_score = sum(data[\"scores\"].values())\n", "\n", "            # Create NodeWithScore object\n", "            node_with_score = NodeWithScore(\n", "                node=data[\"node\"],\n", "                score=combined_score\n", "            )\n", "            final_results.append(node_with_score)\n", "\n", "        # Sort by combined score (descending)\n", "        final_results.sort(key=lambda x: x.score, reverse=True)\n", "\n", "        return final_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing Weighted Fusion with Different Weight Configurations\n", "\n", "Now let's create three versions of our weighted fusion retriever with different weights:\n", "\n", "1. **BM25-Heavy**: Favors lexical matching (80% BM25, 20% Vector)\n", "2. **Vector-Heavy**: Favors semantic understanding (20% BM25, 80% Vector)\n", "3. **Balanced**: Equal weights (50% BM25, 50% Vector)\n", "\n", "We'll test them on a more semantic query to see how the weights affect the results."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Query: how do computers understand images?\n", "================================================================================\n", "\n", "BM25-Heavy Weighted Fusion:\n", "1. [Score: 2.9622] Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\n", "2. [Score: 0.5407] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "3. [Score: 0.4552] Neural networks are computational models inspired by the human brain. They consist of layers of interconnected nodes or 'neurons' that process and transform input data to produce meaningful outputs.\n", "4. [Score: 0.4291] Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\n", "5. [Score: 0.0519] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "6. [Score: 0.0000] Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize a reward signal. It has been used to achieve superhuman performance in games like chess, Go, and various Atari games.\n", "\n", "Vector-Heavy Weighted Fusion:\n", "1. [Score: 1.1955] Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\n", "2. [Score: 0.3600] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "3. [Score: 0.3506] Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\n", "4. [Score: 0.3377] Neural networks are computational models inspired by the human brain. They consist of layers of interconnected nodes or 'neurons' that process and transform input data to produce meaningful outputs.\n", "5. [Score: 0.2074] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "6. [Score: 0.0000] Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize a reward signal. It has been used to achieve superhuman performance in games like chess, Go, and various Atari games.\n", "\n", "Balanced Weighted Fusion:\n", "1. [Score: 2.0788] Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It aims to automate tasks that the human visual system can do.\n", "2. [Score: 0.4504] Natural Language Processing (NLP) encompasses techniques for understanding, interpreting and generating human language. Modern NLP systems use transformer architectures to process and generate text with remarkable accuracy.\n", "3. [Score: 0.3964] Neural networks are computational models inspired by the human brain. They consist of layers of interconnected nodes or 'neurons' that process and transform input data to produce meaningful outputs.\n", "4. [Score: 0.3899] Deep learning is a subset of machine learning that uses multi-layered neural networks to learn from data. It has achieved breakthroughs in computer vision, speech recognition, and natural language processing.\n", "5. [Score: 0.1296] Machine learning is a subset of artificial intelligence that involves building systems that can learn from data. Common machine learning algorithms include linear regression, decision trees, and neural networks.\n", "6. [Score: 0.0000] Reinforcement learning is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize a reward signal. It has been used to achieve superhuman performance in games like chess, Go, and various Atari games.\n"]}], "source": ["# Test the weighted fusion retriever with different weights\n", "# Create weighted fusion retrievers with different weight configurations\n", "bm25_heavy = WeightedFusionRetriever(\n", "    retrievers={k: v for k, v in retrievers_dict.items() if k != \"nodes\"},\n", "    weights={\"BM25\": 0.8, \"Vector\": 0.2}  # BM25 heavy\n", ")\n", "\n", "vector_heavy = WeightedFusionRetriever(\n", "    retrievers={k: v for k, v in retrievers_dict.items() if k != \"nodes\"},\n", "    weights={\"BM25\": 0.2, \"Vector\": 0.8}  # Vector heavy\n", ")\n", "\n", "balanced = WeightedFusionRetriever(\n", "    retrievers={k: v for k, v in retrievers_dict.items() if k != \"nodes\"},\n", "    weights={\"BM25\": 0.5, \"Vector\": 0.5}  # Balanced weights\n", ")\n", "\n", "# Test with a semantic query\n", "semantic_query = \"how do computers understand images?\"\n", "semantic_bundle = QueryBundle(semantic_query)\n", "\n", "print(f\"\\n\\nQuery: {semantic_query}\")\n", "print(\"=\" * 80)\n", "\n", "# Get results from weighted retrievers\n", "bm25_heavy_results = bm25_heavy.retrieve(semantic_bundle)\n", "vector_heavy_results = vector_heavy.retrieve(semantic_bundle)\n", "balanced_results = balanced.retrieve(semantic_bundle)\n", "\n", "# Print results\n", "print(format_results(bm25_heavy_results, \"BM25-Heavy Weighted Fusion\"))\n", "print(format_results(vector_heavy_results, \"Vector-Heavy Weighted Fusion\"))\n", "print(format_results(balanced_results, \"Balanced Weighted Fusion\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analysis of Weighted Fusion Results\n", "\n", "In this example, we used a more natural language query: \"how do computers understand images?\"\n", "\n", "Observations:\n", "1. All three approaches correctly identified the computer vision document as most relevant.\n", "2. The **BM25-Heavy** approach gave a much higher score (2.96) to the top document compared to the **Vector-Heavy** approach (1.20).\n", "3. The **Balanced** approach shows a good compromise (2.08) between the two.\n", "4. The ranking of secondary documents (2-5) is slightly different across the approaches.\n", "\n", "This demonstrates how adjusting weights lets us control the balance between exact keyword matching and semantic understanding in our retrieval system. The best configuration depends on the types of queries your application typically receives."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comprehensive Comparison Across Different Query Types\n", "\n", "Now, let's compare all our retrievers across a variety of query types to better understand their strengths and weaknesses:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RETRIEVER COMPARISON ACROSS QUERY TYPES\n", "================================================================================\n", "\n", "QUERY: \"CV libs\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Deep learning is a subset of machine learning t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Large Language Models (LLMs) like GPT-4 and Cla...\n", "\n", "Vector Results:\n", "  1. Large Language Models (LLMs) like GPT-4 and Cla...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Fusion Results:\n", "  1. Deep learning is a subset of machine learning t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Large Language Models (LLMs) like GPT-4 and Cla...\n", "\n", "BM25-Heavy Results:\n", "  1. Large Language Models (LLMs) like GPT-4 and Cla...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Vector-Heavy Results:\n", "  1. Large Language Models (LLMs) like GPT-4 and Cla...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "================================================================================\n", "QUERY: \"neural networks backpropagation\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Backpropagation is a key algorithm for training...\n", "  2. Neural networks are computational models inspir...\n", "  3. Machine learning is a subset of artificial inte...\n", "\n", "Vector Results:\n", "  1. Backpropagation is a key algorithm for training...\n", "  2. Neural networks are computational models inspir...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Fusion Results:\n", "  1. Backpropagation is a key algorithm for training...\n", "  2. Neural networks are computational models inspir...\n", "  3. Machine learning is a subset of artificial inte...\n", "\n", "BM25-Heavy Results:\n", "  1. Backpropagation is a key algorithm for training...\n", "  2. Neural networks are computational models inspir...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Vector-Heavy Results:\n", "  1. Backpropagation is a key algorithm for training...\n", "  2. Neural networks are computational models inspir...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "================================================================================\n", "QUERY: \"how do computers understand images?\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Computer vision is an interdisciplinary field t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Neural networks are computational models inspir...\n", "\n", "Vector Results:\n", "  1. Computer vision is an interdisciplinary field t...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Natural Language Processing (NLP) encompasses t...\n", "\n", "Fusion Results:\n", "  1. Computer vision is an interdisciplinary field t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Neural networks are computational models inspir...\n", "\n", "BM25-Heavy Results:\n", "  1. Computer vision is an interdisciplinary field t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Neural networks are computational models inspir...\n", "\n", "Vector-Heavy Results:\n", "  1. Computer vision is an interdisciplinary field t...\n", "  2. Natural Language Processing (NLP) encompasses t...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "================================================================================\n", "QUERY: \"natural language processing\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Natural Language Processing (NLP) encompasses t...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Vector Results:\n", "  1. Natural Language Processing (NLP) encompasses t...\n", "  2. Large Language Models (LLMs) like GPT-4 and Cla...\n", "  3. Transformers are a type of deep learning archit...\n", "\n", "Fusion Results:\n", "  1. Natural Language Processing (NLP) encompasses t...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "BM25-Heavy Results:\n", "  1. Natural Language Processing (NLP) encompasses t...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Vector-Heavy Results:\n", "  1. Natural Language Processing (NLP) encompasses t...\n", "  2. Transformers are a type of deep learning archit...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "================================================================================\n", "QUERY: \"machine learning applications\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Machine learning is a subset of artificial inte...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Reinforcement learning is a type of machine lea...\n", "\n", "Vector Results:\n", "  1. Machine learning is a subset of artificial inte...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Computer vision is an interdisciplinary field t...\n", "\n", "Fusion Results:\n", "  1. Machine learning is a subset of artificial inte...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Reinforcement learning is a type of machine lea...\n", "\n", "BM25-Heavy Results:\n", "  1. Machine learning is a subset of artificial inte...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Reinforcement learning is a type of machine lea...\n", "\n", "Vector-Heavy Results:\n", "  1. Machine learning is a subset of artificial inte...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Reinforcement learning is a type of machine lea...\n", "\n", "================================================================================\n", "QUERY: \"reinforcement learning in games\"\n", "--------------------------------------------------------------------------------\n", "\n", "BM25 Results:\n", "  1. Reinforcement learning is a type of machine lea...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Machine learning is a subset of artificial inte...\n", "\n", "Vector Results:\n", "  1. Reinforcement learning is a type of machine lea...\n", "  2. Machine learning is a subset of artificial inte...\n", "  3. Backpropagation is a key algorithm for training...\n", "\n", "Fusion Results:\n", "  1. Reinforcement learning is a type of machine lea...\n", "  2. Deep learning is a subset of machine learning t...\n", "  3. Machine learning is a subset of artificial inte...\n", "\n", "BM25-Heavy Results:\n", "  1. Reinforcement learning is a type of machine lea...\n", "  2. Machine learning is a subset of artificial inte...\n", "  3. Deep learning is a subset of machine learning t...\n", "\n", "Vector-Heavy Results:\n", "  1. Reinforcement learning is a type of machine lea...\n", "  2. Machine learning is a subset of artificial inte...\n", "  3. Backpropagation is a key algorithm for training...\n", "\n", "================================================================================\n"]}], "source": ["# Compare retriever performance across different query types\n", "# Function to compare retrievers\n", "def compare_retrievers(retrievers, queries):\n", "    \"\"\"Compare multiple retrievers across different query types with clearer output.\n", "    \n", "    Args:\n", "        retrievers: Dictionary of retrievers to compare\n", "        queries: List of query strings to test\n", "        \n", "    Returns:\n", "        Formatted string with comparison results\n", "    \"\"\"\n", "    # Set up the results container\n", "    results = []\n", "\n", "    # Process each query\n", "    for query in queries:\n", "        query_bundle = QueryBundle(query)\n", "        query_results = {}\n", "\n", "        # For each retriever, get top results\n", "        for name, retriever in retrievers.items():\n", "            retrieved = retriever.retrieve(query_bundle)\n", "            # Get first sentence of each result for compact display\n", "            top_results = []\n", "            # Limit to top 3 for clarity\n", "            for i, node in enumerate(retrieved[:3]):\n", "                first_sentence = node.node.text.split('.')[0].strip()\n", "                # Truncate if too long\n", "                if len(first_sentence) > 50:\n", "                    first_sentence = first_sentence[:47] + \"...\"\n", "                top_results.append(f\"{i+1}. {first_sentence}\")\n", "\n", "            query_results[name] = top_results\n", "\n", "        results.append((query, query_results))\n", "\n", "    # Format the output as readable text\n", "    output = [\"RETRIEVER COMPARISON ACROSS QUERY TYPES\", \"=\" * 80, \"\"]\n", "\n", "    # For each query\n", "    for query, query_results in results:\n", "        output.append(f\"QUERY: \\\"{query}\\\"\")\n", "        output.append(\"-\" * 80)\n", "\n", "        # For each retriever\n", "        for retriever_name, results_list in query_results.items():\n", "            output.append(f\"\\n{retriever_name} Results:\")\n", "            if not results_list:\n", "                output.append(\"  No results found\")\n", "            else:\n", "                for result in results_list:\n", "                    output.append(f\"  {result}\")\n", "\n", "        output.append(\"\\n\" + \"=\" * 80)\n", "\n", "    return \"\\n\".join(output)\n", "\n", "\n", "all_retrievers = {\n", "    \"BM25\": retrievers_dict[\"BM25\"],\n", "    \"Vector\": retrievers_dict[\"Vector\"],\n", "    \"Fusion\": hybrid_retriever,\n", "    \"BM25-Heavy\": bm25_heavy,\n", "    \"Vector-Heavy\": vector_heavy\n", "}\n", "\n", "# Test with a variety of queries\n", "test_queries = [\n", "    \"CV libs\",                           # Abbreviation and technical term\n", "    \"neural networks backpropagation\",   # Technical keyword search\n", "    \"how do computers understand images?\",  # Natural language question\n", "    \"natural language processing\",       # Direct field search\n", "    \"machine learning applications\",     # General topic search\n", "    \"reinforcement learning in games\"    # Specific application search\n", "]\n", "\n", "# Usage remains the same\n", "comparison_text = compare_retrievers(all_retrievers, test_queries)\n", "print(comparison_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Findings and Practical Tips\n", "\n", "Based on our experiments, we can draw several conclusions and offer practical advice for implementing hybrid retrievers in real-world applications:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Practical tips for implementing hybrid retrievers:\n", "\n", "1. **Choosing weights**: Start with equal weights and adjust based on the query types you see in your application. If users frequently use keywords, increase BM25 weight; if they ask natural language questions, favor vector search.\n", "\n", "2. **Performance considerations**: The fusion approach requires running both retrievers, which can be more expensive. In production, consider:\n", "\n", "    - Running retrievers in parallel\n", "    - Using a faster first-stage retriever (typically BM25) to filter candidates\n", "    - Caching results for common queries\n", "\n", "3. **Beyond simple weighting**: More sophisticated approaches include:\n", "\n", "    - Using machine learning to learn optimal weights\n", "    - Adjusting weights dynamically based on query type\n", "    - Implementing reciprocal rank fusion, which considers result positions\n", "\n", "4. **Evaluation**: Always evaluate your hybrid approach on a diverse set of queries to ensure it's performing better than either method alone."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've explored different approaches to hybrid retrieval, combining the strengths of BM25 lexical search and vector-based semantic search.\n", "\n", "Key takeaways:\n", "- Simple fusion provides an easy way to combine results but doesn't rerank them\n", "- Weighted fusion offers more control over the balance between approaches\n", "- The best configuration depends on your specific use case and query patterns\n", "- Hybrid approaches generally outperform either method alone across diverse query types\n", "\n", "By implementing hybrid retrieval, you can create more robust information retrieval systems that work well for both keyword searches and natural language queries."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}