{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhancing Retrieval with Reranking\n", "\n", "This notebook demonstrates the concept of reranking in information retrieval systems. We'll build a basic search system from scratch and show how reranking can improve search quality.\n", "\n", "## What is <PERSON><PERSON><PERSON>?\n", "\n", "Reranking is a two-stage retrieval approach:\n", "\n", "1. **Initial Retrieval**: Use efficient methods to get a candidate set of potentially relevant documents\n", "2. **Reranking**: Apply a more sophisticated model to reorder these candidates by relevance\n", "\n", "This approach combines the efficiency of simple retrievers with the accuracy of more complex models."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding the Key Technologies\n", "\n", "### Bi-Encoders vs Cross-Encoders\n", "\n", "- **Bi-Encoders**:\n", "  - Encode queries and documents separately\n", "  - Allow pre-computation of document embeddings\n", "  - Fast for initial retrieval across large collections\n", "  - Less accurate as they don't directly compare query-document interactions\n", "\n", "- **Cross-Encoders**:\n", "  - Process query and document pairs together\n", "  - Capture complex interactions between query and document\n", "  - More accurate for relevance assessment\n", "  - Computationally expensive (can't pre-compute)\n", "  - Best used for reranking a small set of candidates"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up Our Environment\n", "\n", "First, let's import the necessary libraries:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from sentence_transformers import CrossEncoder, SentenceTransformer, util\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Sample Data\n", "\n", "Let's create some sample documents to work with for our demonstration:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["documents = [\n", "    \"Python is a high-level, interpreted programming language known for its readability.\",\n", "    \"Machine learning is a subset of artificial intelligence that learns from data.\",\n", "    \"Neural networks are computing systems inspired by biological neural networks.\",\n", "    \"Deep learning uses neural networks with many layers to extract features from data.\",\n", "    \"Natural language processing helps computers understand human language.\",\n", "    \"Python libraries like PyTorch and TensorFlow are used for deep learning.\",\n", "    \"BM25 is a bag-of-words retrieval function used in information retrieval.\",\n", "    \"Vector search finds documents by measuring similarity in embedding space.\",\n", "    \"Reranking refines initial search results with a more complex model.\",\n", "    \"Hybrid search combines multiple retrieval methods to improve search quality.\"\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing a Bi-Encoder Retriever\n", "\n", "First, we'll implement the first stage of our system using a bi-encoder model. This will encode our documents and queries separately, allowing for efficient initial retrieval."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class BiEncoderRetriever:\n", "    \"\"\"Simple implementation of a Bi-Encoder retriever\"\"\"\n", "    \n", "    def __init__(self, model_name=\"sentence-transformers/all-MiniLM-L6-v2\", top_k=5):\n", "        \"\"\"Initialize the retriever with a pre-trained model\"\"\"\n", "        print(f\"Loading bi-encoder model: {model_name}\")\n", "        self.model = SentenceTransformer(model_name, device=\"cpu\")\n", "        self.top_k = top_k\n", "        self.doc_embeddings = None\n", "        self.documents = None\n", "        \n", "    def index_documents(self, documents):\n", "        \"\"\"Generate and store embeddings for all documents\"\"\"\n", "        print(f\"Indexing {len(documents)} documents...\")\n", "        self.documents = documents\n", "        self.doc_embeddings = self.model.encode(documents)\n", "        print(f\"Created embeddings with {self.doc_embeddings.shape[1]} dimensions\")\n", "        \n", "    def retrieve(self, query):\n", "        \"\"\"Retrieve top documents for a given query\"\"\"\n", "        # Encode the query\n", "        query_embedding = self.model.encode(query)\n", "        \n", "        # Calculate similarity scores\n", "        scores = []\n", "        for i, doc_embedding in enumerate(self.doc_embeddings):\n", "            # Compute cosine similarity\n", "            similarity = util.cos_sim(query_embedding, doc_embedding).item()\n", "            scores.append((i, similarity, self.documents[i]))\n", "        \n", "        # Sort by similarity score (descending)\n", "        scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top_k results\n", "        return scores[:self.top_k]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's initialize our bi-encoder retriever and index our documents:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading bi-encoder model: sentence-transformers/all-MiniLM-L6-v2\n", "Indexing 10 documents...\n", "Created embeddings with 384 dimensions\n"]}], "source": ["# Initialize our retriever\n", "retriever = BiEncoderRetriever(top_k=5)\n", "\n", "# Index our documents\n", "retriever.index_documents(documents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's test our bi-encoder retriever with a query:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: 'How is Python used in machine learning?'\n", "\n", "Retrieved 5 documents in 0.0156s\n", "1. Score: 0.6194 - Python is a high-level, interpreted programming language known for its readability.\n", "2. Score: 0.6004 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "3. Score: 0.5793 - Machine learning is a subset of artificial intelligence that learns from data.\n", "4. Score: 0.4139 - Deep learning uses neural networks with many layers to extract features from data.\n", "5. Score: 0.4065 - Natural language processing helps computers understand human language.\n"]}], "source": ["# Define a query\n", "query = \"How is Python used in machine learning?\"\n", "print(f\"Query: '{query}'\\n\")\n", "\n", "# Perform retrieval\n", "start_time = time.time()\n", "bi_encoder_results = retriever.retrieve(query)\n", "retrieval_time = time.time() - start_time\n", "\n", "# Display results\n", "print(f\"Retrieved {len(bi_encoder_results)} documents in {retrieval_time:.4f}s\")\n", "for i, (doc_id, score, doc) in enumerate(bi_encoder_results, 1):\n", "    print(f\"{i}. Score: {score:.4f} - {doc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing a Cross-Encoder Reranker\n", "\n", "Now, let's implement the second stage of our system using a cross-encoder model. This will process query-document pairs together to provide more accurate relevance scores."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class CrossEncoderReranker:\n", "    \"\"\"Simple implementation of a Cross-Encoder reranker\"\"\"\n", "    \n", "    def __init__(self, model_name=\"cross-encoder/ms-marco-MiniLM-L-6-v2\", top_k=3):\n", "        \"\"\"Initialize with a pre-trained cross-encoder model\"\"\"\n", "        print(f\"Loading cross-encoder model: {model_name}\")\n", "        self.model = CrossEncoder(model_name, device=\"cpu\")\n", "        self.top_k = top_k\n", "        \n", "    def rerank(self, query, results):\n", "        \"\"\"Rerank results using cross-encoder model\"\"\"\n", "        if not results:\n", "            return []\n", "        \n", "        # Create query-document pairs\n", "        query_doc_pairs = [(query, doc) for _, _, doc in results]\n", "        \n", "        # Get scores from cross-encoder\n", "        rerank_scores = self.model.predict(query_doc_pairs)\n", "        \n", "        # Combine with original results\n", "        reranked = [(results[i][0], float(score), results[i][2]) \n", "                   for i, score in enumerate(rerank_scores)]\n", "        \n", "        # Sort by new scores (descending)\n", "        reranked.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top_k results\n", "        return reranked[:self.top_k]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's initialize our cross-encoder reranker:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading cross-encoder model: cross-encoder/ms-marco-MiniLM-L-6-v2\n"]}], "source": ["# Initialize our reranker\n", "reranker = CrossEncoderReranker(top_k=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Applying Reranking to Our Results\n", "\n", "Let's apply the cross-encoder reranker to the initial results from the bi-encoder retriever:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: 'How is Python used in machine learning?'\n", "\n", "Reranked 3 documents in 0.1398s\n", "1. Score: 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.4332 - Python is a high-level, interpreted programming language known for its readability.\n", "3. Score: -2.5431 - Machine learning is a subset of artificial intelligence that learns from data.\n"]}], "source": ["# Using the same query from before\n", "print(f\"Query: '{query}'\\n\")\n", "\n", "# Apply reranking\n", "start_time = time.time()\n", "reranked_results = reranker.rerank(query, bi_encoder_results)\n", "rerank_time = time.time() - start_time\n", "\n", "# Display results\n", "print(f\"Reranked {len(reranked_results)} documents in {rerank_time:.4f}s\")\n", "for i, (doc_id, score, doc) in enumerate(reranked_results, 1):\n", "    print(f\"{i}. Score: {score:.4f} - {doc}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparison: Bi-Encoder vs Cross-Encoder Results\n", "\n", "Let's compare how the rankings changed after applying the cross-encoder reranker:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BEFORE RERANKING (BI-ENCODER)\n", "1. Score: 0.6194 - Python is a high-level, interpreted programming language known for its readability.\n", "2. Score: 0.6004 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "3. Score: 0.5793 - Machine learning is a subset of artificial intelligence that learns from data.\n", "\n", "AFTER RERANKING (CROSS-ENCODER)\n", "1. Score: 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.4332 - Python is a high-level, interpreted programming language known for its readability.\n", "3. Score: -2.5431 - Machine learning is a subset of artificial intelligence that learns from data.\n", "\n", "CHANGES IN RANKING:\n", "Document moved from position 2 to 1\n", "Document moved from position 1 to 2\n"]}], "source": ["print(\"BEFORE RERANKING (BI-ENCODER)\")\n", "for i, (doc_id, score, doc) in enumerate(bi_encoder_results[:3], 1):\n", "    print(f\"{i}. Score: {score:.4f} - {doc}\")\n", "\n", "print(\"\\nAFTER RERANKING (CROSS-ENCODER)\")\n", "for i, (doc_id, score, doc) in enumerate(reranked_results, 1):\n", "    print(f\"{i}. Score: {score:.4f} - {doc}\")\n", "\n", "# Analyze changes in ranking\n", "print(\"\\nCHANGES IN RANKING:\")\n", "initial_top_docs = [doc for _, _, doc in bi_encoder_results[:3]]\n", "reranked_top_docs = [doc for _, _, doc in reranked_results]\n", "\n", "for i, doc in enumerate(reranked_top_docs, 1):\n", "    if doc in initial_top_docs:\n", "        old_rank = initial_top_docs.index(doc) + 1\n", "        if old_rank != i:\n", "            print(f\"Document moved from position {old_rank} to {i}\")\n", "    else:\n", "        print(f\"New document at position {i} (wasn't in top 3 before)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deep Dive: Why Cross-Encoders Are More Accurate\n", "\n", "Let's directly compare how bi-encoders and cross-encoders score the same document:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: 'How is Python used in machine learning?'\n", "Document: 'Python libraries like PyTorch and TensorFlow are used for deep learning.'\n", "\n", "BI-ENCODER\n", "Similarity score: 0.6004\n", "The bi-encoder encodes query and document separately,\n", "then calculates similarity between these independent representations.\n", "\n", "CROSS-ENCODER\n", "Relevance score: 5.1547\n", "The cross-encoder processes the query and document together,\n", "allowing it to capture complex interactions between terms.\n"]}], "source": ["# Select a document for our comparison\n", "test_doc = \"Python libraries like PyTorch and TensorFlow are used for deep learning.\"\n", "print(f\"Query: '{query}'\")\n", "print(f\"Document: '{test_doc}'\\n\")\n", "\n", "# Calculate bi-encoder similarity\n", "query_emb = retriever.model.encode(query)\n", "doc_emb = retriever.model.encode(test_doc)\n", "bi_sim = util.cos_sim(query_emb, doc_emb).item()\n", "\n", "# Get cross-encoder score\n", "cross_score = reranker.model.predict([(query, test_doc)])\n", "\n", "print(\"BI-ENCODER\")\n", "print(f\"Similarity score: {bi_sim:.4f}\")\n", "print(\"The bi-encoder encodes query and document separately,\")\n", "print(\"then calculates similarity between these independent representations.\\n\")\n", "\n", "print(\"CROSS-ENCODER\")\n", "print(f\"Relevance score: {float(cross_score[0]):.4f}\")\n", "print(\"The cross-encoder processes the query and document together,\")\n", "print(\"allowing it to capture complex interactions between terms.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance Comparison\n", "\n", "Let's measure the performance difference between the two approaches:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PERFORMANCE COMPARISON\n", "Bi-encoder retrieval time: 0.0113s\n", "Full pipeline (retrieval + reranking) time: 0.0375s\n", "Reranking overhead: 0.0262s\n", "Percentage increase: 231.5%\n"]}], "source": ["# Define a function to time retrieval operations\n", "def time_retrieval(retriever_func, n_runs=10):\n", "    times = []\n", "    for _ in range(n_runs):\n", "        start = time.time()\n", "        _ = retriever_func()\n", "        times.append(time.time() - start)\n", "\n", "    # Calculate mean without numpy\n", "    mean_time = sum(times) / len(times)\n", "    return mean_time\n", "\n", "# Time bi-encoder retrieval\n", "bi_encoder_time = time_retrieval(lambda: retriever.retrieve(query))\n", "\n", "# Time cross-encoder reranking (on top of bi-encoder)\n", "def full_retrieval():\n", "    initial_results = retriever.retrieve(query)\n", "    _ = reranker.rerank(query, initial_results)\n", "    \n", "full_pipeline_time = time_retrieval(full_retrieval)\n", "reranking_overhead = full_pipeline_time - bi_encoder_time\n", "\n", "print(\"PERFORMANCE COMPARISON\")\n", "print(f\"Bi-encoder retrieval time: {bi_encoder_time:.4f}s\")\n", "print(f\"Full pipeline (retrieval + reranking) time: {full_pipeline_time:.4f}s\")\n", "print(f\"Reranking overhead: {reranking_overhead:.4f}s\")\n", "print(f\"Percentage increase: {(reranking_overhead/bi_encoder_time)*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What if we used only Cross-Encoders?\n", "\n", "Let's simulate what would happen if we tried to use cross-encoders for the initial retrieval on all documents:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CROSS-<PERSON><PERSON><PERSON>ER ONLY APPROACH\n", "Retrieved 3 documents in 0.0796s\n", "1. Score: 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.4332 - Python is a high-level, interpreted programming language known for its readability.\n", "3. Score: -2.5431 - Machine learning is a subset of artificial intelligence that learns from data.\n", "\n", "PERFORMANCE COMPARISON\n", "Bi-encoder retrieval time: 0.0113s\n", "Two-stage pipeline time: 0.0375s\n", "Cross-encoder only time: 0.0796s\n", "Cross-encoder is 7.0x slower than bi-encoder\n", "\n", "SCALING ANALYSIS\n", "Estimated retrieval times for different collection sizes:\n", "Doc Count | Bi-encoder | Cross-encoder | Speedup\n", "-------------------------------------------------------\n", "     100 | 0.1132s     | 0.7958s      | 7.0x\n", "    1000 | 1.1317s     | 7.9581s      | 7.0x\n", "   10000 | 11.3173s     | 79.5805s      | 7.0x\n", "  100000 | 113.1728s     | 795.8055s      | 7.0x\n"]}], "source": ["# Function to retrieve using only cross-encoder (brute force)\n", "def cross_encoder_only_retrieval(query, documents, top_k=3):\n", "    # Create query-document pairs for all documents\n", "    query_doc_pairs = [(query, doc) for doc in documents]\n", "    \n", "    # Get scores from cross-encoder\n", "    start_time = time.time()\n", "    scores = reranker.model.predict(query_doc_pairs)\n", "    retrieval_time = time.time() - start_time\n", "    \n", "    # Combine with documents\n", "    results = [(i, float(score), doc) \n", "              for i, (score, doc) in enumerate(zip(scores, documents))]\n", "    \n", "    # Sort by scores (descending)\n", "    results.sort(key=lambda x: x[1], reverse=True)\n", "    \n", "    return results[:top_k], retrieval_time\n", "\n", "# Run cross-encoder-only retrieval\n", "cross_only_results, cross_only_time = cross_encoder_only_retrieval(query, documents)\n", "\n", "print(\"CROSS-ENCODER ONLY APPROACH\")\n", "print(f\"Retrieved {len(cross_only_results)} documents in {cross_only_time:.4f}s\")\n", "for i, (doc_id, score, doc) in enumerate(cross_only_results, 1):\n", "    print(f\"{i}. Score: {score:.4f} - {doc}\")\n", "\n", "print(\"\\nPERFORMANCE COMPARISON\")\n", "print(f\"Bi-encoder retrieval time: {bi_encoder_time:.4f}s\")\n", "print(f\"Two-stage pipeline time: {full_pipeline_time:.4f}s\")\n", "print(f\"Cross-encoder only time: {cross_only_time:.4f}s\")\n", "print(f\"Cross-encoder is {cross_only_time/bi_encoder_time:.1f}x slower than bi-encoder\")\n", "\n", "# Calculate how this would scale\n", "print(\"\\nSCALING ANALYSIS\")\n", "doc_counts = [100, 1000, 10000, 100000]\n", "print(\"Estimated retrieval times for different collection sizes:\")\n", "print(\"Doc Count | Bi-encoder | Cross-encoder | Speedup\")\n", "print(\"-\" * 55)\n", "for count in doc_counts:\n", "    # Assuming linear scaling with document count (simplified)\n", "    bi_time = bi_encoder_time * (count / len(documents))\n", "    cross_time = cross_only_time * (count / len(documents))\n", "    print(f\"{count:8d} | {bi_time:.4f}s     | {cross_time:.4f}s      | {cross_time/bi_time:.1f}x\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion: The Value of Reranking\n", "\n", "We've demonstrated why two-stage retrieval with reranking is valuable:\n", "\n", "1. **Efficiency**: Bi-encoders allow for fast initial retrieval across large document collections\n", "2. **Accuracy**: Cross-encoders provide more accurate relevance assessment for the candidate set\n", "3. **Scalability**: The two-stage approach scales well to large document collections\n", "\n", "### Key Takeaways:\n", "\n", "- **Bi-encoders** are fast but less accurate because they process queries and documents independently\n", "- **Cross-encoders** are more accurate but too slow for initial retrieval on large collections\n", "- **Reranking** gives us the best of both worlds by limiting the expensive cross-encoder processing to a small candidate set\n", "\n", "This pattern is widely used in modern search systems, recommender systems, and many other applications where balancing efficiency and accuracy is crucial."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}