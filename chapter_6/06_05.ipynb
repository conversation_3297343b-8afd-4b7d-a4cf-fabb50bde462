{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Building a Complete Retrieval Pipeline\n", "\n", "This notebook demonstrates how to build a complete retrieval pipeline that combines multiple techniques to improve search quality. We'll start with basic retrieval methods and progressively add more sophisticated components.\n", "\n", "By the end, you'll understand:\n", "- How different retrieval methods work (BM25, vector search)\n", "- How to combine methods for hybrid retrieval\n", "- How to enhance results with reranking\n", "- How to build a configurable pipeline that adapts to different needs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports\n", "\n", "Let's start by importing the necessary libraries:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "from typing import Dict, List\n", "\n", "# LlamaIndex imports\n", "from llama_index.core.schema import Document, NodeWithScore, QueryBundle, TextNode\n", "from llama_index.core import VectorStoreIndex\n", "from llama_index.core.retrievers import BaseRetriever\n", "from llama_index.retrievers.bm25 import BM25Retriever\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "\n", "# Hugging Face imports\n", "from sentence_transformers import SentenceTransformer, CrossEncoder, util"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Sample Documents\n", "\n", "Let's create a set of sample documents to work with throughout this notebook:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 10 sample documents\n"]}], "source": ["texts = [\n", "    \"Python is a high-level programming language known for its readability.\",\n", "    \"Machine learning is a subset of AI that enables systems to learn from data.\",\n", "    \"Neural networks are computing systems inspired by biological neural networks.\",\n", "    \"Deep learning uses neural networks with many layers to extract features from data.\",\n", "    \"Natural language processing helps computers understand human language.\",\n", "    \"Python libraries like PyTorch and TensorFlow are used for deep learning.\",\n", "    \"BM25 is a bag-of-words retrieval function used in information retrieval.\",\n", "    \"Vector search finds documents by measuring similarity in embedding space.\",\n", "    \"Reranking refines initial search results with a more complex model.\",\n", "    \"Retrieval pipelines combine multiple techniques for better search results.\"\n", "]\n", "\n", "# Convert to Document objects\n", "documents = [Document(text=text, id_=f\"doc_{i}\") for i, text in enumerate(texts)]\n", "\n", "# Convert to Nodes for retrieval\n", "nodes = [TextNode(text=doc.text, id_=doc.id_) for doc in documents]\n", "\n", "print(f\"Created {len(documents)} sample documents\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a Testing Function\n", "\n", "Let's create a helper function to test our retrievers consistently:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def test_retriever(retriever, query_text, name=\"Retriever\"):\n", "    \"\"\"Test a retriever with a query and print results.\"\"\"\n", "    print(f\"\\n=== Testing {name} ===\")\n", "    start_time = time.time()\n", "    results = retriever.retrieve(QueryBundle(query_text))\n", "    end_time = time.time()\n", "\n", "    print(f\"Query: '{query_text}'\")\n", "    print(f\"Retrieved {len(results)} documents in {(end_time - start_time):.4f} seconds\")\n", "\n", "    for i, node in enumerate(results[:3], 1):  # Show top 3 for brevity\n", "        print(f\"{i}. Score: {node.score:.4f} - {node.node.get_content()}\")\n", "\n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Retrieval: BM25\n", "\n", "Let's start with a traditional lexical search method: BM25. This algorithm ranks documents based on term frequency and inverse document frequency, essentially looking for keyword matches.\n", "\n", "BM25 is great for finding documents containing specific terms in the query, but it doesn't understand synonyms or semantic meaning."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Testing BM25 Retriever ===\n", "Query: 'How is Python used in machine learning?'\n", "Retrieved 5 documents in 0.0012 seconds\n", "1. Score: 1.5418 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 1.4118 - Machine learning is a subset of AI that enables systems to learn from data.\n", "3. Score: 0.8041 - Deep learning uses neural networks with many layers to extract features from data.\n"]}], "source": ["# Create a BM25 retriever\n", "bm25_retriever = BM25Retriever.from_defaults(nodes=nodes, similarity_top_k=5)\n", "\n", "# Test the BM25 retriever\n", "query = \"How is Python used in machine learning?\"\n", "bm25_results = test_retriever(bm25_retriever, query, \"BM25 Retriever\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Retrieval: Vector Search\n", "\n", "Now let's try semantic search using vector embeddings. This method converts both the query and documents into vector representations and finds the most similar documents based on vector similarity.\n", "\n", "Vector search is better at understanding semantic meaning, even when exact keywords aren't present."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Testing Vector Retriever ===\n", "Query: 'How is Python used in machine learning?'\n", "Retrieved 5 documents in 0.0143 seconds\n", "1. Score: 0.6273 - Python is a high-level programming language known for its readability.\n", "2. Score: 0.6004 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "3. Score: 0.5428 - Machine learning is a subset of AI that enables systems to learn from data.\n"]}], "source": ["# Create the embedding model\n", "model_name = \"sentence-transformers/all-MiniLM-L6-v2\"\n", "embed_model = HuggingFaceEmbedding(model_name=model_name)\n", "\n", "# Create the bi-encoder to generate embeddings\n", "bi_encoder = SentenceTransformer(model_name, device=\"cpu\")\n", "\n", "# Generate embeddings for all nodes\n", "for node in nodes:\n", "    node.embedding = bi_encoder.encode(node.get_content())\n", "\n", "# Create vector index and retriever\n", "vector_index = VectorStoreIndex(nodes=nodes, embed_model=embed_model)\n", "vector_retriever = vector_index.as_retriever(similarity_top_k=5)\n", "\n", "# Test the vector retriever\n", "vector_results = test_retriever(vector_retriever, query, \"Vector Retriever\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing the Results So Far\n", "\n", "Notice the differences between BM25 and vector search results:\n", "\n", "- **BM25** ranks \"Machine learning\" document highest because it directly contains the keyword, followed by the Python libraries document.\n", "- **Vector Search** ranks the general \"Python\" document highest due to semantic similarity, then the libraries document.\n", "\n", "Each method has strengths and weaknesses:\n", "- BM25 is precise with keywords but misses semantic relationships\n", "- Vector search understands semantics but might miss exact term matches\n", "\n", "What if we could combine their strengths?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hybrid Retrieval: Combining BM25 and Vector Search\n", "\n", "Now let's implement a hybrid approach that combines both methods. This weighted fusion retriever will:\n", "1. Get results from both BM25 and vector retrievers\n", "2. Assign weights to each method (e.g., 70% vector, 30% BM25)\n", "3. <PERSON><PERSON><PERSON> and rerank the results based on the weighted scores"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Testing Hybrid Retriever ===\n", "Query: 'How is Python used in machine learning?'\n", "Retrieved 6 documents in 0.0154 seconds\n", "1. Score: 0.8828 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.8035 - Machine learning is a subset of AI that enables systems to learn from data.\n", "3. Score: 0.6208 - Python is a high-level programming language known for its readability.\n"]}], "source": ["class WeightedFusionRetriever(BaseRetriever):\n", "    \"\"\"Combines results from multiple retrievers with weights.\"\"\"\n", "\n", "    def __init__(self, retrievers: Dict[str, BaseRetriever], weights: Dict[str, float]):\n", "        self.retrievers = retrievers\n", "        self.weights = weights\n", "        super().__init__()\n", "\n", "    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:\n", "        all_results = {}\n", "\n", "        # Get results from each retriever\n", "        for name, retriever in self.retrievers.items():\n", "            results = retriever.retrieve(query_bundle)\n", "            weight = self.weights.get(name, 1.0)\n", "\n", "            # Combine results with weighting\n", "            for node in results:\n", "                node_id = node.node.node_id\n", "                weighted_score = node.score * weight\n", "\n", "                if node_id not in all_results:\n", "                    all_results[node_id] = {\"node\": node.node, \"scores\": {}}\n", "                all_results[node_id][\"scores\"][name] = weighted_score\n", "\n", "        # Create final results with combined scores\n", "        final_results = [\n", "            NodeWithScore(node=data[\"node\"], score=sum(data[\"scores\"].values()))\n", "            for node_id, data in all_results.items()\n", "        ]\n", "\n", "        return sorted(final_results, key=lambda x: x.score, reverse=True)\n", "\n", "\n", "# Create hybrid retriever\n", "hybrid_retriever = WeightedFusionRetriever(\n", "    retrievers={\"vector\": vector_retriever, \"bm25\": bm25_retriever},\n", "    weights={\"vector\": 0.7, \"bm25\": 0.3}\n", ")\n", "\n", "# Test the hybrid retriever\n", "hybrid_results = test_retriever(hybrid_retriever, query, \"Hybrid Retriever\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhancing Results with Cross-Encoder Reranking\n", "\n", "Our hybrid retriever improves results by combining methods, but it still relies on the initial retrieval scores.\n", "\n", "Let's take it to the next level with cross-encoder reranking:\n", "\n", "**Bi-Encoders vs Cross-Encoders:**\n", "- **Bi-Encoders** (like our vector retriever) encode queries and documents separately\n", "- **Cross-Encoders** process query-document pairs together, capturing complex interactions\n", "\n", "While cross-encoders are more accurate, they're too computationally expensive to use on an entire collection. The solution? Use them only to rerank a smaller set of candidates from our initial retrieval."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded CrossEncoder model: cross-encoder/ms-marco-MiniLM-L-6-v2\n", "\n", "=== Testing Reranked Retriever ===\n", "Query: 'How is Python used in machine learning?'\n", "Retrieved 5 documents in 0.0512 seconds\n", "1. Score: 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.4848 - Python is a high-level programming language known for its readability.\n", "3. Score: -2.4892 - Machine learning is a subset of AI that enables systems to learn from data.\n"]}], "source": ["class RerankedRetriever(BaseRetriever):\n", "    \"\"\"Two-stage retriever: initial retrieval + cross-encoder reranking.\"\"\"\n", "\n", "    def __init__(self, base_retriever, model_name=\"cross-encoder/ms-marco-MiniLM-L-6-v2\",\n", "                 fetch_k=10, top_k=5):\n", "        self.base_retriever = base_retriever\n", "        self.reranker = CrossEncoder(model_name, device=\"cpu\")\n", "        self.fetch_k = fetch_k\n", "        self.top_k = top_k\n", "        super().__init__()\n", "        print(f\"Loaded CrossEncoder model: {model_name}\")\n", "\n", "    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:\n", "        # Stage 1: Get initial candidates from base retriever\n", "        base_nodes = self.base_retriever.retrieve(query_bundle)[:self.fetch_k]\n", "\n", "        # Early return if no results\n", "        if not base_nodes:\n", "            return []\n", "\n", "        # Stage 2: Rerank candidates with cross-encoder\n", "        query = query_bundle.query_str\n", "        node_texts = [node.node.get_content() for node in base_nodes]\n", "        rerank_scores = self.reranker.predict(\n", "            [(query, text) for text in node_texts])\n", "\n", "        # Create reranked nodes\n", "        reranked_nodes = [\n", "            NodeWithScore(node=node.node, score=float(score))\n", "            for node, score in zip(base_nodes, rerank_scores)\n", "        ]\n", "\n", "        # Sort and filter\n", "        reranked_nodes.sort(key=lambda x: x.score, reverse=True)\n", "        return reranked_nodes[:self.top_k] if self.top_k else reranked_nodes\n", "\n", "\n", "# Create reranked retriever\n", "reranked_retriever = RerankedRetriever(\n", "    base_retriever=hybrid_retriever,\n", "    fetch_k=10,\n", "    top_k=5\n", ")\n", "\n", "# Test the reranked retriever\n", "reranked_results = test_retriever(reranked_retriever, query, \"Reranked Retriever\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Observations about <PERSON><PERSON>ing\n", "\n", "Notice how the cross-encoder completely changed the ranking:\n", "\n", "1. The document about \"Python libraries for deep learning\" moved to the top position with a much higher score (4.97)\n", "2. The document about \"Machine learning\" moved from first to third\n", "3. The scores now range from positive to negative, showing the cross-encoder's more nuanced relevance assessment\n", "\n", "These changes make intuitive sense. For the query \"How is Python used in machine learning?\", the most relevant document is indeed the one that directly mentions Python libraries used for deep learning.\n", "\n", "However, reranking did add some latency vs hybrid retrieval. This is the efficiency-effectiveness tradeoff that makes the two-stage approach valuable."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Building a Complete Configurable Pipeline\n", "\n", "Now that we've explored the individual components, let's build a complete, configurable retrieval pipeline that can adapt to different needs:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Building pipeline: use_bm25, use_vector, use_hybrid, use_reranking\n", "Loaded CrossEncoder model: cross-encoder/ms-marco-MiniLM-L-6-v2\n", "\n", "=== Results (0.0483s) ===\n", "1. 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. 0.4848 - Python is a high-level programming language known for its readability.\n", "3. -2.4892 - Machine learning is a subset of AI that enables systems to learn from data.\n"]}], "source": ["class RetrievalPipeline:\n", "    \"\"\"Configurable retrieval pipeline combining multiple techniques.\"\"\"\n", "\n", "    def __init__(self, use_bm25=True, use_vector=True, use_hybrid=True, use_reranking=True,\n", "                 vector_weight=0.7, bm25_weight=0.3, top_k=5, rerank_top_k=10):\n", "        self.config = {\n", "            \"use_bm25\": use_bm25, \"use_vector\": use_vector,\n", "            \"use_hybrid\": use_hybrid, \"use_reranking\": use_reranking,\n", "            \"vector_weight\": vector_weight, \"bm25_weight\": bm25_weight,\n", "            \"top_k\": top_k, \"rerank_top_k\": rerank_top_k\n", "        }\n", "        self.pipeline = None\n", "\n", "    def build(self, nodes):\n", "        \"\"\"Build the pipeline based on configuration.\"\"\"\n", "        enabled = [k for k, v in self.config.items()\n", "                   if v and k.startswith('use_')]\n", "        print(f\"\\nBuilding pipeline: {', '.join(enabled)}\")\n", "\n", "        # Set up retrievers\n", "        retrievers = {}\n", "        if self.config[\"use_bm25\"]:\n", "            retrievers[\"bm25\"] = BM25Retriever.from_defaults(\n", "                nodes=nodes, similarity_top_k=self.config[\"top_k\"]\n", "            )\n", "\n", "        if self.config[\"use_vector\"]:\n", "            embed_model = HuggingFaceEmbedding(\n", "                model_name=\"sentence-transformers/all-MiniLM-L6-v2\"\n", "            )\n", "            vector_index = VectorStoreIndex(\n", "                nodes=nodes, embed_model=embed_model)\n", "            retrievers[\"vector\"] = vector_index.as_retriever(\n", "                similarity_top_k=self.config[\"top_k\"]\n", "            )\n", "\n", "        # Select base retriever\n", "        if self.config[\"use_hybrid\"] and len(retrievers) > 1:\n", "            weights = {\n", "                \"vector\": self.config[\"vector_weight\"],\n", "                \"bm25\": self.config[\"bm25_weight\"]\n", "            }\n", "            base_retriever = WeightedFusionRetriever(\n", "                retrievers=retrievers, weights=weights)\n", "        else:\n", "            retriever_name = next(iter(retrievers.keys()))\n", "            base_retriever = retrievers[retriever_name]\n", "\n", "        # Add reranking if enabled\n", "        if self.config[\"use_reranking\"]:\n", "            self.pipeline = RerankedRetriever(\n", "                base_retriever=base_retriever,\n", "                fetch_k=self.config[\"rerank_top_k\"],\n", "                top_k=self.config[\"top_k\"]\n", "            )\n", "        else:\n", "            self.pipeline = base_retriever\n", "        return self\n", "\n", "    def retrieve(self, query, verbose=True):\n", "        \"\"\"Execute the retrieval pipeline on a query.\"\"\"\n", "        if self.pipeline is None:\n", "            raise ValueError(\"Pipeline not built. Call build() first.\")\n", "\n", "        if isinstance(query, str):\n", "            query = QueryBundle(query)\n", "\n", "        start_time = time.time()\n", "        results = self.pipeline.retrieve(query)\n", "        elapsed = time.time() - start_time\n", "\n", "        if verbose:\n", "            print(f\"\\n=== Results ({elapsed:.4f}s) ===\")\n", "            for i, node in enumerate(results[:3], 1):\n", "                print(f\"{i}. {node.score:.4f} - {node.node.get_content()}\")\n", "\n", "        return results, elapsed\n", "\n", "\n", "# Try out the pipeline with the full configuration\n", "full_pipeline = RetrievalPipeline().build(nodes)\n", "results, elapsed = full_pipeline.retrieve(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Comparing Different Pipeline Configurations\n", "\n", "Now let's compare different configurations of our pipeline to see how each component affects performance and results. This will help us understand the trade-offs between efficiency and effectiveness."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Pipeline Configuration Comparison ===\n", "Query: 'How is Python used in machine learning?'\n", "\n", "Building pipeline: use_bm25\n", "\n", "BM25 Only (0.0009s):\n", "1. Score: 1.5418 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 1.4118 - Machine learning is a subset of AI that enables systems to learn from data.\n", "\n", "Building pipeline: use_vector\n", "\n", "Vector Only (0.0132s):\n", "1. Score: 0.6273 - Python is a high-level programming language known for its readability.\n", "2. Score: 0.6004 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "\n", "Building pipeline: use_bm25, use_vector, use_hybrid\n", "\n", "Hybrid (0.0188s):\n", "1. Score: 0.8828 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.8035 - Machine learning is a subset of AI that enables systems to learn from data.\n", "\n", "Building pipeline: use_bm25, use_vector, use_hybrid, use_reranking\n", "Loaded CrossEncoder model: cross-encoder/ms-marco-MiniLM-L-6-v2\n", "\n", "Full Pipeline (0.0717s):\n", "1. Score: 5.1547 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "2. Score: 0.4848 - Python is a high-level programming language known for its readability.\n", "\n", "=== Performance Summary ===\n", "BM25 Only: 0.0009s\n", "Vector Only: 0.0132s (15.2x slower than BM25)\n", "Hybrid: 0.0188s (21.5x slower than BM25)\n", "Full Pipeline: 0.0717s (82.2x slower than BM25)\n"]}], "source": ["def test_configurations(nodes, query):\n", "    \"\"\"Compare different pipeline configurations on the same query.\"\"\"\n", "    configurations = {\n", "        \"BM25 Only\": {\"use_bm25\": True, \"use_vector\": False, \"use_hybrid\": False, \"use_reranking\": False},\n", "        \"Vector Only\": {\"use_bm25\": False, \"use_vector\": True, \"use_hybrid\": False, \"use_reranking\": False},\n", "        \"Hybrid\": {\"use_bm25\": True, \"use_vector\": True, \"use_hybrid\": True, \"use_reranking\": False},\n", "        \"Full Pipeline\": {\"use_bm25\": True, \"use_vector\": True, \"use_hybrid\": True, \"use_reranking\": True}\n", "    }\n", "\n", "    print(f\"\\n=== Pipeline Configuration Comparison ===\")\n", "    print(f\"Query: '{query}'\")\n", "    \n", "    results = {}\n", "\n", "    for name, config in configurations.items():\n", "        pipeline = RetrievalPipeline(**config).build(nodes)\n", "        retrieval_results, elapsed = pipeline.retrieve(query, verbose=False)\n", "        results[name] = {\"time\": elapsed, \"results\": retrieval_results}\n", "        \n", "        print(f\"\\n{name} ({elapsed:.4f}s):\")\n", "        for i, node in enumerate(retrieval_results[:2], 1):\n", "            print(f\"{i}. Score: {node.score:.4f} - {node.node.get_content()}\")\n", "    \n", "    # Print performance summary\n", "    print(\"\\n=== Performance Summary ===\")\n", "    baseline_time = results[\"BM25 Only\"][\"time\"]\n", "    for name, result in results.items():\n", "        relative = result[\"time\"] / baseline_time\n", "        print(f\"{name}: {result['time']:.4f}s{' (' + f'{relative:.1f}x slower than BM25)' if name != 'BM25 Only' else ''}\")\n", "    \n", "    return results\n", "\n", "# Run the comparison\n", "comparison_results = test_configurations(nodes, query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Trying a Different Query\n", "\n", "Let's see how our pipeline performs on a different query. This will help us understand how the retrieval methods adapt to different information needs."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Pipeline Configuration Comparison ===\n", "Query: 'What is the difference between deep learning and neural networks?'\n", "\n", "Building pipeline: use_bm25\n", "\n", "BM25 Only (0.0009s):\n", "1. Score: 1.9626 - Deep learning uses neural networks with many layers to extract features from data.\n", "2. Score: 1.7196 - Neural networks are computing systems inspired by biological neural networks.\n", "\n", "Building pipeline: use_vector\n", "\n", "Vector Only (0.0148s):\n", "1. Score: 0.6050 - Deep learning uses neural networks with many layers to extract features from data.\n", "2. Score: 0.5264 - Python libraries like PyTorch and TensorFlow are used for deep learning.\n", "\n", "Building pipeline: use_bm25, use_vector, use_hybrid\n", "\n", "Hybrid (0.0156s):\n", "1. Score: 1.0123 - Deep learning uses neural networks with many layers to extract features from data.\n", "2. Score: 0.8801 - Neural networks are computing systems inspired by biological neural networks.\n", "\n", "Building pipeline: use_bm25, use_vector, use_hybrid, use_reranking\n", "Loaded CrossEncoder model: cross-encoder/ms-marco-MiniLM-L-6-v2\n", "\n", "Full Pipeline (0.0510s):\n", "1. Score: 6.7731 - Deep learning uses neural networks with many layers to extract features from data.\n", "2. Score: 1.8872 - Neural networks are computing systems inspired by biological neural networks.\n", "\n", "=== Performance Summary ===\n", "BM25 Only: 0.0009s\n", "Vector Only: 0.0148s (17.0x slower than BM25)\n", "Hybrid: 0.0156s (18.0x slower than BM25)\n", "Full Pipeline: 0.0510s (58.6x slower than BM25)\n", "\n", "=== Comparison of Top Results ===\n", "For this query, all methods retrieved the same top 2 documents, but with different rankings and scores.\n", "The reranker gave significantly higher scores to both documents, showing its confidence in their relevance.\n"]}], "source": ["new_query = \"What is the difference between deep learning and neural networks?\"\n", "new_comparison = test_configurations(nodes, new_query)\n", "\n", "# Analyze the results\n", "print(\"\\n=== Comparison of Top Results ===\")\n", "print(\"For this query, all methods retrieved the same top 2 documents, but with different rankings and scores.\")\n", "print(\"The reranker gave significantly higher scores to both documents, showing its confidence in their relevance.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Understanding the Latency-Quality Tradeoff\n", "\n", "Let's analyze how our pipeline would scale with larger document collections. This simulation will help us understand the latency-quality tradeoff in a real-world scenario."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Latency Scaling Analysis ===\n", "Estimating retrieval times for different collection sizes:\n", "\n", "Document Count | BM25 | Vector | Hybrid | Full Pipeline\n", "--------------------------------------------------------------------------------------------------------------\n", "10 (current) | 0.0009s | 0.0132s | 0.0188s | 0.0717s\n", "100 | 0.0087s | 0.1322s | 0.1876s | 0.2405s\n", "1,000 | 0.0872s | 1.3222s | 1.8763s | 1.9292s\n", "10,000 | 0.8717s | 13.2222s | 18.7628s | 18.8157s\n", "100,000 | 8.7166s | 132.2222s | 187.6283s | 187.6812s\n", "\n", "Note: This is a simplified linear estimate. In practice, retrieval systems use\n", "optimization techniques like indexing and approximate nearest neighbor search\n", "to achieve sub-linear scaling.\n"]}], "source": ["def estimate_scaling(base_times, doc_counts):\n", "    \"\"\"Estimate latency scaling with document collection size.\"\"\"\n", "    print(\"\\n=== Latency Scaling Analysis ===\")\n", "    print(\"Estimating retrieval times for different collection sizes:\\n\")\n", "    \n", "    # Print header\n", "    methods = list(base_times.keys())\n", "    header = \"Document Count | \" + \" | \".join(methods)\n", "    print(header)\n", "    print(\"-\" * len(header) * 2)\n", "    \n", "    current_count = 10  # Our current document count\n", "    \n", "    # For each document count\n", "    for i, count in enumerate(doc_counts):\n", "        row = []\n", "        # Format the document count\n", "        if i == 0:\n", "            row.append(f\"{count} (current)\")\n", "        else:\n", "            row.append(f\"{count:,}\")\n", "            \n", "        # For each method\n", "        for method in methods:\n", "            # For the full pipeline, only the initial retrieval scales with document count\n", "            # The reranking time is constant based on fetch_k\n", "            if method == \"Full Pipeline\" and i > 0:\n", "                # Estimate the base retrieval time (without reranking)\n", "                base_retrieval_time = base_times[\"Hybrid\"] * (count / current_count)\n", "                # Add the constant reranking time\n", "                reranking_time = base_times[\"Full Pipeline\"] - base_times[\"Hybrid\"]\n", "                time_estimate = base_retrieval_time + reranking_time\n", "            else:\n", "                # For other methods, simply scale linearly\n", "                time_estimate = base_times[method] * (count / current_count)\n", "                \n", "            row.append(f\"{time_estimate:.4f}s\")\n", "            \n", "        print(\" | \".join(row).lju<PERSON>(16))\n", "    \n", "    print(\"\\nNote: This is a simplified linear estimate. In practice, retrieval systems use\")\n", "    print(\"optimization techniques like indexing and approximate nearest neighbor search\")\n", "    print(\"to achieve sub-linear scaling.\")\n", "\n", "# Get the base times from our previous comparison\n", "base_times = {\n", "    \"BM25\": comparison_results[\"BM25 Only\"][\"time\"],\n", "    \"Vector\": comparison_results[\"Vector Only\"][\"time\"],\n", "    \"Hybrid\": comparison_results[\"Hybrid\"][\"time\"],\n", "    \"Full Pipeline\": comparison_results[\"Full Pipeline\"][\"time\"]\n", "}\n", "\n", "# Document counts to estimate for\n", "doc_counts = [10, 100, 1000, 10000, 100000]\n", "\n", "# Run the scaling analysis\n", "estimate_scaling(base_times, doc_counts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Practical Considerations for Production\n", "\n", "When implementing a retrieval pipeline in production, consider these factors:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Performance Optimization\n", "\n", "1. **Pre-compute and cache embeddings**: Generate embeddings at indexing time, not query time\n", "2. **Use approximate nearest neighbor (ANN) search**: For vector search at scale (e.g., FAISS, <PERSON><PERSON>, ScaNN)\n", "3. **Optimize fetch_k and top_k**: More candidates improve quality but increase latency\n", "4. **Batch processing**: Process multiple queries in parallel when possible\n", "\n", "### Model Selection\n", "\n", "1. **Domain-specific embeddings**: Choose models trained on data similar to your domain\n", "2. **Model size tradeoffs**: Larger models are more accurate but slower\n", "3. **Distilled models**: Consider knowledge-distilled models for better speed/quality tradeoff\n", "4. **Cross-encoder selection**: Test different cross-encoder models for your specific task\n", "\n", "### Resource Allocation\n", "\n", "1. **BM25 is CPU-bound**: Allocate sufficient memory for index, but CPU is the primary constraint\n", "2. **Vector search benefits from GPU**: Especially for larger models and collections\n", "3. **Cross-encoders are resource-intensive**: Use judiciously on a carefully filtered candidate set\n", "\n", "### Adaptive Configuration\n", "\n", "1. **Query-dependent weights**: Adjust fusion weights based on query type\n", "2. **Different pipelines for different use cases**: Simple/fast pipeline for suggestions, complex/accurate for main search\n", "3. **A/B testing**: Continuously test configuration changes with real users"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 15. Conclusion: The Power of Retrieval Pipelines\n", "\n", "In this notebook, we've built a complete retrieval pipeline from scratch, exploring:\n", "\n", "1. **Traditional lexical search (BM25)**: Fast and precise for keyword matching\n", "2. **Semantic search (vector embeddings)**: Better at understanding meaning beyond keywords\n", "3. **Hybrid retrieval**: Combining methods to leverage their complementary strengths\n", "4. **Cross-encoder reranking**: Fine-tuning results with more sophisticated relevance assessment\n", "5. **Configurable pipeline**: Adapting the approach to different needs and constraints\n", "\n", "The key insight is that there's no one-size-fits-all approach to retrieval. By combining multiple techniques in a carefully designed pipeline, we can achieve a balance of efficiency and effectiveness that wouldn't be possible with any single method.\n", "\n", "This approach is the foundation of modern search systems, RAG applications, recommender systems, and many other information retrieval tasks where finding the most relevant content quickly is crucial."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}