# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.txt -o requirements.lock
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.16
    # via
    #   llama-index-core
    #   llama-index-readers-web
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   jupyter-server
    #   starlette
    #   watchfiles
argon2-cffi==23.1.0
    # via jupyter-server
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
arrow==1.3.0
    # via isoduration
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
asttokens==3.0.0
    # via stack-data
async-lru==2.0.5
    # via jupyterlab
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   outcome
    #   referencing
    #   trio
babel==2.17.0
    # via jupyterlab-server
backoff==2.2.1
    # via posthog
banks==2.1.1
    # via llama-index-core
bcrypt==4.3.0
    # via chromadb
beautifulsoup4==4.13.4
    # via
    #   feedfinder2
    #   llama-index-readers-file
    #   llama-index-readers-web
    #   nbconvert
    #   newspaper3k
bleach==6.2.0
    # via nbconvert
bm25s==0.2.10
    # via llama-index-retrievers-bm25
build==1.2.2.post1
    # via chromadb
cachetools==5.5.2
    # via google-auth
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
    #   selenium
cffi==1.17.1
    # via argon2-cffi-bindings
charset-normalizer==3.4.1
    # via requests
chroma-hnswlib==0.7.6
    # via chromadb
chromadb==0.6.3
    # via
    #   -r requirements.txt
    #   llama-index-vector-stores-chroma
chromedriver-autoinstaller==0.6.4
    # via llama-index-readers-web
click==8.1.8
    # via
    #   nltk
    #   typer
    #   uvicorn
colorama==0.4.6
    # via griffe
coloredlogs==15.0.1
    # via onnxruntime
comm==0.2.2
    # via
    #   ipykernel
    #   ipywidgets
contourpy==1.3.2
    # via matplotlib
cssselect==1.3.0
    # via newspaper3k
cycler==0.12.1
    # via matplotlib
dataclasses-json==0.6.7
    # via llama-index-core
debugpy==1.8.14
    # via ipykernel
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via nbconvert
deprecated==1.2.18
    # via
    #   banks
    #   llama-index-core
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-semantic-conventions
dirtyjson==1.0.8
    # via llama-index-core
distro==1.9.0
    # via posthog
docx2txt==0.9
    # via -r requirements.txt
durationpy==0.9
    # via kubernetes
executing==2.2.0
    # via stack-data
fastapi==0.115.12
    # via chromadb
fastjsonschema==2.21.1
    # via nbformat
feedfinder2==0.0.4
    # via newspaper3k
feedparser==6.0.11
    # via newspaper3k
filelock==3.18.0
    # via
    #   huggingface-hub
    #   tldextract
filetype==1.2.0
    # via llama-index-core
flatbuffers==25.2.10
    # via onnxruntime
fonttools==4.57.0
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   huggingface-hub
    #   llama-index-core
google-auth==2.39.0
    # via kubernetes
googleapis-common-protos==1.70.0
    # via opentelemetry-exporter-otlp-proto-grpc
greenlet==3.2.0
    # via
    #   playwright
    #   sqlalchemy
griffe==1.7.2
    # via banks
grpcio==1.71.0
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
    #   wsproto
html2text==2024.2.26
    # via llama-index-readers-web
httpcore==1.0.8
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   chromadb
    #   jupyterlab
    #   llama-index-core
huggingface-hub==0.30.2
    # via tokenizers
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   jsonschema
    #   requests
    #   tldextract
    #   trio
    #   yarl
importlib-metadata==8.6.1
    # via opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
ipykernel==6.29.5
    # via
    #   -r requirements.txt
    #   jupyter
    #   jupyter-console
    #   jupyterlab
ipython==9.1.0
    # via
    #   ipykernel
    #   ipywidgets
    #   jupyter-console
ipython-pygments-lexers==1.1.1
    # via ipython
ipywidgets==8.1.5
    # via
    #   -r requirements.txt
    #   jupyter
isoduration==20.11.0
    # via jsonschema
jedi==0.19.2
    # via ipython
jieba3k==0.35.1
    # via newspaper3k
jinja2==3.1.6
    # via
    #   banks
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
joblib==1.4.2
    # via
    #   nltk
    #   scikit-learn
json5==0.12.0
    # via jupyterlab-server
jsonpointer==3.0.0
    # via jsonschema
jsonschema==4.23.0
    # via
    #   jupyter-events
    #   jupyterlab-server
    #   nbformat
jsonschema-specifications==2024.10.1
    # via jsonschema
jupyter==1.1.1
    # via -r requirements.txt
jupyter-client==8.6.3
    # via
    #   ipykernel
    #   jupyter-console
    #   jupyter-server
    #   nbclient
jupyter-console==6.6.3
    # via jupyter
jupyter-core==5.7.2
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
    #   jupyterlab
    #   nbclient
    #   nbconvert
    #   nbformat
jupyter-events==0.12.0
    # via jupyter-server
jupyter-lsp==2.2.5
    # via jupyterlab
jupyter-server==2.15.0
    # via
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   notebook
    #   notebook-shim
jupyter-server-terminals==0.5.3
    # via jupyter-server
jupyterlab==4.4.0
    # via
    #   jupyter
    #   notebook
jupyterlab-pygments==0.3.0
    # via nbconvert
jupyterlab-server==2.27.3
    # via
    #   jupyterlab
    #   notebook
jupyterlab-widgets==3.0.14
    # via ipywidgets
kiwisolver==1.4.8
    # via matplotlib
kubernetes==32.0.1
    # via chromadb
llama-index-core==0.12.27
    # via
    #   -r requirements.txt
    #   llama-index-readers-database
    #   llama-index-readers-file
    #   llama-index-readers-json
    #   llama-index-readers-web
    #   llama-index-retrievers-bm25
    #   llama-index-vector-stores-chroma
llama-index-readers-database==0.3.0
    # via -r requirements.txt
llama-index-readers-file==0.4.7
    # via -r requirements.txt
llama-index-readers-json==0.3.0
    # via -r requirements.txt
llama-index-readers-web==0.3.8
    # via -r requirements.txt
llama-index-retrievers-bm25==0.5.2
    # via -r requirements.txt
llama-index-vector-stores-chroma==0.4.1
    # via -r requirements.txt
lxml==5.3.2
    # via newspaper3k
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   nbconvert
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.1
    # via
    #   -r requirements.txt
    #   seaborn
matplotlib-inline==0.1.7
    # via
    #   ipykernel
    #   ipython
mdurl==0.1.2
    # via markdown-it-py
mistune==3.1.3
    # via nbconvert
mmh3==5.1.0
    # via chromadb
monotonic==1.6
    # via posthog
mpmath==1.3.0
    # via sympy
multidict==6.4.3
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via typing-inspect
nbclient==0.10.2
    # via nbconvert
nbconvert==7.16.6
    # via
    #   jupyter
    #   jupyter-server
nbformat==5.10.4
    # via
    #   jupyter-server
    #   nbclient
    #   nbconvert
nest-asyncio==1.6.0
    # via
    #   ipykernel
    #   llama-index-core
networkx==3.4.2
    # via llama-index-core
newspaper3k==0.2.8
    # via llama-index-readers-web
nltk==3.9.1
    # via
    #   llama-index-core
    #   newspaper3k
notebook==7.4.0
    # via jupyter
notebook-shim==0.2.4
    # via
    #   jupyterlab
    #   notebook
numpy==2.2.4
    # via
    #   bm25s
    #   chroma-hnswlib
    #   chromadb
    #   contourpy
    #   llama-index-core
    #   matplotlib
    #   onnxruntime
    #   pandas
    #   scikit-learn
    #   scipy
    #   seaborn
oauthlib==3.2.2
    # via
    #   kubernetes
    #   requests-oauthlib
onnxruntime==1.21.0
    # via chromadb
opentelemetry-api==1.32.1
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.32.1
    # via opentelemetry-exporter-otlp-proto-grpc
opentelemetry-exporter-otlp-proto-grpc==1.32.1
    # via chromadb
opentelemetry-instrumentation==0.53b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.53b1
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.53b1
    # via chromadb
opentelemetry-proto==1.32.1
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk==1.32.1
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-semantic-conventions==0.53b1
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
opentelemetry-util-http==0.53b1
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.10.16
    # via chromadb
outcome==1.3.0.post0
    # via
    #   trio
    #   trio-websocket
overrides==7.7.0
    # via
    #   chromadb
    #   jupyter-server
packaging==24.2
    # via
    #   build
    #   chromedriver-autoinstaller
    #   huggingface-hub
    #   ipykernel
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   marshmallow
    #   matplotlib
    #   nbconvert
    #   onnxruntime
    #   opentelemetry-instrumentation
pandas==2.2.3
    # via
    #   -r requirements.txt
    #   llama-index-readers-file
    #   seaborn
pandocfilters==1.5.1
    # via nbconvert
parso==0.8.4
    # via jedi
pexpect==4.9.0
    # via ipython
pillow==11.2.1
    # via
    #   llama-index-core
    #   matplotlib
    #   newspaper3k
platformdirs==4.3.7
    # via
    #   banks
    #   jupyter-core
playwright==1.51.0
    # via llama-index-readers-web
posthog==3.24.2
    # via chromadb
prometheus-client==0.21.1
    # via jupyter-server
prompt-toolkit==3.0.51
    # via
    #   ipython
    #   jupyter-console
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
protobuf==5.29.4
    # via
    #   googleapis-common-protos
    #   onnxruntime
    #   opentelemetry-proto
psutil==7.0.0
    # via ipykernel
ptyprocess==0.7.0
    # via
    #   pexpect
    #   terminado
pure-eval==0.2.3
    # via stack-data
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.3
    # via
    #   banks
    #   chromadb
    #   fastapi
    #   llama-index-core
pydantic-core==2.33.1
    # via pydantic
pyee==12.1.1
    # via playwright
pygments==2.19.1
    # via
    #   ipython
    #   ipython-pygments-lexers
    #   jupyter-console
    #   nbconvert
    #   rich
pyparsing==3.2.3
    # via matplotlib
pypdf==5.4.0
    # via llama-index-readers-file
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via build
pysocks==1.7.1
    # via urllib3
pystemmer==*******
    # via llama-index-retrievers-bm25
python-dateutil==2.9.0.post0
    # via
    #   arrow
    #   jupyter-client
    #   kubernetes
    #   matplotlib
    #   newspaper3k
    #   pandas
    #   posthog
python-dotenv==1.1.0
    # via uvicorn
python-json-logger==3.3.0
    # via jupyter-events
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   chromadb
    #   huggingface-hub
    #   jupyter-events
    #   kubernetes
    #   llama-index-core
    #   newspaper3k
    #   uvicorn
pyzmq==26.4.0
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
    #   jupyter-events
regex==2024.11.6
    # via
    #   nltk
    #   tiktoken
requests==2.32.3
    # via
    #   feedfinder2
    #   huggingface-hub
    #   jupyterlab-server
    #   kubernetes
    #   llama-index-core
    #   llama-index-readers-web
    #   newspaper3k
    #   posthog
    #   requests-file
    #   requests-oauthlib
    #   spider-client
    #   tiktoken
    #   tldextract
requests-file==2.1.0
    # via tldextract
requests-oauthlib==2.0.0
    # via kubernetes
rfc3339-validator==0.1.4
    # via
    #   jsonschema
    #   jupyter-events
rfc3986-validator==0.1.1
    # via
    #   jsonschema
    #   jupyter-events
rich==14.0.0
    # via
    #   chromadb
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
scikit-learn==1.6.1
    # via -r requirements.txt
scipy==1.15.2
    # via
    #   bm25s
    #   scikit-learn
seaborn==0.13.2
    # via -r requirements.txt
selenium==4.31.0
    # via llama-index-readers-web
send2trash==1.8.3
    # via jupyter-server
setuptools==78.1.0
    # via jupyterlab
sgmllib3k==1.0.0
    # via feedparser
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   feedfinder2
    #   kubernetes
    #   posthog
    #   python-dateutil
    #   rfc3339-validator
sniffio==1.3.1
    # via
    #   anyio
    #   trio
sortedcontainers==2.4.0
    # via trio
soupsieve==2.6
    # via beautifulsoup4
spider-client==0.0.27
    # via llama-index-readers-web
sqlalchemy==2.0.40
    # via llama-index-core
stack-data==0.6.3
    # via ipython
starlette==0.46.2
    # via fastapi
striprtf==0.0.26
    # via llama-index-readers-file
sympy==1.13.3
    # via onnxruntime
tenacity==9.1.2
    # via
    #   chromadb
    #   llama-index-core
terminado==0.18.1
    # via
    #   jupyter-server
    #   jupyter-server-terminals
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.9.0
    # via llama-index-core
tinycss2==1.4.0
    # via bleach
tinysegmenter==0.3
    # via newspaper3k
tldextract==5.2.0
    # via newspaper3k
tokenizers==0.21.1
    # via chromadb
tornado==6.4.2
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   jupyterlab
    #   notebook
    #   terminado
tqdm==4.67.1
    # via
    #   -r requirements.txt
    #   chromadb
    #   huggingface-hub
    #   llama-index-core
    #   nltk
traitlets==5.14.3
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   ipywidgets
    #   jupyter-client
    #   jupyter-console
    #   jupyter-core
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   matplotlib-inline
    #   nbclient
    #   nbconvert
    #   nbformat
trio==0.29.0
    # via
    #   selenium
    #   trio-websocket
trio-websocket==0.12.2
    # via selenium
typer==0.15.2
    # via chromadb
types-python-dateutil==2.9.0.20241206
    # via arrow
typing-extensions==4.13.2
    # via
    #   anyio
    #   beautifulsoup4
    #   chromadb
    #   fastapi
    #   huggingface-hub
    #   llama-index-core
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
    #   pyee
    #   referencing
    #   selenium
    #   sqlalchemy
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via
    #   dataclasses-json
    #   llama-index-core
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
urllib3==2.4.0
    # via
    #   kubernetes
    #   llama-index-readers-web
    #   requests
    #   selenium
uvicorn==0.34.1
    # via chromadb
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.5
    # via uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
webcolors==24.11.1
    # via jsonschema
webencodings==0.5.1
    # via
    #   bleach
    #   tinycss2
websocket-client==1.8.0
    # via
    #   jupyter-server
    #   kubernetes
    #   selenium
websockets==15.0.1
    # via uvicorn
widgetsnbextension==4.0.14
    # via ipywidgets
wrapt==1.17.2
    # via
    #   deprecated
    #   llama-index-core
    #   opentelemetry-instrumentation
wsproto==1.2.0
    # via trio-websocket
yarl==1.19.0
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
