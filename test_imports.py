#!/usr/bin/env python3
"""
Test script to verify that PyTorch and transformers are properly installed
and can import AutoModelForCausalLM successfully.
"""

def test_imports():
    """Test importing required libraries."""
    try:
        import torch
        print(f"✓ PyTorch imported successfully (version: {torch.__version__})")
        
        from transformers import AutoModelForCausalLM, AutoTokenizer
        print("✓ AutoModelForCausalLM and AutoTokenizer imported successfully")
        
        # Test loading a small model
        model_name = "distilgpt2"
        print(f"\nTesting model loading with {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✓ Tokenizer loaded successfully")
        
        model = AutoModelForCausalLM.from_pretrained(model_name)
        print("✓ Model loaded successfully")
        print(f"✓ Model has {model.num_parameters():,} parameters")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing PyTorch and transformers installation...")
    print("=" * 50)
    
    success = test_imports()
    
    print("=" * 50)
    if success:
        print("✓ All tests passed! The environment is properly configured.")
    else:
        print("✗ Some tests failed. Please check the installation.")
